package org.jeecg.modules.camunda.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.engine.ActivityTypes;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.*;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.modules.camunda.service.WorkflowDefinitionService;
import org.jeecg.modules.camunda.vo.ProcessNodeVo;
import org.jeecg.modules.camunda.vo.WorkflowNodesVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 流程定义
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年08月21日 02:00
 */
@Service
@Slf4j
public class WorkflowDefinitionServiceImpl implements WorkflowDefinitionService {

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private HistoryService historyService;
    @Autowired
    private ActNodeServiceImpl actNodeService;

    /**
     * 通过流程定义id获取 bpmn.xml String
     *
     * @param procDefId process definition  id
     * @return xml string
     */
    @Override
    public Result<String> getDefinitionXmlString(String procDefId) {
        try {
            //通过流程定义key获取流程定义id
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(procDefId).singleResult();
            //                    .processDefinitionKey(ProcessConstants.APP_PRODUCT_PROCESS_ID)
            if (processDefinition == null) {
                log.warn("流程未定义或者已经不存在！获取参数{}", procDefId);
                return Result.error("流程未定义或者已经不存在！");
            }
            //            repositoryService.createProcessDefinitionQuery().
            InputStream inputStream = repositoryService.getProcessModel(processDefinition.getId());
            StringWriter writer = new StringWriter();
            IOUtils.copy(inputStream, writer, "utf-8");
            String xml = writer.toString();
            writer.close();
            inputStream.close();
            return Result.OK(xml);
        } catch (Exception e) {
            log.error("获取bpmn xml字符串失败！获取参数{}", procDefId);
            //            throw new StriveException("获取bpmn xml字符串失败");
            return Result.error("获取bpmn xml字符串失败！");
        }
    }

    /**
     * 获取流程定义节点
     *
     * @param proInsId
     * @return
     */
    @Override
    public WorkflowNodesVO getNodeIds(String proInsId) {
        HistoricProcessInstance hpi = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(proInsId).singleResult();
        if (hpi == null) {
            throw new JeecgBootException("流程没有启动！");
        }
        WorkflowNodesVO dto = new WorkflowNodesVO();
        List<HistoricActivityInstance> actList =
                historyService.createNativeHistoricActivityInstanceQuery()
                        .sql("SELECT * FROM `act_hi_actinst` where PROC_INST_ID_ = #{proInsId} order by SEQUENCE_COUNTER_ asc")
                        .parameter("proInsId", proInsId)
                        .list();
        BpmnModelInstance bpmnModel =
                repositoryService.getBpmnModelInstance(hpi.getProcessDefinitionId());
        if (CollectionUtils.isNotEmpty(actList)) {
            for (int i = 0; i < actList.size(); i++) {
                if (actList.get(i).getActivityId().endsWith("#multiInstanceBody")) {
                    continue;
                }
                dto.getActIns().add(actList.get(i));
                // 获取该活动的流程图节点
                FlowNode flowNode = bpmnModel.getModelElementById(
                        actList.get(i).getActivityId());
                // 找到该节点的流出箭头元素
                Collection<SequenceFlow> sequenceFlows = flowNode.getOutgoing();
                if (i < actList.size() - 1) {
                    HistoricActivityInstance nextAct = ActivityTypes.MULTI_INSTANCE_BODY.equals(actList.get(i + 1).getActivityType())
                            ? actList.get(i + 2) : actList.get(i + 1);
                    // 判断流出箭头的流入节点是否在这些节点中
                    sequenceFlows.forEach(flow -> {
                        if (flow.getTarget().getId().equals(nextAct.getActivityId())) {
                            dto.getSeqIds().add(flow.getId());
                        }
                    });
                }
            }
        }
        return dto;
    }

    /**
     * 获取流程节点
     *
     * @param id
     */
    public List<ProcessNodeVo> getProcessNode(String id) {
        List<ProcessNodeVo> list = new ArrayList<>();
        BpmnModelInstance bpmnModel = repositoryService.getBpmnModelInstance(id);
        Collection<Process> processes = bpmnModel.getModelElementsByType(Process.class);
        if (processes == null || processes.size() == 0) {
            return list;
        }
        String startId = "";

        for (Process process : processes) {
            Collection<FlowElement> elements = process.getFlowElements();

            for (FlowElement element : elements) {
                if (element instanceof StartEvent) {
                    // 开始节点
                    startId = element.getId();
                    break;
                }
            }

            for (FlowElement element : elements) {
                if (element instanceof UserTask) {
                    ProcessNodeVo node = new ProcessNodeVo();
                    node.setProcDefId(id);
                    node.setId(element.getId());
                    node.setTitle(element.getName());
                    node.setType(1);
                    node.setUsers(actNodeService.findUserByNodeId(element.getId(), id));
                    // 设置关联角色
                    //                    node.setRoles(actNodeService.findRoleByNodeId(element.getId(), id));
                    // 设置关联部门
                    //                    node.setDepartments(actNodeService.findDepartmentByNodeId(element.getId(),id));
                    // 设置关联部门负责人
                    //                    node.setDepartmentManages(actNodeService.findDepartmentManageByNodeId(element.getId(),id));
                    // 是否设置发起人部门负责人
                    //                    node.setChooseDepHeader(actNodeService.hasChooseDepHeader(element.getId(),id));
                    //                     是否设置发起人
                    //                    node.setChooseSponsor(actNodeService.hasChooseSponsor(element.getId(), id));
                    //设置部门
                    node.setPosts(actNodeService.findPostByNodeId(element.getId(), id));
                    node.setChooseSign(actNodeService.hasChooseSign(element.getId(), id));

                    node.setMultiple(element.getUniqueChildElementByType(MultiInstanceLoopCharacteristics.class) != null);

                    // 设置表单变量
                    StringBuilder variable = new StringBuilder();
                    List<String> formVariables = actNodeService.findFormVariableByNodeId(element.getId(), id);
                    for (String formVariable : formVariables) {
                        variable.append(formVariable).append(",");
                    }
                    node.setFormVariables(variable.length() > 0 ? variable.substring(0, variable.length() - 1) : variable.toString());

                    //只需要保证第一个节点位置正确就可以了
                    boolean added = false;
                    for (FlowNode pn : ((FlowNode) element).getPreviousNodes().list()) {
                        if (pn.getId().equals(startId)) {
                            list.add(0, node);
                            added = true;
                            break;
                        }
                    }
                    if (!added) {
                        list.add(node);
                    }
                }
            }
        }

        return list;
    }

    /**
     * 排序节点
     *
     * @param startId
     * @param nodeMaps
     * @param endId
     * @param list
     */
    public void sortNode(String startId, Map<String, ProcessNodeVo> nodeMaps, String endId, List<ProcessNodeVo> list) {
        list.add(nodeMaps.get(startId));
        startId = nodeMaps.get(startId).getId();
        if (startId.equals(endId)) {
            return;
        }
        sortNode(startId, nodeMaps, endId, list);
    }
}
