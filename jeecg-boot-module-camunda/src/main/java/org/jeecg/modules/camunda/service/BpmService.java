package org.jeecg.modules.camunda.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.repository.Deployment;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ActivityInstance;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.runtime.ProcessInstanceModificationBuilder;
import org.camunda.bpm.engine.task.Task;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.camunda.entity.ActZprocess;
import org.jeecg.modules.camunda.entity.param.TaskQueryParam;
import org.jeecg.modules.camunda.mapper.ActHiTaskInstMapper;
import org.jeecg.modules.camunda.service.impl.ActZprocessServiceImpl;
import org.jeecg.modules.camunda.utils.NumberUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

/**
 * 流程相关的类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年08月08日 23:44
 */
@Service
@Slf4j
public class BpmService {

    @Autowired
    private HistoryService historyService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private ActHiTaskInstMapper actHiTaskInstMapper;

    @Autowired
    private CamundaCommonService camundaCommonService;

    @Autowired
    private ActZprocessServiceImpl actZprocessService;


    /**
     * 驳回
     *
     * @param rejectType    驳回类型，1：起草节点，2：上一节点，3：目标节点
     * @param processInstId 流程实例id
     * @param currentTaskId 当前任务id
     * @param reason 审批已经意见
     * @param isReject  true:为审批人驳回 ; false:为申请人撤回
     * @return
     */
//    @Transactional(rollbackFor = Exception.class)
    public void rollbackProcess(Integer rejectType, String processInstId, String currentTaskId,  String reason, Boolean isReject) throws Exception {
        ActivityInstance tree = runtimeService.getActivityInstance(processInstId);
        ProcessInstance instance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstId).singleResult();
        if (instance == null) {
            throw new Exception("流程已经结束！");

        }
        String toActId = "";
        if (NumberUtil.equalsValue(rejectType, 1)) {
            toActId = getStartNode(processInstId).getActivityId();
        } else if (NumberUtil.equalsValue(rejectType, 2)) {
            List<HistoricActivityInstance> resultList = historyService
                    .createHistoricActivityInstanceQuery()
                    .processInstanceId(processInstId)
                    .activityType("userTask")
                    .finished()
                    .orderByHistoricActivityInstanceEndTime()
                    .desc()
                    .list();
            if (resultList == null || resultList.size() <= 0) {
                throw new Exception("未找到上一节点");
            }
            toActId = resultList.get(0).getActivityId();
        } else if (NumberUtil.equalsValue(rejectType, 3)) {
            if (StringUtils.isBlank(toActId)) {
                throw new Exception("指定目标节点不能为空！");
            }
        } else {
            throw new Exception("驳回类型值不对，三种类型  1：起草节点，2：上一节点，3：目标节点！");
        }
        //        获取当前任务节点1
        Task task = taskService.createTaskQuery().taskId(currentTaskId).singleResult();
        //填写驳回意见
        taskService.createComment(currentTaskId, processInstId, isReject ? "驳回:"+ reason :"撤回:"+ reason);
        //业务数据也改成“驳回“状态
        if (task == null) {
            throw new Exception("流程节点已过！请刷新页面！");
        }
        if (toActId.equals(tree.getChildActivityInstances()[0].getActivityId())) {
            throw new Exception(isReject ? "重复驳回！请刷新页面！" :"重复撤回！请刷新页面！");
        }
        taskService.createComment(currentTaskId, processInstId, isReject ? "驳回流程":"撤回流程");
        ProcessInstanceModificationBuilder processInstanceModification = runtimeService
                .createProcessInstanceModification(processInstId);
//        Set<String> taskIds = new HashSet<>();
        if ("multiInstanceBody".equals(tree.getChildActivityInstances()[0].getActivityType())) {
            for (ActivityInstance childInstance : tree.getChildActivityInstances()[0].getChildActivityInstances()) {
                processInstanceModification.cancelActivityInstance(childInstance.getId());
            }
          /*  //未完成的
            List<HistoricActivityInstance> historicActInList = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstId).unfinished().list();
            historicActInList.stream().filter(historicActivityInstance -> !historicActivityInstance.getActivityId().endsWith("#multiInstanceBody")).forEach(t -> taskIds.add(t.getTaskId()));*/
        } else {
//            taskIds.add(currentTaskId);
            processInstanceModification.cancelActivityInstance(getInstanceIdForActivity(tree, task.getTaskDefinitionKey()));
        }
        processInstanceModification.startBeforeActivity(toActId)
                .execute();

     /*   runtimeService
                .createProcessInstanceModification(processInstId)
//                .cancelActivityInstance(getInstanceIdForActivity(tree,  flag ? "" :task.getTaskDefinitionKey()))//置为空才能正常全部清空
//                .cancelActivityInstance(getInstanceIdForActivity(tree, ""))//置为空才能正常全部清空
                .startBeforeActivity(toActId)
                .execute();*/
        actHiTaskInstMapper.updateHiTaskInstByIdArray(Arrays.asList(currentTaskId),isReject?"rejected":"retrieve");
    }

    /**
     * 获取申请节点
     * @param processInstId
     * @return
     * @throws Exception
     */
    public HistoricActivityInstance getStartNode(String processInstId) throws Exception {
        List<HistoricActivityInstance> resultList = historyService
                .createHistoricActivityInstanceQuery()
                .processInstanceId(processInstId)
                .activityType("userTask")
                .finished()
                .orderByHistoricActivityInstanceEndTime()
                .asc()
                .list();
        if (resultList == null || resultList.size() <= 0) {
            throw new Exception("未找到发起节点");
        }
        return  resultList.get(0);
    }

    public String getInstanceIdForActivity(ActivityInstance activityInstance, String activityId) {
        ActivityInstance instance = getChildInstanceForActivity(activityInstance, activityId);
        if (instance != null) {
            return instance.getId();
        }
        return null;
    }

    public ActivityInstance getChildInstanceForActivity(ActivityInstance activityInstance, String activityId) {
        if (activityId.equals(activityInstance.getActivityId())) {
            return activityInstance;
        }

        for (ActivityInstance childInstance : activityInstance.getChildActivityInstances()) {
            ActivityInstance instance = getChildInstanceForActivity(childInstance, activityId);
            if (instance != null) {
                return instance;
            }
        }

        return null;
    }

    /**
     * 查询已完成的实例
     *
     * @param taskQueryParam 任务查询参数
     * @return 响应结果
     */
    public Result<Page<HistoricProcessInstance>> queryHistoryProcess(TaskQueryParam taskQueryParam) {
        taskQueryParam.setFinished(true);
        return camundaCommonService.queryHistoryProcess(taskQueryParam);
    }

    @Transactional(rollbackFor = Exception.class)
    public Result<?> deploy(String id) {
        //通过id部署流程
        InputStream processModel = repositoryService.getProcessModel(id);
        if (processModel == null) {
            return Result.error("模型数据为空，请先成功设计流程并保存");
        }

        ProcessDefinition processDefinition = repositoryService.getProcessDefinition(id);
        try {
            Deployment deployment = repositoryService.createDeployment().name(processDefinition.getName()).addInputStream(processDefinition.getResourceName(), processModel).deploy();
            // 设置流程分类 保存扩展流程至数据库
            List<ProcessDefinition> list = repositoryService.createProcessDefinitionQuery().deploymentId(deployment.getId()).list();
            for (ProcessDefinition pd : list) {
                ActZprocess actZprocess = new ActZprocess();
                actZprocess.setId(pd.getId());
                actZprocess.setName(processDefinition.getName());
                actZprocess.setProcessKey(processDefinition.getKey());
                actZprocess.setDeploymentId(deployment.getId());
                actZprocess.setDescription(processDefinition.getDescription());
                actZprocess.setVersion(pd.getVersion());
                //TODO:目前为空值
                actZprocess.setDiagramName(pd.getResourceName());
                actZprocessService.setAllOldByProcessKey(processDefinition.getKey());
                actZprocess.setLatest(true);
                actZprocessService.save(actZprocess);
            }
        } catch (Exception e) {
            String err = e.toString();
            log.error(e.getMessage(), e);
            if (err.indexOf("NCName") > -1) {
                return Result.error("部署失败：流程设计中的流程名称不能为空，不能为数字以及特殊字符开头！");
            }
            if (err.indexOf("PRIMARY") > -1) {
                return Result.error("部署失败：该模型已发布，key唯一！");
            }
            return Result.error("部署失败！");
        }

        return Result.ok("部署成功");
    }
}
