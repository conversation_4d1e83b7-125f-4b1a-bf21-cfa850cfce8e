package org.jeecg.modules.camunda.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.camunda.entity.ActNode;
import org.jeecg.modules.camunda.entity.Role;

import java.util.List;

/**
 * 流程节点扩展表
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年08月15日 23:25
 */
public interface IActNodeService extends IService<ActNode> {
}
