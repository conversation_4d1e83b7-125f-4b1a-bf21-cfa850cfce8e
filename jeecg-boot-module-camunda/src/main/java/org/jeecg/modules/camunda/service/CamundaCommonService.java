package org.jeecg.modules.camunda.service;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.history.*;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.runtime.VariableInstance;
import org.camunda.bpm.engine.runtime.VariableInstanceQuery;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.engine.task.TaskQuery;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.camunda.entity.param.ProcessVariablesQueryParam;
import org.jeecg.modules.camunda.entity.param.TaskQueryParam;
import org.jeecg.modules.camunda.utils.CamundaUtils;
import org.jeecg.modules.camunda.vo.ProcessDefinitionVO;
import org.jeecg.modules.camunda.vo.TaskVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Camunda通用服务类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年08月02日 16:45
 */
@Slf4j
@Service
public class CamundaCommonService {

    @Resource
    private RuntimeService runtimeService;

    @Resource
    private HistoryService historyService;

    @Resource
    private TaskService taskService;

    /** ================================================================================== */
    /** ========================== 流程相关（RuntimeService）=============================== */
    /** ================================================================================== */

    /**
     * 启动流程实例
     *
     * @param processDefinitionKey
     * @param businessKey
     * @param processVariables
     * @return
     */
    public ProcessInstance startProcessInstance(String processDefinitionKey, String businessKey, Object processVariables) {
        log.debug("start process instance, processDefinitionKey: {}, businessKey={}, processVariables: {}", processDefinitionKey, businessKey, processVariables);
        Map processVariablesMap = CamundaUtils.convertProcessVariablesFromEntity(processVariables);
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processDefinitionKey, businessKey, processVariablesMap);
        log.debug("start process instance success, processInstanceId: {}", processInstance.getId());
        //跳过第一执行节点
        Task task = null;
        TaskQuery taskQuery = taskService.createTaskQuery();
        CamundaUtils.setNotNull(taskQuery::taskAssignee, processVariablesMap.get("userName") + "");
        List<Task> todoList = taskQuery.list();
        for (Task tmp : todoList) {
            if (tmp.getProcessInstanceId().equals(processInstance.getId())) {
                task = tmp;
                break;
            }
        }
        taskService.complete(task.getId());

        return processInstance;
    }

    /**
     * 启动流程实例
     *
     * @param processDefinitionKey
     * @param businessKey
     * @return
     */
    public ProcessInstance startProcessInstance(String processDefinitionKey, String businessKey) {
        log.debug("start process instance, processDefinitionKey: {}, businessKey={}", processDefinitionKey, businessKey);
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processDefinitionKey, businessKey);
        log.debug("start process instance success, processInstanceId: {}", processInstance.getId());
        return processInstance;
    }

    /**
     * 获取流程实例对应的流程变量集合
     *
     * @param processInstanceId
     * @return
     */
    public Map<String, Object> getRuntimeProcessVariables(String processInstanceId) {
        List<VariableInstance> variableInstanceList = runtimeService.createVariableInstanceQuery()
                .processInstanceIdIn(processInstanceId)
                .list();
        Map<String, Object> variableInstanceMap = CamundaUtils.convertVariableInstances(variableInstanceList);
        return variableInstanceMap;
    }

    /**
     * 获取流程实例对应的流程变量集合
     *
     * @param processVariablesQueryParam
     * @return
     */
    public Map<String, Object> getRuntimeProcessVariables(ProcessVariablesQueryParam processVariablesQueryParam) {
        //创建查询
        VariableInstanceQuery variableInstanceQuery = runtimeService.createVariableInstanceQuery();
        CamundaUtils.setNotNull(variableInstanceQuery::processInstanceIdIn, processVariablesQueryParam.getProcessInstanceId());
        CamundaUtils.setNotNull(variableInstanceQuery::taskIdIn, processVariablesQueryParam.getTaskId());
        CamundaUtils.setNotNull(variableInstanceQuery::variableName, processVariablesQueryParam.getVariableName());
        CamundaUtils.setNotNull(variableInstanceQuery::tenantIdIn, processVariablesQueryParam.getTenantId());

        //获取流程变量
        List<VariableInstance> variableInstanceList = variableInstanceQuery.list();
        Map<String, Object> variableInstanceMap = CamundaUtils.convertVariableInstances(variableInstanceList);
        return variableInstanceMap;
    }

    /**
     * 获取流程实例对应的流程变量集合
     *
     * @param processInstanceId
     * @param processVariableName
     * @param <T>
     * @return
     */
    public <T> T getRuntimeProcessVariables(String processInstanceId, String processVariableName) {
        VariableInstance variableInstance = runtimeService.createVariableInstanceQuery()
                .processInstanceIdIn(processInstanceId)
                .variableName(processVariableName)
                .singleResult();
        return (T) variableInstance.getValue();
    }

    /**
     * 获取流程实例对应的历史流程变量集合
     *
     * @param processInstanceId
     * @return
     */
    public Map<String, Object> getHistoryProcessVariables(String processInstanceId) {
        //获取流程变量
        List<HistoricVariableInstance> historicVariableInstanceList = historyService.createHistoricVariableInstanceQuery()
                .processInstanceIdIn(processInstanceId)
                .list();
        Map<String, Object> variableInstanceMap = CamundaUtils.convertHistoricVariableInstances(historicVariableInstanceList);
        return variableInstanceMap;
    }

    /** ================================================================================== */
    /** ========================== 任务相关（TaskService、HistoryService）================== */
    /** ================================================================================== */
    /**
     * 查询代办任务分页列表
     *
     * @param taskQueryParam
     * @return
     */
    public <T> Result<Page<Task>> queryRuntimeTasks(TaskQueryParam taskQueryParam) {
        return queryRuntimeTasks(taskQueryParam, null, null, null, null);
    }

    /**
     * 查询代办任务分页列表
     *
     * @param taskQueryParam
     * @param extendTaskQuery
     * @return
     */
    public <T> Result<Page<Task>> queryRuntimeTasks(TaskQueryParam taskQueryParam, Consumer<TaskQuery> extendTaskQuery) {
        return this.queryRuntimeTasks(taskQueryParam, extendTaskQuery, null, null, null);
    }

    /**
     * 查询代办任务分页列表
     *
     * @param taskQueryParam                 任务查询参数
     * @param taskBizKeyVariableName         任务对应的 用于标识任务数据的 流程变量名，
     * @param taskBizKey2BizTaskDataFunction 转换 标识任务数据的流程变量名 为 具体业务任务数据
     * @param <T>
     * @return
     */
    public <T> Result<Page<Task>> queryRuntimeTasks(TaskQueryParam taskQueryParam,
                                                       String taskBizKeyVariableName,
                                                       Function<String, T> taskBizKey2BizTaskDataFunction) {
        return this.queryRuntimeTasks(taskQueryParam, null, taskBizKeyVariableName, taskBizKey2BizTaskDataFunction, null);
    }

    /**
     * 查询代办任务分页列表
     *
     * @param taskQueryParam                任务查询参数
     * @param bizKey2BizProcessDataFunction 转换 流程BusinessKey 为 具体业务流程数据
     * @param <T>
     * @return
     */
    public <T> Result<Page<Task>> queryRuntimeTasks(TaskQueryParam taskQueryParam, Function<String, T> bizKey2BizProcessDataFunction) {
        return this.queryRuntimeTasks(taskQueryParam, null, null, null, bizKey2BizProcessDataFunction);
    }

    /**
     * 查询代办任务分页列表
     *
     * @param taskQueryParam                 任务查询参数
     * @param extendTaskQuery                自定义扩展查询参数（若无特殊扩展可为空）
     * @param taskBizKeyVariableName         任务对应的 用于标识任务数据的 流程变量名，
     * @param taskBizKey2BizTaskDataFunction 转换 标识任务数据的流程变量名 为 具体业务任务数据
     * @param bizKey2BizProcessDataFunction  转换 流程BusinessKey 为 具体业务流程数据
     * @param <T>
     * @return
     */
    public <T> Result<Page<Task>> queryRuntimeTasks(TaskQueryParam taskQueryParam,
                                                       Consumer<TaskQuery> extendTaskQuery,
                                                       String taskBizKeyVariableName,
                                                       Function<String, T> taskBizKey2BizTaskDataFunction,
                                                       Function<String, T> bizKey2BizProcessDataFunction) {
        log.debug("query runtime tasks, param: {}", taskQueryParam);

        //创建查询
        TaskQuery taskQuery = taskService.createTaskQuery();
        CamundaUtils.setNotNull(taskQuery::processInstanceId, taskQueryParam.getProcessInstanceId());
        CamundaUtils.setNotNull(taskQuery::taskDefinitionKey, taskQueryParam.getTaskDefinitionKey());
        CamundaUtils.setNotNull(taskQuery::taskAssignee, taskQueryParam.getAssignee());
        CamundaUtils.setNotNull(taskQuery::tenantIdIn, taskQueryParam.getTenantId());

        Optional.ofNullable(extendTaskQuery)
                .ifPresent(addConditionConsumer -> addConditionConsumer.accept(taskQuery));
        //计算分页参数
        Integer curPageStartIndex = (taskQueryParam.getPageNo() - 1) * taskQueryParam.getPageSize();
        //设置排序参数
        CamundaUtils.setTaskQueryOrderBy(taskQuery, taskQueryParam);
        //查询任务列表
        List<Task> taskList = taskQuery.listPage(curPageStartIndex, taskQueryParam.getPageSize());
        //查询任务总数
        Long totalCount = taskQuery.count();
        //直接返回Task对象Json序列化异常，转换为VO对象
        List<TaskVo<T>> taskVoList = taskList.stream()
                .map(task -> {
                    TaskVo<T> taskVo = CamundaUtils.convertTask(task);
                    return taskVo;
                })
                .map(taskVo -> {
                    //设置流程变量
                    this.setTaskProcessVariables(taskVo, taskQueryParam);
                    //设置businessKey对应的流程业务数据
                    this.setTaskVoProcessData(taskVo, bizKey2BizProcessDataFunction);
                    //设置task变量（例如paymentId）对应的task业务数据
                    this.setTaskVoTaskData(taskVo, taskBizKeyVariableName, taskBizKey2BizTaskDataFunction);
                    return taskVo;
                })
                .collect(Collectors.toList());
        Page<Task> page = new Page<>(taskQueryParam.getPageNo(), taskQueryParam.getPageSize(),totalCount );
        page.setRecords(taskList);
        return Result.ok(page);
    }

    /**
     * 设置task对应流程变量
     *
     * @param taskVo
     * @param taskQueryParam
     */
    private void setTaskProcessVariables(TaskVo taskVo, TaskQueryParam taskQueryParam) {
        //设置流程变量
        if (taskQueryParam.getWithProcessVariables()) {
            taskVo.setProcessVariables(this.getRuntimeProcessVariables(taskVo.getProcessInstanceId()));
        }
    }

    /**
     * 设置task对应的业务流程数据<br/>
     * task -> task.processInstanceId -> processInstance -> processInstance.businessKey -> bizProcessData
     *
     * @param taskVo
     * @param bizKey2BizProcessDataFunction
     * @param <T>
     */
    private <T> void setTaskVoProcessData(TaskVo<T> taskVo, Function<String, T> bizKey2BizProcessDataFunction) {
        if (null == bizKey2BizProcessDataFunction) {
            return;
        }
        ProcessInstance processInstance = this.runtimeService.createProcessInstanceQuery()
                .processInstanceId(taskVo.getProcessInstanceId())
                .singleResult();
        if (null != processInstance && null != processInstance.getBusinessKey()) {
            taskVo.setBizProcessData(bizKey2BizProcessDataFunction.apply(processInstance.getBusinessKey()));
        }
    }

    /**
     * 设置task对应的业务任务数据
     * task -> task.processVariables[taskBizDataVariableName] -> bizTaskData
     *
     * @param taskVo
     * @param taskBizDataVariableName
     * @param processVariable2BizTaskDataFunction
     * @param <M>
     */
    private <M> void setTaskVoTaskData(TaskVo<M> taskVo, String taskBizDataVariableName,
                                       Function<String, M> processVariable2BizTaskDataFunction) {
        if (null == taskBizDataVariableName || null == processVariable2BizTaskDataFunction) {
            return;
        }
        String taskDataIdValue = null;
        if (null != taskVo.getProcessVariables() && taskVo.getProcessVariables().containsKey(taskBizDataVariableName)) {
            taskDataIdValue = String.valueOf(taskVo.getProcessVariables().get(taskBizDataVariableName));
        } else {
            taskDataIdValue = this.getRuntimeProcessVariables(taskVo.getProcessInstanceId(), taskBizDataVariableName);
        }
        taskVo.setBizTaskData(processVariable2BizTaskDataFunction.apply(taskDataIdValue));
    }

    /**
     * 查询已完成任务分页列表
     *
     * @param taskQueryParam
     * @param addCondition
     * @return
     */
    public Page<HistoricTaskInstance> queryHistoryTasks(TaskQueryParam taskQueryParam, Consumer<HistoricTaskInstanceQuery> addCondition) {
        //创建查询
        HistoricTaskInstanceQuery historicTaskInstanceQuery = this.historyService.createHistoricTaskInstanceQuery();
        if (Boolean.TRUE.equals(taskQueryParam.getFinished())) {
            historicTaskInstanceQuery.finished();
        }
        if (Boolean.FALSE.equals(taskQueryParam.getFinished())) {
            historicTaskInstanceQuery.unfinished();
        }
        CamundaUtils.setNotNull(historicTaskInstanceQuery::processInstanceId, taskQueryParam.getProcessInstanceId());
        CamundaUtils.setNotNull(historicTaskInstanceQuery::taskDefinitionKey, taskQueryParam.getTaskDefinitionKey());
        CamundaUtils.setNotNull(historicTaskInstanceQuery::taskAssignee, taskQueryParam.getAssignee());
        CamundaUtils.setNotNull(historicTaskInstanceQuery::tenantIdIn, taskQueryParam.getTenantId());
        CamundaUtils.setNotNull(historicTaskInstanceQuery::startedAfter, taskQueryParam.getStartedAfter());
        CamundaUtils.setNotNull(historicTaskInstanceQuery::startedBefore, taskQueryParam.getStartedBefore());
        //添加附加条件
        Optional.ofNullable(addCondition)
                .ifPresent(addConditionConsumer -> addConditionConsumer.accept(historicTaskInstanceQuery));
        //计算分页参数
        Integer curPageStartIndex = CamundaUtils.getPageStartIndex(taskQueryParam);
        //设置排序参数
        CamundaUtils.setHistoricTaskInstanceQueryOrderBy(historicTaskInstanceQuery, taskQueryParam);
        //查询任务列表
        List<HistoricTaskInstance> taskList = historicTaskInstanceQuery.listPage(curPageStartIndex, taskQueryParam.getPageSize());
        //查询任务总数
        Long totalCount = historicTaskInstanceQuery.count();

        ////直接返回Task对象Json序列化异常，转换为VO对象
        //List<TaskVo> taskVoList = taskList.stream()
        //        .map(taskVo -> {
        //            if (taskQueryParam.getWithProcessVariables()) {
        //                taskVo.setProcessVariables(this.getProcessVariables(taskVo.getProcessInstanceId()));
        //            }
        //            return taskVo;
        //        })
        //        .collect(Collectors.toList());
        Page<HistoricTaskInstance> page = new Page<>(taskQueryParam.getPageNo(), taskQueryParam.getPageSize(),totalCount );
        page.setRecords(taskList);
        return page;
    }

    /**
     * 查询已完成任务分页列表
     *
     * @param taskQueryParam
     * @return
     */
    public Page<HistoricTaskInstance> queryHistoryTasks(TaskQueryParam taskQueryParam) {
        return this.queryHistoryTasks(taskQueryParam, null);
    }


    /**
     * 完成任务
     *
     * @param taskId 任务ID
     */
    public void completeTask(String taskId) {
        log.debug("complete task, taskId: {}", taskId);
        this.taskService.complete(taskId);
    }

    /**
     * 完成任务
     *
     * @param taskId             任务ID
     * @param processInstancesId 流程实例ID
     * @param comment            审批意见
     * @param processVariables   流程变量（支持Map和POJO）
     */
    public void completeTask(String taskId, String processInstancesId, String comment, Object processVariables) {
        log.debug("complete task, taskId: {}, processVariables: {}", taskId, processVariables);
        Map processVariablesMap = CamundaUtils.convertProcessVariablesFromEntity(processVariables);
        taskService.createComment(taskId, processInstancesId, comment);
        this.taskService.complete(taskId, processVariablesMap);
    }

    /**
     * 获取待办任务
     *
     * @param account   人员账号
     * @param actInstId 流程ID(非必填)
     * @param busTypes  流程类型(非必填)
     * @return
     * @throws Exception List<ProcessTask>
     * @throws
     * @since 1.0.0
     */
//    public List<Task> getTasks(String account, String actInstId, BpmBusTypeEnum... busTypes) {
    public List<Task> getTasks(String account, String actInstId) {
        //创建查询
        TaskQuery taskQuery = this.taskService.createTaskQuery();
        CamundaUtils.setNotNull(taskQuery::processInstanceId, actInstId);
        CamundaUtils.setNotNull(taskQuery::taskAssignee, account);
        /*if (busTypes != null && busTypes.length > 0) {
            StringBuilder processKey = new StringBuilder();
            for (BpmBusTypeEnum busType : busTypes) {
                processKey.append(",").append(busType.getBusType());
            }
            //TODO: 待完善
           *//* processKey.deleteCharAt(0);
            paramsMap.put("processKey", processKey.toString());*//*
        }*/
        //查询任务列表
        return taskQuery.list();
    }

    /**
     * 根据流程获取TaskId
     *
     * @param processInstanceId
     * @param curState
     * @return Long
     * @throws Exception
     */
    public String getTaskId(String processInstanceId, String curState) {
        List<Task> tasks = getTasks("", processInstanceId);
        if (CollectionUtils.isNotEmpty(tasks)) {
            for (Task task : tasks) {
                if (StringUtils.isEmpty(curState) || curState.equals(task.getTaskDefinitionKey())) {
                    return task.getId();
                }
            }
        }
        return null;
    }

    public Result<Page<HistoricProcessInstance>> queryHistoryProcess(TaskQueryParam taskQueryParam) {
        //创建查询
        HistoricProcessInstanceQuery historicProcessInstanceQuery = this.historyService.createHistoricProcessInstanceQuery();
        if (Boolean.TRUE.equals(taskQueryParam.getFinished())) {
            historicProcessInstanceQuery.finished();
        }
        if (Boolean.FALSE.equals(taskQueryParam.getFinished())) {
            historicProcessInstanceQuery.unfinished();
        }
        CamundaUtils.setNotNull(historicProcessInstanceQuery::processInstanceId, taskQueryParam.getProcessInstanceId());
        CamundaUtils.setNotNull(historicProcessInstanceQuery::processDefinitionKey, taskQueryParam.getProcessDefinitionKey());
        CamundaUtils.setNotNull(historicProcessInstanceQuery::tenantIdIn, taskQueryParam.getTenantId());
        CamundaUtils.setNotNull(historicProcessInstanceQuery::startedAfter, taskQueryParam.getStartedAfter());
        CamundaUtils.setNotNull(historicProcessInstanceQuery::startedBefore, taskQueryParam.getStartedBefore());
        //计算分页参数
        Integer curPageStartIndex = CamundaUtils.getPageStartIndex(taskQueryParam);
        //设置排序参数
//        CamundaUtils.setHistoricTaskInstanceQueryOrderBy(historicProcessInstanceQuery, taskQueryParam);
        //查询任务列表
        List<HistoricProcessInstance> processInstanceList = historicProcessInstanceQuery.listPage(curPageStartIndex, taskQueryParam.getPageSize());
        //查询任务总数
        Long totalCount = historicProcessInstanceQuery.count();
        Page<HistoricProcessInstance> page = new Page<>(taskQueryParam.getPageNo(), taskQueryParam.getPageSize(), totalCount );
        page.setRecords(processInstanceList);
        return Result.ok(page);
    }
}
