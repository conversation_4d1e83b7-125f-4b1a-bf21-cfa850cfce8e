package org.jeecg.modules.camunda.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.shiro.SecurityUtils;
import org.camunda.bpm.engine.*;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.impl.persistence.entity.TaskEntity;
import org.camunda.bpm.engine.repository.Deployment;
import org.camunda.bpm.engine.task.Comment;
import org.camunda.bpm.engine.task.Task;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.camunda.constants.ProcessConstants;
import org.jeecg.modules.camunda.entity.param.TaskQueryParam;
import org.jeecg.modules.camunda.mapper.ActHiTaskInstMapper;
import org.jeecg.modules.camunda.service.BpmService;
import org.jeecg.modules.camunda.vo.ProcessDefinitionInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 流程相关的类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年08月08日 23:55
 */
//@RestController
//@RequestMapping("/bpmn")
@Slf4j
public class BpmController {
    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private ActHiTaskInstMapper actHiTaskInstMapper;

    /* *//**
     * 设计模型保存 TODO:hzk
     * @param modelId
     * @param request
     *//*
    @RequestMapping(value="/model/{modelId}/save", method = RequestMethod.PUT)
    @ResponseStatus(value = HttpStatus.OK)
    public void saveModel(@PathVariable String modelId, HttpServletRequest request) {
        repositoryService.createProcessDefinitionQuery()
    }*/


    /**
     * 查询历史信息
     */
    @PostMapping("/queryHistory")
    public void queryHistory() {
        List<HistoricActivityInstance> list = historyService.createHistoricActivityInstanceQuery()
                .finished()
                .orderByHistoricActivityInstanceEndTime()
                .asc()
                .list();
        for (HistoricActivityInstance instance : list) {
            System.out.println(instance.getActivityId());
            System.out.println(instance.getProcessDefinitionKey());
            System.out.println(instance.getAssignee());
            System.out.println(instance.getStartTime());
            System.out.println(instance.getEndTime());
            System.out.println("=============================");
        }
    }

    /**
     * @Description: 流程定义部署
     */
    @PostMapping("/deployText")
    public Result<?> deploy(@RequestBody ProcessDefinitionInfo processDefinition) {
        /*String bpmn = "diagram_1.bpmn";
        //class方式
        Deployment deploy = repositoryService.createDeployment()
                .addClasspathResource("processes/" + bpmn)
                .deploy();
        System.out.println(deploy.getId());*/
        //获取当前用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //通过xmlText部署流程并添加创建用户id
        Deployment deployment = repositoryService.createDeployment()
                .name(processDefinition.getDefinitionName())
                //                .tenantId(String.valueOf(sysUser.getId()))
                //TODO:必须带“.bpmn”
                .addString(processDefinition.getResourceName() + ".bpmn", processDefinition.getResource())
                .deploy();
        return Result.OK(deployment.getId());
    }

    /**
     * @Description: 根据流程定义id部署
     */
    @PostMapping("/deploy/{id}")
    public Result<?> deploy(@PathVariable("id") String id) {
        return bpmService.deploy(id);
    }

    /**
     * @Description: 流程任务查询
     */
    @PostMapping("/taskquery")
    public void taskQuery() {
        //这个会查出所有流程定义key的数据，最好还是根据流程id去获取
        List<Task> tasks = taskService.createTaskQuery()
                .processDefinitionKey(ProcessConstants.APP_PRODUCT_PROCESS_ID)
                .list();
        for (Task task : tasks) {
            System.out.println(task.getAssignee());
            System.out.println(task.getId());
            System.out.println(task.getName());
            System.out.println(task.getTenantId());
        }
    }

    /**
     * 根据流程id获取审批记录
     *
     * @param processInstId
     */
    @GetMapping("/taskGetComment")
    public Result<List<Map<String, Object>>> taskGetComment(@RequestParam("processInstId") String processInstId) {
        List<HistoricActivityInstance> list = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstId)
                .orderByHistoricActivityInstanceStartTime()
                .asc().list();
        List<Map<String, Object>> result = new ArrayList<>(list.size());
        for (HistoricActivityInstance historicActivityInstance : list) {
            if (historicActivityInstance.getActivityId().endsWith("#multiInstanceBody")
                    || "startEvent".equals(historicActivityInstance.getActivityId()) || "endEvent".equals(historicActivityInstance.getActivityId())) {
                continue;
            }
            Map<String, Object> map = new HashMap<>();
            String taskId = historicActivityInstance.getTaskId();
            //创建查询
            TaskEntity taskEntity = actHiTaskInstMapper.selectById(taskId);
            List<Comment> taskComments = taskService.getTaskComments(taskId);
            map.put("activityName", historicActivityInstance.getActivityName());
            map.put("activityType", matching(historicActivityInstance.getActivityType()));
            map.put("assignee", historicActivityInstance.getAssignee());
            map.put("state", taskEntity.getDeleteReason() == null ? "" : matchState(taskEntity.getDeleteReason()));
            map.put("startTime", DateFormatUtils.format(historicActivityInstance.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
            map.put("endTime", historicActivityInstance.getEndTime() != null ? DateFormatUtils.format(historicActivityInstance.getEndTime(), "yyyy-MM-dd HH:mm:ss") : "");
            map.put("activityName", historicActivityInstance.getActivityName());
            if (taskComments.size() > 0) {
                map.put("message", taskComments.get(0).getFullMessage());
            } else {
                map.put("message", "");
            }
            result.add(map);
        }
        return Result.OK(result);

    }

    private String matchState(String deleteReason) {
        String value = "";
        switch (deleteReason) {
            case TaskEntity.DELETE_REASON_COMPLETED:
                value = "通过";
                break;
            case TaskEntity.DELETE_REASON_DELETED:
                value = "取消";
                break;
            case CommonConstant.BPM_STATUS__CANCEL:
                value = "申请人取消";
                break;
            case CommonConstant.BPM_STATUS_REJECTED:
                value = "驳回";
                break;
            case CommonConstant.BPM_STATUS_REFUSE:
                value = "拒绝";
                break;
            case CommonConstant.BPM_STATUS_RETRIEVE:
                value = "撤回";
                break;
            case CommonConstant.BPM_STATUS_FOLLOWING:
                value = "进行中";
                break;
            case CommonConstant.BPM_STATUS_DRAFT:
                value = "草稿";
                break;
            default:
                value = "";
                break;
        }
        return value;
    }

    private String matching(String activityType) {
        String value = "";
        switch (activityType) {
            case "startEvent":
                value = "开始";
                break;
            case "apply":
                value = "申请";
                break;
            case "approve1":
                value = "领导们审批";
                break;
            case "approve2":
                value = " 岸基审批";
                break;
            case "endEvent":
                value = " 结束";
                break;
            default:
                value = "未知节点";
                break;
        }
        return value;
    }

    /**
     * @Description: 驳回
     */
    @GetMapping("/rollbackProcess")
    public Result<?> rollbackProcess(@RequestParam("rejectType") Integer rejectType,
                                     @RequestParam("processInstId") String processInstId,
                                     @RequestParam("taskId") String taskId, @RequestParam("reason") String reason) throws Exception {
        bpmService.rollbackProcess(rejectType, processInstId, taskId,reason, true);
        return Result.OK("驳回成功");
    }


    /**
     * 查询已完成的实例
     *
     * @param taskQueryParam 任务查询参数
     * @return 响应结果
     */
    @PostMapping("/historyProcess")
    public Result<Page<HistoricProcessInstance>> getHistoryProcess(@Validated @RequestBody TaskQueryParam taskQueryParam) {
        log.info("get payment tasks history, param: {}", taskQueryParam);
        Result<Page<HistoricProcessInstance>> taskVoRespResult = bpmService.queryHistoryProcess(taskQueryParam);
        return taskVoRespResult;
    }

}
