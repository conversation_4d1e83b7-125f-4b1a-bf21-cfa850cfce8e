package org.jeecg.modules.camunda.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年11月05日 04:50
 */
@Data
public class Position {
    /**
     * id
     */
    private String id;
    /**
     * 职务编码
     */
    private String code;
    /**
     * 职务名称
     */
    private String name;
    /**
     * 职级
     */
    @Dict(dicCode = "position_rank")
    private String postRank;
    /**
     * 公司id
     */
    private String companyId;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 修改人
     */
    private String updateBy;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
    /**
     * 组织机构编码
     */
    private String sysOrgCode;
}
