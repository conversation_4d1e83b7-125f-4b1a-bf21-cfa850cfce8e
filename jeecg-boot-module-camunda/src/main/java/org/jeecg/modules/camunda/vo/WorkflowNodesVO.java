package org.jeecg.modules.camunda.vo;

import lombok.Data;
import org.camunda.bpm.engine.history.HistoricActivityInstance;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程活动节点ID和sequenceId数据
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年08月24日 23:50
 */
@Data
public class WorkflowNodesVO {
    /**
     * 活动节点ID
     */
    private List<HistoricActivityInstance> actIns = new ArrayList<>();
    /**
     * 活动节点关联sequence id
     */
    private List<String> seqIds = new ArrayList<>();


}
