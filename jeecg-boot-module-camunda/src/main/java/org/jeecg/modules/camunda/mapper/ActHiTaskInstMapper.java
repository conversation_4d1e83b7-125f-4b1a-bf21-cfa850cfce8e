package org.jeecg.modules.camunda.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.camunda.bpm.engine.impl.persistence.entity.TaskEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年08月10日 14:06
 */
@Mapper
public interface ActHiTaskInstMapper {
    @Update(" <script> " +
            " update ACT_HI_TASKINST set " +
            " DELETE_REASON_ = #{status} " +
            " where ID_ in " +
            " <foreach collection = 'taskIdLit' item = 't' separator = ',' open = '(' close = ')' > " +
            " #{t}" +
            " </foreach> " +
            " </script> ")
    int updateHiTaskInstByIdArray(@Param("taskIdLit") List<String> taskIdLit, @Param("status") String status);

    /**
     * 根据taskid获取历史任务
     *
     * @param taskId
     * @return
     */
    @Select("<script> select *  from ACT_HI_TASKINST where id_=#{taskId} </script> ")
    TaskEntity selectById(@Param("taskId") String taskId);

/**
 * 查询已办任务
 *
 * @param page         分页组件
 * @param queryWrapper 条件查询
 * @return 待办任务
 *//*
    @Select("SELECT  t3.* FROM( SELECTt1.*,t2.NAME_ as PROC_DEF_NAME_  FROM `act_hi_taskinst` t1 " +
            "INNER JOIN `act_re_procdef` t2 ON t1.PROC_DEF_ID_ = t2.ID_ " +
            "ORDER BY t1.PROC_INST_ID_, t1.START_TIME_ desc) t GROUP BY t.PROC_INST_ID_) t ${ew.customSqlSegment}")
    IPage<WorkflowTaskEntity> listCompleteTaskByPage(
            IPage<WorkflowTaskEntity> page,
            @Param(Constants.WRAPPER) QueryWrapper<WorkflowTaskEntity> queryWrapper);
    }*/

}
