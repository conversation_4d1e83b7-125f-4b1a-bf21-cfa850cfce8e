
部署注意事项：
```
执行db_update.sql
把ReportTemplate.docx文件放到Linux的/data/reports/templates/scl90-v1-0目录下
还需要更新中转服务器
```
变更点：
```
支持船员通过支付宝购买船员会员
受试人管理显示会员状态
学习资料和试卷增加收费配置
企业付费模式
(添加企业时，会员模式，以及添加套餐)
企业付费套餐机制
企业套餐续费和管理
中转服务，同步对应会员机制
考试机制调整：新增回查选项和exam增加paper字段

下面是节选的，因为写文档提的有点晚，之前的提交没有写提交说明
1 企业会员即将过期和过期人数按公司隔离
/adm/trainee/getWillAndExpireMemberCount

2 企业公司对已有船员进行套餐扣除逻辑优化，当套餐没有余额时，直接跳过该公司，不再对剩余的船员进行操作
/adm/sys/mng/deductMember

3 开通个人船员时，获取最近购买套餐由条件判断改为条件查询
/app/aliPay/tradeQuery

4 开启app注册绑定企业套餐的验证
/app/trainee/register

5 优化获取公司会员金额
/app/trainee/getMemberMoney

6 Packages表新增部门名称

7 删除之前的获取会员状态的接口
org.jeecg.modules.system.controller.ConfigInfoController
```