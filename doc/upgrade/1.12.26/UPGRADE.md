## 部署注意事项：

```text
1.需要执行db_update.sql
2.配置商户API证书私钥和微信支付公钥：
    把/1.12.26/微信支付/商户API证书私钥/apiclient_key.pem放到application-prod-saas.yml中
    wechatpay.privateKeyPath设置的路径
    把/1.12.26/微信支付/微信支付公钥/pub_key.pem放到application-prod-saas.yml中
    wechatpay.publicKeyPath设置的路径
功后，放到application-prod-saas.yml的wechatpay.privateKeyPath（私钥）和wechatpay.publicKeyPath（公钥）配置的目录下
3.中转服务需要同步更新到1.3.54
4.修改菜单管理下受试人管理中的url配置，找到/miniProgram/trainee/physicalDelete并删除
```

填写完`部署注意事项`后，按照下列`Check List`逐条对照检查，检查完成后勾选✅
* [x] 检查 `sql增量语句` 是否需要执行
* [x] 检查 `配置文件(application-*.yml)`中是否需要修改
* [x] 检查 `linux服务器` 配置/数据 文件是否需要新增或修改
* [x] 检查 `打包环境` 是否需要发生变动(例如: 需手动安装某些jar包)
* [x] 检查 `中转服务器` 是否需要更新

## 变更点：

```text
- 修复部分接口未脱敏或未隔离（接口：/adm/userPaper/list、/adm/trainee/listForPhaseItem）
- 修复`不等长的排序规则`报错（主要变更代码：UserPaperMapper.listPages）
- 修复首页船舶未考人员数量排名（SysDepartMapper.getNoExamNo）
- 修复已提交考卷、代打分考卷的状态过滤（ExamController.queryPageList）
- 修复首页学习资料总完成率排名（TraineeServiceImpl.doImportOrUpdateUser）
- 完成微信支付（AppWeChatPayController，PayConfig）
- app进行心理测试时，完善因为缺失知识点导致提交失败的错误日志
（ExamQuestionServiceImpl.doHandlePsychologyAnswer）
- 完成企业会员数量的返还机制的开发
（TraineeController.delete，TraineeServiceImpl.doImportOrUpdateUser、initTrainee）
- App注册失败时，归还套餐（AppTraineeController.register）
- 微信小程序添加套餐扣除和归还套餐（MiniProgramTraineeController.signAndLogin）
- 微信小程序修改部分接口的路径
（MiniProgramSysUserController修改为MiniProgramTraineeController）
- 修复新船导入受试人数量异常问题（TraineeServiceImpl.importUsers）
- 程序版本号升级至 1.12.26
```