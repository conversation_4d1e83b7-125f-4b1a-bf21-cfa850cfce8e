### update20250424.sql <==
# 创建表sys_trainee_depart_log
create table sys_trainee_depart_log
(
    id           int auto_increment primary key,
    trainee_id   varchar(32)                 null comment '船员id',
    trainee_name varchar(32)                 null comment '船员姓名',
    dep_name     varchar(32)                 null comment '部门名称',
    dep_id       varchar(32)                 null comment '部门id',
    link_date    datetime                    null comment '首次关联时间',
    link_times   int                         null comment '关联次数',
    create_time  datetime comment '创建时间' null,
    update_time  datetime comment '更新时间' null,
    unique key composite_key (trainee_id, dep_id)
) comment ='船员部门关联记录表';

# 严格化 sys_trainee_depart 索引
create unique index sys_trainee_depart_trainee_id_dep_id_uindex
    on sys_trainee_depart (trainee_id, dep_id);

drop index idx_sud_trainee_dep_id on sys_trainee_depart;

### update20250507.sql <==
update team
set trainee_count = (select trainee_count
                     from (select count(*) as trainee_count, team_id
                           from trainee
                           where trainee.del_flag = 0
                           group by team_id) t
                     where team.del_flag = 0
                       and team.id = t.team_id);

update team
set trainee_count = 0
where trainee_count is null;