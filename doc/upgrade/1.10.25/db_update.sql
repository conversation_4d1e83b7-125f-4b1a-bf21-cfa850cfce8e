### update20250421.sql <==
alter table `invoice_apply`
    add column `name` varchar(100) comment '申请人姓名';

alter table `invoice_apply`
    add column `pay_type` tinyint comment '支付方式';

UPDATE invoice_apply
    JOIN orders ON invoice_apply.order_num = orders.orders_num
SET invoice_apply.name = orders.name, invoice_apply.pay_type = orders.pay_type

### update20250425.sql <==
create index IDX_TRAINEE_REALNAME
    ON trainee (realname);

create index IDX_TRAINEE_CREATE_TIME
    ON trainee (create_time);

CREATE INDEX IDX_UPI_CTIME_USER_ITEM
    ON user_phase_item (create_time DESC, user_id, phase_item_id);

CREATE INDEX IDX_PI_ID_MODE
    ON phase_item (mode, id);

CREATE INDEX IDX_SU_DELFLAG_ID
    ON trainee (del_flag, id);

ANALYZE TABLE user_phase_item, trainee, phase_item, phase, team, sys_trainee_depart, sys_depart;
