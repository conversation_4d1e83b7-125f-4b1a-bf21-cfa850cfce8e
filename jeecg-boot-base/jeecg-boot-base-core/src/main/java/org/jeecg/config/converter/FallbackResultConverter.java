package org.jeecg.config.converter;

import org.jeecg.common.api.vo.Result;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.AbstractHttpMessageConverter;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

/**
 * 兜底消息转换器：强制将 Result 对象以 JSON 格式输出
 */
public class FallbackResultConverter extends AbstractHttpMessageConverter<Object> {

    private final MappingJackson2HttpMessageConverter jsonConverter;

    public FallbackResultConverter() {
        // 支持所有 MediaType，但优先级最低
        super(MediaType.ALL);
        this.jsonConverter  = new MappingJackson2HttpMessageConverter();
    }

    @Override
    protected boolean supports(Class<?> clazz) {
        // 仅处理 Result 类型且响应未提交时生效
        return Result.class.isAssignableFrom(clazz);
    }

    @NotNull
    @Override
    protected Object readInternal(Class<?> clazz, HttpInputMessage inputMessage) {
        throw new UnsupportedOperationException("Not supported");
    }

    @Override
    protected void writeInternal(Object object, HttpOutputMessage outputMessage)
            throws IOException, HttpMessageNotWritableException {

        // 强制设置响应头为 JSON
        outputMessage.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        // 复用 Jackson 转换器处理 Result 序列化
        jsonConverter.write(object,  MediaType.APPLICATION_JSON, outputMessage);
    }

    @NotNull
    @Override
    public List<MediaType> getSupportedMediaTypes() {
        // 声明支持所有类型，但实际仅在无法匹配其他转换器时生效
        return Collections.singletonList(MediaType.ALL);
    }
}