package org.jeecg.config.shiro.filters;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.AccessControlFilter;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @create 2019-02-01 15:56
 * @desc 鉴权请求URL访问权限拦截器
 */

@Slf4j
@Component
public class ResourceCheckFilter extends AccessControlFilter {

    private Boolean adminSuperMode = false;

    private String errorUrl;

    public String getErrorUrl() {
        return errorUrl;
    }

    public void setErrorUrl(String errorUrl) {
        this.errorUrl = errorUrl;
    }

    /**
     * 表示是否允许访问 ，如果允许访问返回true，否则false；
     *
     * @param servletRequest
     * @param servletResponse
     * @param o               表示写在拦截器中括号里面的字符串 mappedValue 就是 [urls] 配置中拦截器参数部分
     * @return
     * @throws Exception
     */
    @Override
    protected boolean isAccessAllowed(ServletRequest servletRequest, ServletResponse servletResponse, Object o) throws Exception {
       Subject subject = getSubject(servletRequest, servletResponse);
       String url = getPathWithinApplication(servletRequest);
       log.debug("当前用户正在访问的 url => {}", url);

       // adminSuperMode为true且用户为admin时跳过权限检查
       if (adminSuperMode && subject.getPrincipal() != null) {
           // 从Subject中获取登录用户信息
           Object principal = subject.getPrincipal();
           // 获取用户名
           String username = "";
           if (principal instanceof org.jeecg.common.system.vo.LoginUser) {
               username = ((org.jeecg.common.system.vo.LoginUser) principal).getUsername();
           } else if (principal instanceof String) {
               username = (String) principal;
           }
           // 如果是admin用户，则跳过权限检查
           if ("admin".equals(username)) {
               log.info("当前用户为admin，且启用超级管理员模式，跳过接口权限检查");
               return true;
           }
       }
       
       // 执行权限检查
       // RegexPermissionResolver会自动处理URL模式转换
       try {
           boolean permitted = subject.isPermitted(url);
           log.debug("用户对URL [{}] 的权限检查结果: {}", url, permitted);
           return permitted;
       } catch (Exception e) {
           log.error("权限检查异常", e);
           return false; // 出现异常时拒绝访问
       }
    }

    /**
     * onAccessDenied：表示当访问拒绝时是否已经处理了； 如果返回 true 表示需要继续处理； 如果返回 false
     * 表示该拦截器实例已经处理了，将直接返回即可。
     *
     * @param servletRequest
     * @param servletResponse
     * @return
     * @throws Exception
     */
    @Override
    protected boolean onAccessDenied(ServletRequest servletRequest, ServletResponse servletResponse) throws Exception {
        // 当 isAccessAllowed 返回 false 的时候，才会执行 method onAccessDenied

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        response.sendRedirect(request.getContextPath() + this.errorUrl);

        Subject subject = getSubject(servletRequest, servletResponse);
        String url = getPathWithinApplication(servletRequest);
        // 从Subject中获取登录用户信息
        Object principal = subject.getPrincipal();
        // 获取用户名
        String username = "";
        if (principal instanceof org.jeecg.common.system.vo.LoginUser) {
            username = ((org.jeecg.common.system.vo.LoginUser) principal).getUsername();
        } else if (principal instanceof String) {
            username = (String) principal;
        }
        log.warn("用户 {} 试图访问url被拦截 => {}", username, url);
        // 返回 false 表示已经处理，例如页面跳转啥的，表示不在走以下的拦截器了（如果还有配置的话）
        return false;
    }

    public void setAdminSuperMode(Boolean adminSuperMode) {
        this.adminSuperMode = adminSuperMode;
    }


}