package org.jeecg.config.vo;

/**
 * @Description: TODO
 * @author: scott
 * @date: 2022年01月21日 14:23
 */
public class Shiro {
    private String excludeUrls = "";
    
    /**
     * 是否启用管理员超级模式，开启后可跳过接口鉴权
     */
    private Boolean adminSuperMode = false;

    public String getExcludeUrls() {
        return excludeUrls;
    }

    public void setExcludeUrls(String excludeUrls) {
        this.excludeUrls = excludeUrls;
    }
    
    public Boolean getAdminSuperMode() {
        return adminSuperMode;
    }
    
    public void setAdminSuperMode(Boolean adminSuperMode) {
        this.adminSuperMode = adminSuperMode;
    }
}
