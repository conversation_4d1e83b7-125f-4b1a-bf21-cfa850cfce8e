package org.jeecg.config.shiro;

import org.apache.shiro.authz.permission.PermissionResolver;
import org.apache.shiro.realm.Realm;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.jeecg.config.shiro.permission.RegexPermissionResolver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;

/**
 * Shiro权限配置
 * 注册自定义的权限解析器，支持动态URL模式匹配
 */
@Configuration
public class ShiroPermissionConfig implements ApplicationListener<ContextRefreshedEvent> {
    private static final Logger log = LoggerFactory.getLogger(ShiroPermissionConfig.class);

    @Autowired
    private ApplicationContext applicationContext;
    
    /**
     * 自定义的权限解析器
     */
    @Bean
    public PermissionResolver permissionResolver() {
        log.info("创建自定义权限解析器");
        return new RegexPermissionResolver();
    }
    
    /**
     * 创建一个Bean后处理器，用于在SecurityManager初始化后设置PermissionResolver
     * 这种方式可以避免循环依赖问题
     */
    @Bean
    public BeanPostProcessor shiroPermissionBeanPostProcessor(final PermissionResolver permissionResolver) {
        log.info("注册Shiro权限解析器后处理器");
        return new BeanPostProcessor() {
            @Override
            public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
                // 找到DefaultWebSecurityManager后，设置PermissionResolver到每个AuthorizingRealm
                if (bean instanceof DefaultWebSecurityManager) {
                    DefaultWebSecurityManager securityManager = (DefaultWebSecurityManager) bean;
                    log.info("检测到SecurityManager，开始配置权限解析器");
                    
                    // 为每个Realm设置自定义的权限解析器
                    for (Realm realm : securityManager.getRealms()) {
                        if (realm instanceof org.apache.shiro.realm.AuthorizingRealm) {
                            org.apache.shiro.realm.AuthorizingRealm authRealm = 
                                (org.apache.shiro.realm.AuthorizingRealm) realm;
                            
                            authRealm.setPermissionResolver(permissionResolver);
                            log.info("为Realm [{}] 设置自定义权限解析器", realm.getClass().getName());
                        }
                    }
                }
                return bean;
            }
        };
    }
    
    /**
     * 在容器刷新后，确保所有Realm都已配置权限解析器
     */
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        log.info("应用上下文刷新，检查Realm权限解析器配置");
        
        try {
            // 获取SecurityManager
            DefaultWebSecurityManager securityManager = applicationContext.getBean(DefaultWebSecurityManager.class);
            PermissionResolver permissionResolver = applicationContext.getBean(PermissionResolver.class);
            
            log.info("SecurityManager类型: {}", securityManager.getClass().getName());
            log.info("权限解析器类型: {}", permissionResolver.getClass().getName());
            
            // 应用启动后再次确认所有Realm均已配置正确的权限解析器
            for (Realm realm : securityManager.getRealms()) {
                if (realm instanceof org.apache.shiro.realm.AuthorizingRealm) {
                    org.apache.shiro.realm.AuthorizingRealm authRealm = 
                        (org.apache.shiro.realm.AuthorizingRealm) realm;
                    
                    // 确保使用自定义权限解析器
                    if (!(authRealm.getPermissionResolver() instanceof RegexPermissionResolver)) {
                        authRealm.setPermissionResolver(permissionResolver);
                        log.info("修正：为Realm [{}] 设置权限解析器", realm.getClass().getName());
                    } else {
                        log.info("确认：Realm [{}] 已使用RegexPermissionResolver", realm.getClass().getName());
                    }
                }
            }
            
            log.info("权限解析器配置检查完成");
        } catch (Exception e) {
            log.error("检查权限解析器配置时出错: {}", e.getMessage());
        }
    }
} 