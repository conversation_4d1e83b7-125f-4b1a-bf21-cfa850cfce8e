package org.jeecg.common.util;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年05月05日 14:11
 */
public class Ognl {
    public static boolean isEmpty (Object o) throws IllegalArgumentException {
        return BeanUtils.isEmpty(o);
    }

    public static boolean isNotEmpty (Object o) {
        return !isEmpty(o);
    }

    public static boolean isNotEmpty (Long o) {
        return !isEmpty(o);
    }

    public static boolean isNumber (Object o) {
        return BeanUtils.isNumber(o);
    }
}
