package org.jeecg.common.constant.enums;

import lombok.Getter;

@Getter
public enum ErrorCodeEnum {

    SQL_INJECTION_DETECTED("1001", "检测到SQL注入风险"),
    SQL_SHOW_TABLES_DETECTED("1002", "检测到SHOW TABLES风险"),
    SERVER_ERROR("5000", "服务器异常"),
    ILLEGAL_REQUEST("4001", "非法请求");

    // 获取错误码
    private final String code;  // 错误码
    // 获取错误描述
    private final String message;  // 错误描述

    ErrorCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    // 根据错误码获取对应的错误描述
    public static String getMessageByCode(String code) {
        for (ErrorCodeEnum error : ErrorCodeEnum.values()) {
            if (error.getCode().equals(code)) {
                return error.getMessage();
            }
        }
        return ErrorCodeEnum.SQL_INJECTION_DETECTED.getMessage();
    }
}

