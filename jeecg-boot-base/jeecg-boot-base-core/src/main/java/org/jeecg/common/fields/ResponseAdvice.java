package org.jeecg.common.fields;

import org.jeecg.common.api.vo.Result;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

// 在统一响应处理器中调用（如ResponseBodyAdvice实现类）
@ControllerAdvice
public class ResponseAdvice implements ResponseBodyAdvice<Result> {
    private static final Map<String, Set<String>> KEEP_FIELDS = new ConcurrentHashMap<>();

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 仅处理返回类型为 Result 且带有 @FieldFilter 注解的方法
        return returnType.getParameterType().isAssignableFrom(Result.class)
                && (returnType.hasMethodAnnotation(FieldFilter.class)
                || returnType.getContainingClass().isAnnotationPresent(FieldFilter.class));
    }

    @Override
    public Result beforeBodyWrite(Result result, @NotNull MethodParameter returnType, @NotNull MediaType mediaType,
                                  @NotNull Class<? extends HttpMessageConverter<?>> converterType,
                                  @NotNull ServerHttpRequest request, @NotNull ServerHttpResponse response) {
        // 获取当前接口需要保留的字段（可从注解/配置读取）
        return DataFieldFilterUtil.filterFields(result,  getKeepFields(returnType));
    }

    private Set<String> getKeepFields(MethodParameter returnType) {
        if (returnType.getMethod() != null) {
            return KEEP_FIELDS.computeIfAbsent(returnType.getMethod().toGenericString(), key -> {
                FieldFilter methodAnnotation = returnType.getMethodAnnotation(FieldFilter.class);
                if (methodAnnotation != null) {
                    return new HashSet<>(Arrays.asList(methodAnnotation.value()));
                }
                return null;
            });
        } else {
            FieldFilter methodAnnotation = returnType.getMethodAnnotation(FieldFilter.class);
            if (methodAnnotation != null) {
                return new HashSet<>(Arrays.asList(methodAnnotation.value()));
            }
            return null;
        }
    }
}