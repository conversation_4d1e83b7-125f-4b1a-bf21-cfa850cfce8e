package org.jeecg.common.util.poi;

import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.annotation.ExcelTarget;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.jeecgframework.poi.excel.export.ExcelExportServer;
import org.jeecgframework.poi.excel.export.styler.IExcelExportStyler;
import org.jeecgframework.poi.exception.excel.ExcelExportException;
import org.jeecgframework.poi.exception.excel.enums.ExcelExportEnum;
import org.jeecgframework.poi.util.PoiPublicUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.*;

public class CustomExcelExportServer extends ExcelExportServer {
    private int MAX_NUM = 60000;

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomExcelExportServer.class);

    public void createSheet(Workbook workbook, ExportParams entity, Class<?> pojoClass, Collection<?> dataSet, String[] exportFields) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Excel export start ,class is {}", pojoClass);
            LOGGER.debug("Excel version is {}", entity.getType().equals(ExcelType.HSSF) ? "03" : "07");
        }

        if (workbook != null && entity != null && pojoClass != null && dataSet != null) {
            super.type = entity.getType();
            if (this.type.equals(ExcelType.XSSF)) {
                this.MAX_NUM = 1000000;
            }

            Sheet sheet = null;

            try {
                sheet = workbook.createSheet(entity.getSheetName());
            } catch (Exception var19) {
                sheet = workbook.createSheet();
            }

            try {
                this.dataHanlder = entity.getDataHanlder();
                if (this.dataHanlder != null) {
                    this.needHanlderList = Arrays.asList(this.dataHanlder.getNeedHandlerFields());
                }

                this.setExcelExportStyler((IExcelExportStyler) entity.getStyle().getConstructor(Workbook.class).newInstance(workbook));
                Drawing patriarch = sheet.createDrawingPatriarch();
                List<ExcelExportEntity> excelParams = new ArrayList();
                if (entity.isAddIndex()) {
                    excelParams.add(this.indexExcelEntity(entity));
                }

                Field[] fileds = PoiPublicUtil.getClassFields(pojoClass);
                if (exportFields != null) {

                    List<Field> listFields = new ArrayList<>(Arrays.asList(fileds));
                    List<Field> list = new ArrayList<>();

                    for (int i = 0; i < exportFields.length; ++i) {
                        final String name = exportFields[i];
                        Optional<Field> first = listFields.stream().filter(f -> f.getName().equals(name)).findFirst();
                        first.ifPresent(list::add);
                    }

                    if (list.size() > 0) {
                        fileds = list.toArray(new Field[0]);
                    } else {
                        fileds = null;
                    }
                }

                ExcelTarget etarget = (ExcelTarget) pojoClass.getAnnotation(ExcelTarget.class);
                String targetId = etarget == null ? null : etarget.value();
                this.getAllExcelField(entity.getExclusions(), targetId, fileds, excelParams, pojoClass, (List) null);
                this.reConfigExcelExportParams(excelParams, entity);
                int index = entity.isCreateHeadRows() ? this.createHeaderAndTitle(entity, sheet, workbook, excelParams) : 0;
                this.setCellWith(excelParams, sheet);
                this.setColumnHidden(excelParams, sheet);
                short rowHeight = this.getRowHeight(excelParams);
                this.setCurrentIndex(1);
                Iterator<?> its = dataSet.iterator();
                List<Object> tempList = new ArrayList();

                while (its.hasNext()) {
                    Object t = its.next();
                    index += this.createCells(patriarch, index, t, excelParams, sheet, workbook, rowHeight);
                    tempList.add(t);
                    if (index >= this.MAX_NUM) {
                        break;
                    }
                }

                this.mergeCells(sheet, excelParams, index);
                if (entity.getFreezeCol() != 0) {
                    sheet.createFreezePane(entity.getFreezeCol(), 0, entity.getFreezeCol(), 0);
                }

                its = dataSet.iterator();
                int i = 0;

                for (int le = tempList.size(); i < le; ++i) {
                    its.next();
                    its.remove();
                }

                this.addStatisticsRow(this.getExcelExportStyler().getStyles(true, (ExcelExportEntity) null), sheet);
                if (dataSet.size() > 0) {
                    this.createSheet(workbook, entity, pojoClass, dataSet, exportFields);
                }

            } catch (Exception var20) {
                LOGGER.error(var20.getMessage(), var20);
                throw new ExcelExportException(ExcelExportEnum.EXPORT_ERROR, var20.getCause());
            }
        } else {
            throw new ExcelExportException(ExcelExportEnum.PARAMETER_ERROR);
        }
    }

}
