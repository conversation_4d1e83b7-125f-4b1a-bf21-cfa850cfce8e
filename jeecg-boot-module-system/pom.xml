<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.jeecgframework.boot</groupId>
        <artifactId>jeecg-boot-parent</artifactId>
        <version>3.2.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jeecg-boot-module-system</artifactId>

    <description>船员职业健康与技能培训系统</description>
    <version>1.12.26</version>

    <repositories>
        <repository>
            <id>pomres</id>
            <url>https://releases.aspose.com/java/repo</url>
        </repository>
        <repository>
            <id>aliyun</id>
            <name>aliyun Repository</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>jeecg</id>
            <name>jeecg Repository</name>
            <url>https://maven.jeecg.org/nexus/content/repositories/jeecg</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <dependencies>
        <!-- 更换内嵌tomcat版本 -->
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-annotations-api</artifactId>
            <version>9.0.99</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>9.0.99</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-el</artifactId>
            <version>9.0.99</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-websocket</artifactId>
            <version>9.0.99</version>
        </dependency>

        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-system-local-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cloud.tianai.captcha</groupId>
            <artifactId>tianai-captcha-springboot-starter</artifactId>
            <version>1.4.0.4.mate</version>
        </dependency>

        <dependency>
            <groupId>cloud.tianai.captcha</groupId>
            <artifactId>tianai-captcha-mate</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            <!-- go to https://search.maven.org/search?q=tencentcloud-sdk-java and get the latest version. -->
            <!-- 请到https://search.maven.org/search?q=tencentcloud-sdk-java查询所有版本，最新版本如下 -->
            <version>3.1.978</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/ml.dmlc/xgboost4j-spark -->
        <dependency>
            <groupId>ml.dmlc</groupId>
            <artifactId>xgboost4j_2.12</artifactId>
            <version>1.7.6</version>
        </dependency>

        <!-- 企业微信/钉钉 api -->
        <dependency>
            <groupId>org.jeecgframework</groupId>
            <artifactId>jeewx-api</artifactId>
        </dependency>
        <!-- 积木报表  与下面的有冲突 所以注释掉 hzk 20220917-->
        <!--<dependency>
          <groupId>org.jeecgframework.jimureport</groupId>
          <artifactId>jimureport-spring-boot-starter</artifactId>
          <version>1.5.0-beta</version>
        </dependency>-->

        <dependency>
            <groupId>fr.opensagres.xdocreport</groupId>
            <artifactId>fr.opensagres.poi.xwpf.converter.pdf-gae</artifactId>
            <!--原来的版本是2.0.1 和 poi 4.1.2冲突了-->
            <version>2.0.2</version>
            <exclusions>
                <!--poi-ooxml版本冲突-->
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- DEMO 示例模块 用不到，所以注释了
        <dependency>
          <groupId>org.jeecgframework.boot</groupId>
          <artifactId>jeecg-boot-module-demo</artifactId>
          <version>${jeecgboot.version}</version>
        </dependency>
        -->

        <!--    支付宝支付-->
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.40.44.ALL</version>
        </dependency>
        <!--pdf模板导出使用-->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.10</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>
        <!--aspose-words-->
        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-words</artifactId>
            <version>24.3</version>
            <classifier>jdk17</classifier>
        </dependency>
        <!--jsoup-->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.15.1</version>
        </dependency>

        <!--    微信支付-->
        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-java</artifactId>
            <version>0.2.17</version>
        </dependency>

        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-boot-module-camunda</artifactId>
            <version>${jeecgboot.version}</version>
        </dependency>

        <!--pdf模板导出使用-->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.10</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>
        <!--aspose-words-->
        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-words</artifactId>
            <version>24.3</version>
            <classifier>jdk17</classifier>
        </dependency>
        <!--jsoup-->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.15.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox-app</artifactId>
            <version>2.0.21</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>2.3.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.seeta.sdk</groupId>
            <artifactId>seeta-sdk-platform</artifactId>
            <version>1.2.1</version>
        </dependency>
        <!-- Elasticsearch 8.14.3 所需的依赖 -->
        <dependency>
            <groupId>co.elastic.clients</groupId>
            <artifactId>elasticsearch-java</artifactId>
            <version>8.14.3</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
            <version>8.14.3</version>
        </dependency>
        <dependency>
            <groupId>jakarta.json</groupId>
            <artifactId>jakarta.json-api</artifactId>
            <version>2.0.1</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>training-server-${profile.name}-${project.version}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <!--微服务模式下修改为true,跳过SpringBoot启动打包插件，否则微服务模块无法引用-->
                    <skip>${skip.springboot.maven}</skip>

                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>dll</nonFilteredFileExtension>
                        <nonFilteredFileExtension>properties</nonFilteredFileExtension>
                        <nonFilteredFileExtension>so</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <!-- 添加maven-clean-plugin配置，保留target/tools目录及其中的符号链接 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <!-- 使用更明确的配置方式 -->
                    <excludeDefaultDirectories>true</excludeDefaultDirectories>
                    <filesets>
                        <!-- 明确指定要删除的内容，除了tools目录 -->
                        <fileset>
                            <directory>${project.build.directory}</directory>
                            <includes>
                                <include>**/*</include>
                            </includes>
                            <excludes>
                                <exclude>tools/**</exclude>
                            </excludes>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>

        </plugins>

        <resources>
            <resource>
                <directory>src/main/java</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>
