package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.UserPaper;
import org.jeecg.modules.training.vo.PaperVO;
import org.jeecg.modules.training.vo.TraineeVO;
import org.jeecg.modules.training.vo.UserPaperVO;

import java.util.List;

/**
 * @Description: 试卷库
 * @Author: huazhengkan
 * @Date: 2022-06-15
 * @Version: V1.0
 */
@Mapper
public interface UserPaperMapper extends BaseMapper<UserPaper> {

    /**
     * APP端查询考试列表（统一方法 - 支持基础版本和增强版本）
     *
     * <p>
     * 使用说明：
     *   基础版本：当 departId、memberType、memberLevel 为 null 时，执行基础查询
     *   增强版本：当 departId、memberType、memberLevel 都有值时，执行带权限过滤和排序的查询
     * 功能特性：
     *   根据 iteams_display_order 表进行自定义排序（仅增强版本）
     *   基于会员等级进行权限过滤（仅增强版本）
     *   支持企业会员制和船员会员制两种模式（仅增强版本）
     *   在SQL层面实现权限检查，提升查询效率
     * </p>
     *
     * @param userId 用户ID（船员ID）
     * @param categoryId 分类ID
     * @param departId 部门ID（可选：null=基础版本，有值=增强版本）
     * @param memberType 会员类型（可选：null=基础版本，0=船员会员模式，1=企业会员模式）
     * @param memberLevel 会员等级（可选：null=基础版本，0=无有效套餐，1=基础版，2=标准版，3=尊享版）
     * @return 考试列表
     */
    List<PaperVO> findPapers(@Param("userId") String userId, @Param("categoryId") Long categoryId,
                            @Param("departId") String departId, @Param("memberType") Integer memberType,
                            @Param("memberLevel") Short memberLevel);

    Integer updateExamResult(@Param("id") Long id, @Param("success") Integer success, @Param("fail") Integer fail, @Param("duration") Long duration);

    /**
     * 处理告警
     *
     * @param now
     */
    void processWarnStatus(@Param("now") String now);

    /**
     * 处理过期
     *
     * @param now
     */
    void processExpiredStatus(@Param("now") String now);

    /**
     * 找出按paper规则，应该要推送，但还没有推送的组合
     *
     * @return
     */
    List<UserPaper> findNotPushCombination();

    /**
     * 找出需要推给这些用户的
     *
     * @param userIds
     * @return
     */
    List<UserPaper> findUserPush(@Param("userIds") String userIds);

    /**
     * 找出指定考试需要推给的人员
     *
     * @param paperId
     * @return
     */
    List<UserPaper> findPaperPush(@Param("paperId") Long paperId);

    IPage<UserPaperVO> listPages(Page<UserPaperVO> page, @Param("ew") QueryWrapper<UserPaperVO> queryWrapper);

    IPage<TraineeVO> listExamWarnList(Page<TraineeVO> page, @Param("ew") QueryWrapper<TraineeVO> queryWrapper);

    void fixPaperPushStatus(@Param("paperId") Long paperId);

    UserPaper getTop(@Param("ew") LambdaQueryWrapper<UserPaper> queryWrapper);

    int resetUserPaper(@Param("userId") String userId);

    List<PaperVO> findPaperList(@Param("userId") String userId);
}
