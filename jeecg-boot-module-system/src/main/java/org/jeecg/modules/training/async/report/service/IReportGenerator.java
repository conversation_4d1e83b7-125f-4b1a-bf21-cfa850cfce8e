package org.jeecg.modules.training.async.report.service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import org.jeecg.modules.training.async.report.model.DetailBaseVO;

/**
 * 报告处理接口
 * 整合数据获取和报告生成的功能
 * 遵循单一职责原则，将报告相关的操作统一管理
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface IReportGenerator {
    
    /** 报告模板在dataRoot/下的子目录名称 */
    String TEMPLATE_BASE_DIR = "Reports" + File.separator + "Templates";
    
    /** 报告模板文件名 */
    String TEMPLATE_FILE_NAME = "ReportTemplate.docx";
    
    /** 公章图片文件名 (可选) */
    // 在线印章生成器: https://tools.kalvinbg.cn/convenience/seal
    String COMPANY_SEAL_FILE_NAME = "CompanySeal.png";
    
    /**
     * 根据ID获取数据
     *
     * @param templateName 报告模板名称
     * @param idStr        数据对象ID
     * @return 报告内容详情
     * @throws RuntimeException 当获取数据失败时抛出
     */
    DetailBaseVO getDetail(String templateName, String idStr);

    /**
     * 生成报告
     * 将成绩信息生成为Word格式报告，并转换为PDF
     *
     * @param templateName 报告模板名称
     * @param detail 报告内容详情
     * @param outputPath 输出文件路径
     * @throws RuntimeException 当报告生成失败时抛出
     */
    void generateReport(String templateName, DetailBaseVO detail, String outputPath);

    /**
     * 读取图片文件
     * 
     * @param imageFile 图片文件
     * @return BufferedImage 读取到的图片对象
     * @throws RuntimeException 当读取图片失败时抛出
     */
    default BufferedImage readImage(File imageFile) {
        try {
            // 通过 ImageIO 读入图片文件，并转换为 BufferedImage 类型
            BufferedImage image = ImageIO.read(imageFile);
            // 检查是否成功读取图片
            if (image == null) {
                throw new RuntimeException("无法读取图像: " + imageFile.getAbsolutePath());
            }
            return image;
        } catch (IOException e) {
            throw new RuntimeException("读取图像文件时发生异常: " + imageFile.getAbsolutePath(), e);
        }
    }
}