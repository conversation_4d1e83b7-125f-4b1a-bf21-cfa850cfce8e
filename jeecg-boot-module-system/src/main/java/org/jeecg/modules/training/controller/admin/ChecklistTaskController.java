package org.jeecg.modules.training.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.camunda.bpm.engine.RepositoryService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.camunda.entity.WorkflowQuery;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.training.entity.ChecklistTask;
import org.jeecg.modules.training.service.IChecklistTaskService;
import org.jeecg.modules.training.vo.ChecklistTaskVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Arrays;

/**
 * @Description: 许可证和检查实例主表
 * @Author: jeecg-boot
 * @Date: 2022-10-23
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "许可证和检查实例主表")
@RestController
@RequestMapping("/adm/checkTask")
public class ChecklistTaskController extends JeecgController<ChecklistTask, IChecklistTaskService> {
    @Autowired
    private IChecklistTaskService checklistTaskService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private ISysUserService sysUserService;

    @Value("${jeecg.path.dataRoot}")
    private String dataRoot;

    /**
     * 分页列表查询
     *
     * @param vo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "许可证和检查实例主表-分页列表查询")
    @ApiOperation(value = "许可证和检查实例主表-分页列表查询", notes = "许可证和检查实例主表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ChecklistTaskVO vo,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }
        QueryWrapper<ChecklistTaskVO> queryWrapper = QueryGenerator.initQueryWrapper(vo, req.getParameterMap());
        Page<ChecklistTaskVO> page = new Page<>(pageNo, pageSize);

        IPage<ChecklistTaskVO> pageList = checklistTaskService.findPages(page, loginUser.getId(), null, null, queryWrapper);

        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param checklistTask
     * @return
     */
    @AutoLog(value = "许可证和检查实例主表-添加")
    @ApiOperation(value = "许可证和检查实例主表-添加", notes = "许可证和检查实例主表-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ChecklistTask checklistTask) {
        checklistTaskService.save(checklistTask);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param checklistTask
     * @return
     */
    @AutoLog(value = "许可证和检查实例主表-编辑")
    @ApiOperation(value = "许可证和检查实例主表-编辑", notes = "许可证和检查实例主表-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody ChecklistTask checklistTask) {
        checklistTaskService.updateById(checklistTask);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "许可证和检查实例主表-通过id删除")
    @ApiOperation(value = "许可证和检查实例主表-通过id删除", notes = "许可证和检查实例主表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        checklistTaskService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "许可证和检查实例主表-批量删除")
    @ApiOperation(value = "许可证和检查实例主表-批量删除", notes = "许可证和检查实例主表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        this.checklistTaskService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "许可证和检查实例主表-通过id查询")
    @ApiOperation(value = "许可证和检查实例主表-通过id查询", notes = "许可证和检查实例主表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        ChecklistTask checklistTask = checklistTaskService.getById(id);
        return Result.OK(checklistTask);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param checklistTask
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ChecklistTask checklistTask) {
        return super.exportXls(request, checklistTask, ChecklistTask.class, "许可证和检查实例主表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ChecklistTask.class);
    }

    /**
     * 根据配置主表id生成实例
     *
     * @param checklistId
     * @return
     */
   /* @AutoLog(value = "app-根据配置主表id生成实例")
    @ApiOperation(value = "app-根据配置主表id生成实例", notes = "app-根据配置主表id生成实例")
    @RequestMapping(value = "start", method = RequestMethod.POST)
    public Result<?> start(Long checklistId) {
        return checklistTaskService.start(checklistId);
    }
*/
    /**
     * 申请和提交节点
     *
     * @param vo
     * @return
     */
    /*@AutoLog(value = "app-申请和提交节点")
    @ApiOperation(value = "app-申请和提交节点", notes = "app-申请和提交节点")
    @RequestMapping(value = "submit", method = RequestMethod.POST)
    public Result<?> submit(@RequestBody ChecklistTaskVO vo) {
        try {
            checklistTaskService.submit(vo);
            return Result.ok("提交成功！");
        } catch (JeecgBootException ex) {
            ex.printStackTrace();
            return Result.error(ex.getMessage());
        } catch (Exception ex) {
            ex.printStackTrace();
            return Result.error("服务器忙，请稍后再试");
        }
    }*/


    /**
     * 取消申请
     *
     * @param id
     * @param reason 当前流程的任务id
     * @return
     */
    /*@AutoLog(value = "app-取消申请")
    @ApiOperation(value = "app-取消申请", notes = "app-取消申请")
    @RequestMapping(value = "cancel", method = RequestMethod.POST)
    public Result<?> cancel(@RequestParam(name = "id") Long id, @RequestParam(name = "reason", required = false) String reason) {
        try {
            if (reason == null)
                reason = "";
            checklistTaskService.cancel(id, reason);
            return Result.ok("提交成功！");
        } catch (JeecgBootException ex) {
            ex.printStackTrace();
            return Result.error(ex.getMessage());
        } catch (Exception ex) {
            ex.printStackTrace();
            return Result.error("服务器忙，请稍后再试");
        }
    }*/

    /**
     * 拒绝
     *
     * @param id
     * @param reason 原因
     * @return
     */
    @AutoLog(value = "app- 审核不通过，拒绝申请")
    @ApiOperation(value = "app- 审核不通过，拒绝申请", notes = "app- 审核不通过，拒绝申请")
    @RequestMapping(value = "/refuse", method = RequestMethod.POST)
    public Result<?> refuse(@RequestParam(name = "id") Long id, @RequestParam(name = "reason", required = false) String reason) {
        try {
            if (reason == null)
                reason = "";
            checklistTaskService.refuse(id, reason);
            return Result.ok("提交成功！");
        } catch (JeecgBootException ex) {
            ex.printStackTrace();
            return Result.error(ex.getMessage());
        } catch (Exception ex) {
            ex.printStackTrace();
            return Result.error("服务器忙，请稍后再试");
        }
    }

    /**
     * 驳回
     *
     * @param id
     * @param reason 原因
     * @return
     */
    /*@AutoLog(value = "app- 驳回")
    @ApiOperation(value = "app- 驳回", notes = "app- 驳回")
    @RequestMapping(value = "reject", method = RequestMethod.POST)
    public Result<?> reject( @RequestParam(name = "id") Long id, @RequestParam(name = "reason", required = false) String reason) {
        try {
            if (reason == null)
                reason = "";
            checklistTaskService.reject(id, reason);
            return Result.ok("提交成功！");
        } catch (JeecgBootException ex) {
            ex.printStackTrace();
            return Result.error(ex.getMessage());
        } catch (Exception ex) {
            ex.printStackTrace();
            return Result.error("服务器忙，请稍后再试");
        }
    }*/

    /**
     * 申请人撤回
     *
     * @param id
     * @param reason 原因
     * @return
     */
    /*@AutoLog(value = "app- 申请人撤回")
    @ApiOperation(value = "app- 申请人撤回", notes = "app- 申请人撤回")
    @RequestMapping(value = "retrieve", method = RequestMethod.POST)
    public Result<?> retrieve(@RequestParam(name = "id") Long id, @RequestParam(name = "reason", required = false) String reason) {
        try {
            if (reason == null)
                reason = "";
            checklistTaskService.retrieve(id, reason);
            return Result.ok("提交成功！");
        } catch (JeecgBootException ex) {
            ex.printStackTrace();
            return Result.error(ex.getMessage());
        } catch (Exception ex) {
            ex.printStackTrace();
            return Result.error("服务器忙，请稍后再试");
        }
    }*/

    /**
     * 获取指定申请当前用户需要填写的条目
     *
     * @param id
     * @return
     */
    /*@AutoLog(value = "app-获取指定申请当前用户需要填写的条目")
    @ApiOperation(value = "app-获取指定申请当前用户需要填写的条目", notes = "app-获取指定申请当前用户需要填写的条目")
    @RequestMapping(value = "getCurrentTaskItems", method = RequestMethod.POST)
    public Result<?> getCurrentTaskItems(Long id) {
        return checklistTaskService.getCurrentTaskItems(id);
    }
*/
    /**
     * 获取指定申请已经填写填写的条目
     *
     * @param id
     * @return
     */
    /*@AutoLog(value = "app-获取指定申请已经填写填写的条目")
    @ApiOperation(value = "app-获取指定申请已经填写填写的条目", notes = "app-获取指定申请已经填写填写的条目")
    @RequestMapping(value = "getPassedTaskItems", method = RequestMethod.POST)
    public Result<?> getPassedTaskItems(Long id) {
        return checklistTaskService.getPassedTaskItems(id);
    }*/

    /***
     * 获取当前用户可见的申请
     * 需要使用这个地方传的taskid 再提交流程的使用
     * @param categoryId
     * @param categoryType
     * @param pageNo
     * @param pageSize
     * @return
     */
    /*@AutoLog(value = "app-获取当前用户可见的申请")
    @ApiOperation(value = "app-获取当前用户可见的申请", notes = "app-获取当前用户可见的申请")
    @RequestMapping(value = "getMyTasks", method = RequestMethod.POST)
    public Result<?> getMyTasks(Long categoryId, Integer status, Integer categoryType, Integer pageNo, Integer pageSize, String name, String processStatus) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        QueryWrapper<ChecklistTaskVO> queryWrapper = new QueryWrapper<>();

        if (NumberUtil.isNotEmpty(categoryId)) {
            queryWrapper.eq("category_id", categoryId);
        }

        if (NumberUtil.isNotEmpty(categoryType)) {
            queryWrapper.eq("category_type", categoryType);
        }

        if (StringUtils.isNotEmpty(name)) {
            queryWrapper.like("name", name);
        }

        if (StringUtils.isNotEmpty(processStatus)) {
            queryWrapper.in("process_status", (Object[]) processStatus.split(","));
        }

        Page<ChecklistTaskVO> page = new Page<ChecklistTaskVO>(pageNo, pageSize);
        IPage<ChecklistTaskVO> pageList = new Page<>();
        if (NumberUtil.isNotEmpty(status)) {
            switch (status) {
                case 1://待处理（需要我审核的）
                    WorkflowQuery workflowQuery = new WorkflowQuery();
                    if (categoryType != null) {
                        workflowQuery.setCategoryType(categoryType.toString());
                    }
                    workflowQuery.setAssignee(loginUser.getId());
                    workflowQuery.setTaskName(name);
                    workflowQuery.setProcessStatus(processStatus);
                    pageList = checklistTaskService.getTodoTaskList(workflowQuery, pageNo, pageSize);
                    break;
                case 2://已处理（已审核，我审核过的）
                    pageList = checklistTaskService.getDoneTaskList(page, loginUser.getId(), queryWrapper);
                    break;
                case 3://已发起（我申请的）
                    pageList = checklistTaskService.getApplyTaskList(page, loginUser.getId(), queryWrapper);
                    break;
            }
        }

        for (ChecklistTaskVO task : pageList.getRecords()) {
            task.setIsMine(loginUser.getId().equals(task.getCreateBy()));
        }

        return Result.OK(pageList);
    }*/

    /***
     * 查询待办任务
     * @param workflowQuery
     * @return
     */
    /*@AutoLog(value = "app-查询待办任务数量")
    @ApiOperation(value = "app-查询待办任务数量", notes = "app-查询待办任务数量")
    @RequestMapping(value = "getTodoTaskCount", method = RequestMethod.POST)
    public Result<?> getTodoTaskCount(WorkflowQuery workflowQuery) {
        Long count = checklistTaskService.getTodoTaskCount(workflowQuery);
        return Result.OK(count);
    }*/

    /***
     * 查询待办任务
     * @param workflowQuery
     * @param pageNo
     * @param pageSize
     * @return
     */
    @AutoLog(value = "app-查询待办任务")
    @ApiOperation(value = "app-查询待办任务", notes = "app-查询待办任务")
    @RequestMapping(value = "/getTodoTaskList", method = RequestMethod.POST)
    public Result<?> getTodoTaskList(WorkflowQuery workflowQuery, Integer pageNo, Integer pageSize) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        IPage<ChecklistTaskVO> todoTaskList = checklistTaskService.getTodoTaskList(workflowQuery, pageNo, pageSize);
        for (ChecklistTaskVO task : todoTaskList.getRecords()) {
            task.setIsMine(loginUser.getId().equals(task.getCreateBy()));
        }

        return Result.OK(todoTaskList);
    }

    /***
     * 生成指定申请PDF报告
     * @param id
     * @return
     */
    @AutoLog(value = "app-生成指定申请PDF报告")
    @ApiOperation(value = "app-生成指定申请PDF报告", notes = "app-生成指定申请PDF报告")
    @RequestMapping(value = "/createReport", method = RequestMethod.POST)
    public Result<?> createReport(Long id) {
        checklistTaskService.createReport(id);
        return Result.OK("生成成功");
    }

    /***
     * 下载PDF报告
     * @param id
     * @return
     */
    @AutoLog(value = "app-下载PDF报告")
    @ApiOperation(value = "app-下载PDF报告", notes = "app-下载PDF报告")
    @RequestMapping("/downloadReport")
    public void downloadReport(HttpServletRequest request, HttpServletResponse response, Long id) {
        // 其余处理略
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            ChecklistTask task = service.getById(id);
            if (task.getReportFile() == null) {
                checklistTaskService.createReport(id);
                task = service.getById(id);
                //throw new RuntimeException("尚未生成报告..");
            }

            String filePath = dataRoot + File.separator + task.getReportFile();
            File file = new File(filePath);
            if (!file.exists()) {
                response.setStatus(404);
                throw new RuntimeException("文件不存在..");
            }

            // 设置强制下载不打开
            response.setContentType("application/force-download");
            response.addHeader("Content-Disposition", "attachment;fileName=" + new String(task.getName().getBytes("UTF-8"), "iso-8859-1"));
            response.setContentLengthLong(file.length());
            inputStream = new BufferedInputStream(new FileInputStream(filePath));
            outputStream = response.getOutputStream();
            byte[] buf = new byte[4096];
            int len;
            while ((len = inputStream.read(buf)) > 0) {
                outputStream.write(buf, 0, len);
            }
            response.flushBuffer();
        } catch (IOException e) {
            log.error("下载PDF报告失败" + e.getMessage());
            response.setStatus(404);
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    /**
     * image/签名文件上传方法
     *
     * @param id
     * @param type
     * @param request
     * @param response
     * @return
     */
    /*@AutoLog(value = "app-image/签名文件上传方法")
    @ApiOperation(value = "app-image/签名文件上传方法", notes = "app-mage/签名文件上传")
    @PostMapping(value = "/upload")
    public Result<?> upload(Long id, String type, HttpServletRequest request, HttpServletResponse response) {
        Result<String> result = new Result<>();
        String savePath = "";
        StringBuilder bizPath = new StringBuilder();
        bizPath.append("checklist/");
        bizPath.append(id);
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multipartRequest.getFile("file");
        bizPath.append(File.separator);
        bizPath.append(type);
        savePath = CommonUtils.uploadLocal(file, bizPath.toString(), dataRoot);
        if (oConvertUtils.isNotEmpty(savePath)) {
            result.setResult(savePath);
            result.setCode(200);
            result.setMessage("上传成功！");
        } else {
            result.setMessage("上传失败！");
            result.setSuccess(false);
        }
        return result;
    }*/

    /**
     * 预览图片&下载文件
     * 请求地址：http://localhost:8080/checkTask?path=
     *
     * @param request
     * @param response
     */
    @GetMapping(value = "/getImage")
    public void getImage(HttpServletRequest request, HttpServletResponse response) {
        // ISO-8859-1 ==> UTF-8 进行编码转换
        String imgPath = request.getParameter("path");
        if (oConvertUtils.isEmpty(imgPath)) {
            return;
        }
        // 其余处理略
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            imgPath = imgPath.replace("..", "").replace("../", "");
            if (imgPath.endsWith(",")) {
                imgPath = imgPath.substring(0, imgPath.length() - 1);
            }
            String filePath = dataRoot + File.separator + imgPath;
            File file = new File(filePath);
            if (!file.exists()) {
                response.setStatus(404);
                throw new RuntimeException("文件[" + imgPath + "]不存在..");
            }
            // 设置强制下载不打开
            response.setContentType("application/force-download");
            response.addHeader("Content-Disposition", "attachment;fileName=" + new String(file.getName().getBytes("UTF-8"), "iso-8859-1"));
            response.setContentLengthLong(file.length());
            inputStream = new BufferedInputStream(new FileInputStream(filePath));
            outputStream = response.getOutputStream();
            byte[] buf = new byte[4096];
            int len;
            while ((len = inputStream.read(buf)) > 0) {
                outputStream.write(buf, 0, len);
            }
            response.flushBuffer();
        } catch (IOException e) {
            log.error("预览文件失败" + e.getMessage());
            response.setStatus(404);
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }

    }

}
