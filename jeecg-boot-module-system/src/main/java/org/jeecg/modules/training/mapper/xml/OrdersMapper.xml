<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.OrdersMapper">

    <select id="invoiceOrderList" resultType="org.jeecg.modules.training.entity.Orders">
         select *
         from (select * from orders) as o
         ${ew.customSqlSegment};
    </select>
</mapper>
