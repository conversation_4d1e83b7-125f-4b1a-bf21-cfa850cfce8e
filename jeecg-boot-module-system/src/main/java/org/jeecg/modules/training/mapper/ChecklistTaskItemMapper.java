package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.ChecklistTaskItem;
import org.jeecg.modules.training.vo.ChecklistTaskItemVO;

import java.util.List;
import java.util.Map;

/**
 * @Description: 许可证和检查实例子表
 * @Author: jeecg-boot
 * @Date: 2022-10-23
 * @Version: V1.0
 */
public interface ChecklistTaskItemMapper extends BaseMapper<ChecklistTaskItem> {

    /**
     * 根据流程节点状态获取数据
     *
     * @param params
     * @return
     */
    List<ChecklistTaskItemVO> getByNodeStatus(Map<String, Object> params);

    /**
     * 根据流程节点状态获取数据
     *
     * @param params
     * @return
     */
    List<ChecklistTaskItemVO> getCurrentTaskItems(Map<String, Object> params);

    /**
     * 根据taskId和nodeId更新nodeStatus
     *
     * @param params
     * @return
     */
    Boolean updateNodeStatus(Map<String, Object> params);

    /**
     * 根据taskId状态获取数据
     *
     * @param taskId
     * @return
     */
    List<ChecklistTaskItemVO> getByTaskId(@Param("taskId") Long taskId);

}
