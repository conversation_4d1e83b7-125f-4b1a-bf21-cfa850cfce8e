<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.UserPaperMapper">

  <!-- APP端查询考试列表（统一方法 - 支持基础版本和增强版本） -->
  <select id="findPapers" resultType="org.jeecg.modules.training.vo.PaperVO">
    SELECT p.*,
           up.total_times,
           up.retry_times,
           up.done_times,
           up.passed_times,
           up.failed_times,
           up.id AS user_paper_id,
           c.name AS categroy_name,
           up.paper_name
          CASE
              WHEN p.charge_status = 0 THEN 0
              ELSE p.charge_type
          END as required_package_level -- 所需会员等级
    FROM paper p
         LEFT JOIN (
             SELECT up1.*
             FROM user_paper up1
             WHERE user_id = #{userId}
         ) up ON p.id = up.paper_id
         LEFT JOIN (
             SELECT su1.*,
                    IF(DATEDIFF(NOW(), su1.onboard_date) &lt; 30, 0,
                       FLOOR(DATEDIFF(NOW(), su1.onboard_date) / 30)) AS board_time
             FROM trainee su1
             WHERE su1.id = #{userId}
         ) su ON up.user_id = su.id
         LEFT JOIN category c ON p.category_id = c.id
         <!-- 增强版本的额外表关联 -->
         <if test="departId != null and memberType != null and memberLevel != null">
             -- 关联iteams_display_order表获取显示顺序
             LEFT JOIN iteams_display_order ido ON ido.member_package_lev = #{memberLevel}
                 AND ido.iteam_package_lev = CASE
                     WHEN p.charge_status = 0 THEN 0
                     ELSE p.charge_type
                 END
         </if>
    WHERE up.del_flag = 0
      AND c.del_flag = 0
      AND (p.auto_expire IS NULL OR p.auto_expire = 0 OR (up.post = su.post))
      AND (up.status = 0 OR up.status = 1)
      AND (up.board_time IS NULL OR up.board_time &lt;= su.board_time)
      AND p.del_flag = 0
      AND p.disabled = FALSE
      AND p.category_id = #{categoryId}
      AND (p.dep_route = su.dep_route)
      <!-- 增强版本的权限过滤逻辑 -->
      <if test="departId != null and memberType != null and memberLevel != null">
          -- 企业会员制权限过滤逻辑
          <if test="memberType == 1">
              AND (
                  -- 免费资料，所有人都可以访问
                  p.charge_status = 0
                  -- 或者用户套餐等级满足资料所需等级
                  OR (p.charge_status = 1 AND #{memberLevel} >= p.charge_type)
                  -- 或者已单独购买（企业会员模式检查depart_ordered_iteams表）
                  OR EXISTS (
                      SELECT 1 FROM depart_ordered_iteams doi
                      WHERE doi.depart_id = #{departId}
                        AND doi.categorie_type = 2
                        AND doi.iteam_id = p.share_id
                        AND p.share_id IS NOT NULL
                  )
              )
          </if>
          -- 船员会员制不作过滤
      </if>
    <!-- 排序逻辑：增强版本优先按display_order排序，基础版本按原有逻辑排序 -->
    <choose>
        <when test="departId != null and memberType != null and memberLevel != null">
            -- 增强版本：按照iteams_display_order表中定义的顺序排序，然后按类型和创建时间排序
            ORDER BY COALESCE(ido.sequence, 999) ASC, p.type DESC, up.create_time DESC
        </when>
        <otherwise>
            -- 基础版本：按原有逻辑排序
            ORDER BY p.type DESC, up.create_time DESC
        </otherwise>
    </choose>
  </select>


  <select id="listPages" resultType="org.jeecg.modules.training.vo.UserPaperVO">
    SELECT *
    FROM (SELECT up.*,
                 su.realname AS user_name,
                 t.team_name AS team_name,
                 t1.team_name AS team_name_old,
                 su.phone    AS phone,
                 su.post        AS user_post,
                 p.type      AS paper_type,
                 c.name      AS category_name,
                 sd.route    AS user_dep_route,
                 sd.depart_name
          FROM user_paper up
               INNER JOIN paper p ON p.id = up.paper_id
               INNER JOIN Trainee su ON su.id = up.user_id
               INNER JOIN category c ON c.id = p.category_id
               INNER JOIN sys_trainee_depart std ON std.trainee_id = up.user_id
               INNER JOIN sys_depart sd ON std.dep_id = sd.id
               LEFT JOIN team t ON t.id = su.team_id
               LEFT JOIN team t1 ON t1.id = up.team_id
          WHERE up.del_flag = 0
            AND su.del_flag = 0
            AND c.del_flag = 0
            AND su.status = 1
            AND (p.auto_expire IS NULL OR p.auto_expire = 0 OR (up.post = su.post))
            AND p.del_flag = 0
            AND p.disabled = FALSE) AS user_paper
      ${ew.customSqlSegment}
  </select>

  <!--查询需要考试的人员信息-->
  <select id="listExamWarnList" resultType="org.jeecg.modules.training.vo.TraineeVO">
      SELECT *
      FROM (SELECT up.id,
                   up.exam_results,
                   username,
                   realname,
                   onboard_date,
                   identity_card,
                   phone,
                   su.org_code,
                   su.status,
                   su.del_flag,
                   work_no,
                   su.post,
                   su.create_by,
                   su.create_time,
                   su.update_by,
                   su.update_time,
                   up.dep_route,
                   user_identity,
                   depart_ids,
                   group_info,
                   up.id          AS user_paper_id,
                   up.paper_name  AS paper_name,
                   up.total_times AS total_times,
                   p.id           AS paper_id,
                   sd.route       AS route,
                   t.team_name    AS team_name,
                   c.name         AS category_name,
                   up.board_time  AS paper_time,
                   up.status AS user_paper_status
            FROM user_paper up
                     INNER JOIN paper p ON p.id = up.paper_id
                     INNER JOIN trainee su ON su.id = up.user_id
                     INNER JOIN category c ON c.id = p.category_id
                     LEFT JOIN team t ON su.team_id = t.id
                     LEFT JOIN sys_trainee_depart std ON su.id = std.trainee_id
                     LEFT JOIN sys_depart sd ON std.dep_id = sd.id
            WHERE up.del_flag = 0
              AND su.del_flag = 0
              AND su.status = 1
              AND c.del_flag = 0
              AND (p.auto_expire IS NULL OR p.auto_expire = 0 OR (up.post = su.post))
              AND p.del_flag = 0
              AND p.disabled = FALSE
              AND (up.status = 1 OR up.exam_results = 1)) trainee
      ${ew.customSqlSegment}
  </select>


  <update id="updateExamResult">
    UPDATE user_paper up LEFT JOIN user_paper_sync ss
      ON ss.id = up.id
    SET up.done_times     = done_times + 1,
        up.passed_times   = passed_times + ${success},
        up.failed_times   = failed_times + ${fail},
        up.total_duration = total_duration + ${duration},
            up.update_time = now(),
        ss.states          = '{}'
    WHERE up.id = ${id}
  </update>

  <update id="processWarnStatus">
    UPDATE user_paper up LEFT JOIN user_paper_sync ss
        ON ss.id = up.id
    SET up.status = 1,
        ss.states  = '{}'
    WHERE up.warn_time IS NOT NULL
      AND DATEDIFF(up.warn_time, #{now}) &lt;= 0
      AND up.del_flag = 0
      AND up.status = 0

  </update>

  <update id="processExpiredStatus">
      UPDATE user_paper up LEFT JOIN user_paper_sync ss
          ON ss.id = up.id
      SET up.status = 3,
          ss.states = '{}'
      WHERE up.end_time IS NOT NULL
        AND DATEDIFF(up.end_time, #{now}) &lt;= 0
        AND up.del_flag = 0
        AND (up.status = 0 OR up.status = 1)

  </update>
  <update id="fixPaperPushStatus">
    UPDATE user_paper up LEFT JOIN user_paper_sync ss
        ON ss.id = up.id
    SET up.status = 2,
        ss.states = '{}'
    WHERE up.status &lt; 2
      AND (up.total_times - up.passed_times) * (up.retry_times + 1) - up.failed_times &lt;= 0
      AND up.total_times > 0
      AND up.paper_id = ${paperId}
  </update>

  <select id="findNotPushCombination" resultType="org.jeecg.modules.training.entity.UserPaper">
    SELECT su.id AS user_id,
           p.id  AS paper_id,
           p.dep_route,
           su.board_time
    FROM (SELECT su2.id,
                 su2.post,
                 su2.team_id,
                 su2.group_info,
                 IF(bt &lt; 30, 0, FLOOR(bt / 30))                                  AS board_time,
                 IF(bt &lt; 30, '上船前', CONCAT('上船后', FLOOR(bt / 30), '个月')) AS time_factor,
                 sd.route
          FROM (SELECT su1.id,
                       su1.post,
                       su1.team_id,
                       su1.group_info,
                       DATEDIFF(NOW(), su1.onboard_date) AS bt
                FROM trainee su1
                WHERE su1.del_flag = 0) AS su2
               LEFT JOIN sys_trainee_depart std ON su2.id = std.trainee_id
               LEFT JOIN sys_depart sd ON std.dep_id = sd.id) AS su,
         paper p,
         category c
    WHERE p.del_flag = 0
      AND p.category_id = c.id
      AND c.del_flag = 0
      AND p.disabled = FALSE
      AND p.auto_push = TRUE
      AND (su.route = p.dep_route)
      AND (
        (p.time_factor IS NULL
          OR p.time_factor = ''
          OR FIND_IN_SET(su.time_factor, p.time_factor))
        AND
        (p.post_factor IS NULL
          OR p.post_factor = ''
          OR FIND_IN_SET(su.post, p.post_factor))
      )
      AND NOT EXISTS(SELECT 1
                     FROM user_paper up
                     WHERE up.user_id = su.id
                       AND up.paper_id = p.id
                       AND (su.route = up.dep_route)
                       AND up.del_flag = 0
                       AND (p.time_factor IS NULL OR p.time_factor = '' OR up.board_time = su.board_time))

  </select>

  <select id="findUserPush" resultType="org.jeecg.modules.training.entity.UserPaper">
    SELECT DISTINCT su.id AS user_id,
                    p.id  AS paper_id,
                    p.dep_route,
                    su.board_time
    FROM (SELECT su2.id,
                 su2.post,
                 su2.group_info,
                 IF(bt &lt; 30, 0, FLOOR(bt / 30))            AS board_time,
                 IF(bt &lt; 30, '上船前',
                    CONCAT('上船后', FLOOR(bt / 30), '个月')) AS time_factor,
                 sd.route
          FROM (SELECT su1.id,
                       su1.post,
                       su1.group_info,
                       DATEDIFF(NOW(), su1.onboard_date) AS bt
                FROM trainee su1
                WHERE su1.del_flag = 0
                      AND su1.status = 1
                  AND su1.id IN ${userIds}) AS su2
               LEFT JOIN sys_trainee_depart std ON su2.id = std.trainee_id
               LEFT JOIN sys_depart sd ON std.dep_id = sd.id) AS su,
         paper p
    WHERE p.del_flag = 0
      AND p.disabled = FALSE
      AND p.auto_push = TRUE
      AND (su.route = p.dep_route)
      AND (
        (p.time_factor IS NULL
          OR p.time_factor = ''
          OR FIND_IN_SET(su.time_factor, p.time_factor))
        AND
        (p.post_factor IS NULL
          OR p.post_factor = ''
          OR FIND_IN_SET(su.post, p.post_factor))
      )
  </select>

  <select id="findPaperPush" resultType="org.jeecg.modules.training.entity.UserPaper">

    SELECT DISTINCT su.id AS user_id,
                    p.id  AS paper_id,
                    p.dep_route,
                    su.board_time
    FROM (SELECT su2.id,
                 su2.post,
                 su2.group_info,
                 sd.route,
                 IF(bt &lt; 30, 0, FLOOR(bt / 30))            AS board_time,
                 IF(bt &lt; 30, '上船前',
                    CONCAT('上船后', FLOOR(bt / 30), '个月')) AS time_factor
          FROM (SELECT su1.id,
                       su1.post,
                       su1.group_info,
                       DATEDIFF(NOW(), su1.onboard_date) AS bt
                FROM trainee su1
                WHERE su1.del_flag = 0 AND su1.status = 1) AS su2
               LEFT JOIN sys_trainee_depart std ON su2.id = std.trainee_id
               LEFT JOIN sys_depart sd ON std.dep_id = sd.id) AS su,
         paper p
    WHERE p.del_flag = 0
      AND p.disabled = FALSE
      AND p.auto_push = TRUE
      AND p.id = #{paperId}
      AND (su.route = p.dep_route)
      AND (
        (p.time_factor IS NULL
          OR p.time_factor = ''
          OR FIND_IN_SET(su.time_factor, p.time_factor))
        AND
        (p.post_factor IS NULL
          OR p.post_factor = ''
          OR FIND_IN_SET(su.post, p.post_factor))
      )
  </select>

  <select id="getTop" resultType="org.jeecg.modules.training.entity.UserPaper">
    SELECT *
    FROM user_paper ${ew.customSqlSegment}
    LIMIT 1
  </select>
  <!--    -->
  <update id="resetUserPaper">
    UPDATE user_paper up, paper p
    SET up.del_flag = 1
    WHERE up.user_id = #{userId}
      AND up.del_flag = 0
      AND up.paper_id = p.id
      AND p.auto_expire
      AND (p.keep_duration = 0 OR (timestampdiff(day, up.update_time, now()) > p.keep_duration))
  </update>


  <select id="findPaperList" resultType="org.jeecg.modules.training.vo.PaperVO">
    SELECT p.*,
           up.total_times,
           up.retry_times,
           up.done_times,
           up.passed_times,
           up.failed_times,
           up.id  AS user_paper_id,
           c.name AS categroy_name,
           up.paper_name
    FROM paper p
         LEFT JOIN (SELECT up1.*
                    FROM user_paper up1
                    WHERE user_id = #{userId}) up ON p.id = up.paper_id
         LEFT JOIN (SELECT su1.*,
                           IF(DATEDIFF(NOW(), su1.onboard_date) &lt; 30, 0,
                              FLOOR(DATEDIFF(NOW(), su1.onboard_date) / 30)) AS board_time
                    FROM trainee su1
                    WHERE su1.id = #{userId}) su ON up.user_id = su.id
         LEFT JOIN category c ON p.category_id = c.id
    WHERE up.del_flag = 0
      AND c.del_flag = 0
      AND p.del_flag = 0
      AND (p.auto_expire IS NULL OR p.auto_expire = 0 OR (up.post = su.post))
      AND (up.status = 0 OR up.status = 1)
      AND (up.board_time IS NULL OR up.board_time &lt;= su.board_time)
      AND p.disabled = FALSE
      AND p.type = 2
    ORDER BY p.type DESC, up.create_time DESC
  </select>
</mapper>
