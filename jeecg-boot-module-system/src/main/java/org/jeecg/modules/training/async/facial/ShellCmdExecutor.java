package org.jeecg.modules.training.async.facial;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.training.async.materials.ProcessKiller;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.ArrayList;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;

/**
 * A 'shell' process wrapper.
 *
 * <AUTHOR>
 */
public class ShellCmdExecutor {
    private final static Logger LOG = LoggerFactory.getLogger(ShellCmdExecutor.class);

    /**
     * The path of the shell executable.
     */
    private String shellExecutablePath;

    /**
     * Arguments for the executable.
     */
    private ArrayList args = new ArrayList();

    /**
     * The process representing the 'shell' execution.
     */
    private Process process = null;

    /**
     * A process killer to kill the 'shell' process with a shutdown hook, useful
     * if the jvm execution is shutted down during an ongoing encoding process.
     */
    private ProcessKiller processKiller = null;

    /**
     * A stream reading from the 'shell' process standard output channel.
     */
    private InputStream inputStream = null;

    /**
     * A stream writing in the 'shell' process standard input channel.
     */
    private OutputStream outputStream = null;

    private StringBuilder error = null;

    /**
     * It build the executor.
     *
     * @param shellExecutablePath The path of the 'shell' executable.
     */
    public ShellCmdExecutor(String shellExecutablePath) {
        this.shellExecutablePath = shellExecutablePath;
    }

    /**
     * Adds an argument to the 'shell' executable call.
     *
     * @param arg The argument.
     */
    public void addArgument(String arg) {
        args.add(arg);
    }

    /**
     * Adds an argument to the 'shell' executable call.
     *
     * @param arg The argument.
     */
    public void setArgument(int i, String arg) {
        args.set(i, arg);
    }

    /**
     * Executes the 'shell' process with the previous given arguments.
     * and redirectError = true
     *
     * @throws IOException If the process call fails.
     */
    public void execute() throws IOException {
        execute(true);
    }

    public void execute(boolean redirectError) throws IOException {
        int argsSize = args.size();
        String[] cmd = new String[argsSize + 1];
        cmd[0] = shellExecutablePath;
        for (int i = 0; i < argsSize; i++) {
            cmd[i + 1] = (String) args.get(i);
        }

        ProcessBuilder pb = new ProcessBuilder(cmd);

        error = null;

        //合并  inputStream 和 errorStream
        pb.redirectErrorStream(redirectError);

        Runtime runtime = Runtime.getRuntime();

        process = pb.start();

        processKiller = new ProcessKiller(process);
        runtime.addShutdownHook(processKiller);

        inputStream = process.getInputStream();
        outputStream = process.getOutputStream();

        if (!redirectError) {
            error = new StringBuilder();
            final BufferedReader errorReader = new BufferedReader(new InputStreamReader(this.process.getErrorStream()));
            new Thread(() -> {
                try {
                    String line;
                    while ((line = errorReader.readLine()) != null)
                        error.append(line).append("\n");
                    errorReader.close();
                } catch (IOException e) {
                    // return on IOException
                }
            }).start();
        }
    }

    /**
     * Returns a stream reading from the 'shell' process standard output channel.
     *
     * @return A stream reading from the 'shell' process standard output channel.
     */
    public InputStream getInputStream() {
        return inputStream;
    }

    /**
     * Returns a stream writing in the 'shell' process standard input channel.
     *
     * @return A stream writing in the 'shell' process standard input channel.
     */
    public OutputStream getOutputStream() {
        return outputStream;
    }

    /**
     * wait for 'shell' execution end.
     */
    public void waitFor() {
        //读取输出，防止卡死
        readStandardInput(null);
        try {
            process.waitFor();
        } catch (Exception ex) {
            LOG.error("EXP", ex);
        }
    }

    /**
     * wait for 'shell' execution end.
     */
    public void waitFor(int time) {
        if (time <= 0) {
            waitFor();
            return;
        }

        //读取输出，防止卡住
        readStandardInput();

        try {
            process.waitFor(time, TimeUnit.MILLISECONDS);
        } catch (Exception ex) {
            LOG.error("EXP", ex);
        }
    }

    /**
     * If there's a 'shell' execution in progress, it kills it.
     */
    public void destroy() {
        try {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Throwable t) {
                    ;
                }
                inputStream = null;
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (Throwable t) {
                    ;
                }
                outputStream = null;
            }
            try {
                if (process != null) {
                    process.destroyForcibly();
                    process = null;
                }
            } catch (Exception ex) {

            }
            if (processKiller != null) {
                Runtime runtime = Runtime.getRuntime();
                runtime.removeShutdownHook(processKiller);
                processKiller = null;
            }
        } catch (Exception ex) {
            LOG.error("EXP", ex);
        }
    }

    /**
     * get input stream reader
     */
    public BufferedReader getInputReader() {
        return new BufferedReader(new InputStreamReader(this.inputStream));
    }

    /**
     * read all standard input
     *
     * @return
     */
    public String readStandardInput() {
        StringBuilder sb = new StringBuilder();
        try {
            BufferedReader reader = this.getInputReader();
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line).append("\n");
            }
        } catch (Exception ex) {
        }
        return sb.toString();

    }

    /**
     * read all standard input
     *
     * @return
     */
    public void readStandardInput(Predicate<String> onLine) {
        try {
            BufferedReader reader = this.getInputReader();
            String line;
            while ((line = reader.readLine()) != null) {
                if (onLine != null && !onLine.test(line))
                    return;
            }
        } catch (Exception ex) {
        }
    }

    /**
     * get input stream reader
     */
    public String getErrorInfo() {
        if (error == null)
            throw new RuntimeException("not set redirectError to false when call execute(redirectError) ");
        return error.toString();
    }

    @Override
    public String toString() {
        return shellExecutablePath + " " + StringUtils.join(args, " ");
    }

    public boolean isAlive() {
        return process.isAlive();
    }
}
