package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 题库
 * @Author: jeecg-boot
 * @Date: 2022-05-04
 * @Version: V1.0
 * autoResultMap=true主要是给获取json字段的时候使用，配合字段属性为object和@TableField(typeHandler = FastjsonTypeHandler.class)
 * @Accessors :在get和set方法时做一些相关设置
 * 属性：1）fluent 默认false,为true时，get和set方法前没有“get”和“set”字
 * 2）chain 默认false，为true时，对应字段的setter方法调用后，会返回当前对象
 */
@Data
@TableName(value = "questions", autoResultMap = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "questions对象", description = "题库")
public class Questions {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键id")
    private Long id;
    /**
     * 分类id
     */
    @Excel(name = "分类id", width = 15)
    @ApiModelProperty(value = "分类id")
    private Long categoryId;
    /**
     * 知识点id
     */
    @Excel(name = "知识点id", width = 15)
    @ApiModelProperty(value = "知识点id")
    private Long knowledgeId;
    /**
     * 题目类型:1.单选;2:多选;3:判断
     */
    @Excel(name = "题目类型:1.单选;2:多选;3:判断;4:填写", width = 15)
    @ApiModelProperty(value = "题目类型:1.单选;2:多选;3:判断;4:填写")
    private Integer type;
    /**
     * 试题标题
     */
    @Excel(name = "试题标题", width = 15)
    @ApiModelProperty(value = "试题标题")
    private String title;
    /**
     * 问题详情描述
     */
    @Excel(name = "问题详情描述", width = 15)
    @ApiModelProperty(value = "问题详情描述")
    private String content;
    /**
     * [{"order":"A","content":"2222222222222","isAnswer":"0错误/1正确","analysis":"整题解析","score":1},{"order":"B","content":"3333333333333","isAnswer":1,"analysis":"整题解析"},{"order":"C","content":"3333333333333","isAnswer":1,"analysis":"整题解析","score":1},{"order":"D","content":"3333333333333","isAnswer":1,"analysis":"整题解析","score":1}]
     */
    @Excel(name = "选项", width = 15)
    @ApiModelProperty(value = "选项")
    /**
     * @TableField(typeHandler = FastjsonTypeHandler.class)
     * private JSONArray imageUrls;
     *
     * @TableField(typeHandler = FastjsonTypeHandler.class)主要给保存json字段时使用
     * 而且mp还支持JSONArray和JSONObject类型的属性
     * .配置映射器和家resultMap注解
     * <result property="options" column="options" javaType="com.alibaba.fastjson.JSONArray" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
     *
     * @Select("select * from test_db where id = #{id}")

     * TbClassNews findById(Long id);
     * */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private Object options;
    /**
     * 分数
     */
    @Excel(name = "分数", width = 15)
    @ApiModelProperty(value = "分数")
    private Integer score;

    /**
     * 分组分套分类等等
     */
    @Excel(name = "分组分套分类等等", width = 15)
    @ApiModelProperty(value = "分组分套分类等等")
    private String mark;

    /**
     * 分数
     */
    @Excel(name = "题号", width = 15)
    @ApiModelProperty(value = "题号")
    private Integer sort;
    /**
     * 删除状态（0，正常，1已删除）
     */
    @Excel(name = "删除状态（0，正常，1已删除）", width = 15)
    @ApiModelProperty(value = "删除状态（0，正常，1已删除）")
    @TableLogic
	private Integer delFlag;
    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @Excel(name = "更新人", width = 15)
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    private String depRoute;

    /**
     * 关联行业id集合
     */
    private String industryIds;

    private String shareMode;

    private Long shareId;
}
