package org.jeecg.modules.training.controller.admin;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.training.service.IStudyLogDailyStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("adm/studyLogDailyStatistics")
public class StudyLogDailyStatisticsController {

    @Autowired
    private IStudyLogDailyStatisticsService studyLogDailyStatistics;

    // 获取学习资料被学习次数 统计表专用
    @GetMapping(value = "/getList")
    public Result<?> getList() {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        return Result.OK(studyLogDailyStatistics.getList(loginUser.getDepRoute()));
    }
}
