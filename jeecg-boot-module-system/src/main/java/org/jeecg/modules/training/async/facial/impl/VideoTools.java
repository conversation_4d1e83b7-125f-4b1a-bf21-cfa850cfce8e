package org.jeecg.modules.training.async.facial.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.boot.system.ApplicationHome;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年09月23日 01:40
 */
@Slf4j
public class VideoTools {
    public static String ffmpegFilePath;
    public static String ffprobeFilePath;

    public static void checkTools() {

        if (!StringUtils.isEmpty(ffmpegFilePath))
            return;
        ApplicationHome ah = new ApplicationHome(VideoTools.class);

        //这里代码不要动，如果是Windows，可以尝试重命名ffmpeg.exe为ffmpeg（如果不行就判断一下当前操作系统类型）
        //Windows 的就 加个 .exe
        ffmpegFilePath = ah.getSource().getParentFile().toString() + "/tools/ffmpeg";
        ffprobeFilePath = ah.getSource().getParentFile().toString() + "/tools/ffprobe";

        log.info("debug-" + ffmpegFilePath);
        log.info("debug-" + ffprobeFilePath);

    }
}
