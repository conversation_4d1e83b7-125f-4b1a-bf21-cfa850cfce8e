package org.jeecg.modules.training.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.IdentityService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.task.Task;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.camunda.entity.ActNode;
import org.jeecg.modules.camunda.entity.WorkflowQuery;
import org.jeecg.modules.camunda.entity.WorkflowTaskEntity;
import org.jeecg.modules.camunda.mapper.ActHiTaskInstMapper;
import org.jeecg.modules.camunda.mapper.ActNodeMapper;
import org.jeecg.modules.camunda.service.BpmService;
import org.jeecg.modules.camunda.service.WorkflowDefinitionService;
import org.jeecg.modules.camunda.service.WorkflowTaskService;
import org.jeecg.modules.camunda.service.impl.ActNodeServiceImpl;
import org.jeecg.modules.camunda.vo.ProcessNodeVo;
import org.jeecg.modules.camunda.vo.TaskVo;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.mapper.SysPositionMapper;
import org.jeecg.modules.system.mapper.SysUserMapper;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.training.entity.Checklist;
import org.jeecg.modules.training.entity.ChecklistItem;
import org.jeecg.modules.training.entity.ChecklistTask;
import org.jeecg.modules.training.entity.ChecklistTaskItem;
import org.jeecg.modules.training.mapper.ChecklistItemMapper;
import org.jeecg.modules.training.mapper.ChecklistMapper;
import org.jeecg.modules.training.mapper.ChecklistTaskItemMapper;
import org.jeecg.modules.training.mapper.ChecklistTaskMapper;
import org.jeecg.modules.training.service.IChecklistTaskItemService;
import org.jeecg.modules.training.service.IChecklistTaskService;
import org.jeecg.modules.training.util.CheckListUtil;
import org.jeecg.modules.training.vo.CheckItemOptionVO;
import org.jeecg.modules.training.vo.ChecklistTaskItemVO;
import org.jeecg.modules.training.vo.ChecklistTaskVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 许可证和检查实例主表
 * @Author: hzk
 * @Date: 2022-10-23
 * @Version: V1.0
 */
@Service
public class ChecklistTaskServiceImpl extends ServiceImpl<ChecklistTaskMapper, ChecklistTask> implements IChecklistTaskService {

    @Autowired
    private ChecklistMapper checkMapper;

    @Autowired
    private ChecklistItemMapper checkItemMapper;

    @Autowired
    private ChecklistTaskItemMapper taskItemMapper;

    @Autowired
    private ChecklistTaskMapper taskMapper;

    @Autowired
    private ActNodeMapper actNodeMapper;

    @Autowired
    private ActNodeServiceImpl actNodeService;

    @Autowired
    private SysPositionMapper sysPositionMapper;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private WorkflowDefinitionService workflowDefinitionService;

    @Autowired
    private IChecklistTaskItemService taskItemService;

    @Autowired
    private WorkflowTaskService workflowTaskService;

    @Resource
    private IdentityService identityService;

    @Autowired
    private TaskService taskService;

    @Value("${jeecg.path.dataRoot}")
    private String dataRoot;

    @Autowired
    private ActHiTaskInstMapper actHiTaskInstMapper;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private HistoryService historyService;

    /**
     * 根据配置主表查询实例数据
     *
     * @param checklistIds
     * @return
     */
    public List<ChecklistTask> getByChecklistId(String... checklistIds) {
        return this.baseMapper.getByChecklistId(checklistIds);
    }

    @Override
    public IPage<ChecklistTaskVO> findPages(Page<ChecklistTaskVO> page, String userId, String orgCode, String post, QueryWrapper<ChecklistTaskVO> queryWrapper) {
        return baseMapper.listPapers(page, userId, orgCode, post, queryWrapper);
    }

    @Override
    public Result<?> getCurrentTaskItems(Long checkTaskId) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }
        Map<String, Object> params = Maps.newHashMap();
        ChecklistTask task = this.baseMapper.selectById(checkTaskId);
        List<ChecklistTaskItemVO> taskItemVOs = new ArrayList<>();
        String taskId = null;

        if (task.getProcessId() != null) {
            //流程已经启动

            //获取当前审批人的流程节点
            TaskVo<?> taskVo = workflowTaskService.taskByProIdAndAss(task.getProcessId(), loginUser.getId());
            if (taskVo == null) {
                return Result.OK(taskItemVOs);
            }
            taskId = taskVo.getId();
            params.put("nodeId", taskVo.getTaskDefinitionKey());
        } else {
            //流程没有启动，查第一个节点的数据
            params.put("isFirst", "T");
        }

        params.put("checkTaskId", checkTaskId);
        taskItemVOs = taskItemMapper.getCurrentTaskItems(params);
        for (ChecklistTaskItemVO taskItemVO : taskItemVOs) {
            if (taskItemVO.getAnswers() != null) {
                JSONArray values = JSONArray.parseArray(taskItemVO.getAnswers().toString());
                List<CheckItemOptionVO> options = JSONArray.parseArray(taskItemVO.getItemOptions().toString(), CheckItemOptionVO.class);
                for (int i = 0; i < values.size() && i < options.size(); i++) {
                    if (values.get(i) != null) {
                        options.get(i).setValue(values.get(i).toString());
                    }
                }
                taskItemVO.setOptions(options);
            } else {
                taskItemVO.setOptions(JSONArray.parseArray(taskItemVO.getItemOptions().toString(), CheckItemOptionVO.class));
            }
            taskItemVO.setTaskId(taskId);
        }
        return Result.OK(taskItemVOs);
    }

    @Override
    public Result<?> getPassedTaskItems(Long checkTaskId) {
        List<ChecklistTaskItemVO> taskItemVOs = getTaskItemByNodeStatus(checkTaskId, "passed");
        return Result.OK(taskItemVOs);
    }

    //    @NotNull
    private List<ChecklistTaskItemVO> getTaskItemByNodeStatus(Long checkTaskId, String nodeStatus) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }
        SysUser sysUser = sysUserService.getUserByName(loginUser.getUsername());
        //职务
        String post = sysUser.getPost();
        Map<String, Object> params = Maps.newHashMap();
        params.put("checkTaskId", checkTaskId);
        params.put(nodeStatus, "T");
        params.put("post", post);
        List<ChecklistTaskItemVO> taskItemVOs = taskItemMapper.getByNodeStatus(params);
        //if (CollectionUtils.isEmpty(taskItemVOs)) {
        //     throw new JeecgBootException("当前用户没有填写的数据！");
        //}
        for (ChecklistTaskItemVO taskItemVO : taskItemVOs) {
            if (taskItemVO.getAnswers() != null) {
                JSONArray values = JSONArray.parseArray(taskItemVO.getAnswers().toString());
                List<CheckItemOptionVO> options = JSONArray.parseArray(taskItemVO.getItemOptions().toString(), CheckItemOptionVO.class);
                for (int i = 0; i < values.size() && i < options.size(); i++) {
                    if (values.get(i) != null) {
                        options.get(i).setValue(values.get(i).toString());
                    }
                }
                taskItemVO.setOptions(options);
            } else {
                taskItemVO.setOptions(JSONArray.parseArray(taskItemVO.getItemOptions().toString(), CheckItemOptionVO.class));
            }
        }
        return taskItemVOs;
    }

    public IPage<ChecklistTaskVO> getTodoTaskList(WorkflowQuery workflowQuery, Integer pageNo, Integer pageSize) {
        Page<ChecklistTaskVO> page = new Page<ChecklistTaskVO>(pageNo, pageSize);

        QueryWrapper<WorkflowTaskEntity> queryWrapper = new QueryWrapper<>();
        if (!StringUtils.isBlank(workflowQuery.getAssignee())) {
            queryWrapper.like("assignee", workflowQuery.getAssignee());
        }
        //流程发起人
        if (!StringUtils.isBlank(workflowQuery.getStartUser())) {
            queryWrapper.like("start_user_id", workflowQuery.getStartUser());
        }
        if (!StringUtils.isBlank(workflowQuery.getOwner())) {
            queryWrapper.like("owner", workflowQuery.getOwner());
        }

        if (!StringUtils.isBlank(workflowQuery.getCategoryType())) {
            queryWrapper.in("category_type", (Object[]) workflowQuery.getCategoryType().split(","));
        }

        if (!StringUtils.isBlank(workflowQuery.getTaskName())) {
            queryWrapper.like("task_name", workflowQuery.getTaskName());
        }

        if (!StringUtils.isBlank(workflowQuery.getProcessStatus())) {
            queryWrapper.in("process_status", (Object[]) workflowQuery.getProcessStatus().split(","));
        }

        queryWrapper.orderByDesc("create_time");

        return this.baseMapper.listTodoTaskByPage(page, queryWrapper);
    }

    public IPage<ChecklistTaskVO> getDoneTaskList(Page<ChecklistTaskVO> page, String assignee, QueryWrapper<ChecklistTaskVO> queryWrapper) {
        queryWrapper.eq("assignee", assignee);
        queryWrapper.isNotNull("end_time_");
        queryWrapper.and(qw ->
                qw.eq("delete_reason_", "completed").or().eq("delete_reason_", "refuse").or().eq("delete_reason_", "deleted")
        );
        queryWrapper.orderByDesc("create_time");
        return this.baseMapper.listDoneTaskByPage(page, queryWrapper);
    }

    public IPage<ChecklistTaskVO> getApplyTaskList(Page<ChecklistTaskVO> page, String assignee, QueryWrapper<ChecklistTaskVO> queryWrapper) {
        //流程发起人
        queryWrapper.eq("create_by", assignee);
        queryWrapper.orderByDesc("create_time");
        return this.baseMapper.listPapers(page, null, null, null, queryWrapper);
    }

    @Override
    public Long getTodoTaskCount(WorkflowQuery workflowQuery) {

        QueryWrapper<WorkflowTaskEntity> queryWrapper = new QueryWrapper<>();
        if (!StringUtils.isBlank(workflowQuery.getAssignee())) {
            queryWrapper.like("assignee", workflowQuery.getAssignee());
        }

        if (!StringUtils.isBlank(workflowQuery.getCategoryType())) {
            queryWrapper.in("category_type", (Object[]) workflowQuery.getCategoryType().split(","));
        }

        if (!StringUtils.isBlank(workflowQuery.getTaskName())) {
            queryWrapper.like("name", workflowQuery.getTaskName());
        }

        return this.baseMapper.countTodoTasks(queryWrapper);
    }

    @Override
    public void createReport(Long id) {
        ChecklistTask task = this.baseMapper.selectById(id);
        //        if (!"completed".equals(task.getProcessStatus())) {
        //            throw new JeecgBootException("流程结束的才能生成报告")
        //        }
        Checklist checklist = checkMapper.selectById(task.getChecklistId());
        String templatePath = dataRoot + File.separator + checklist.getTemplateFile();
        StringBuffer outPath = new StringBuffer();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        outPath.append("checklist/reports");
        outPath.append(File.separator);
        outPath.append(format.format(new Date()));
        String savePath = dataRoot + File.separator + outPath.toString();

        File file = new File(savePath);
        if (!file.exists()) {
            file.mkdirs();
        }

        outPath.append(File.separator);
        outPath.append(id);
        outPath.append(".pdf");
        savePath = dataRoot + File.separator + outPath.toString();

        file = new File(savePath);
        if (file.exists()) {
            file.delete();
        }

        try {

            List<ChecklistTaskItemVO> taskItemVOs = taskItemMapper.getByTaskId(id);
            Map<String, Object> params = Maps.newHashMap();
            for (ChecklistTaskItemVO vo : taskItemVOs) {
                List<CheckItemOptionVO> checklistItemOptions = JSONArray.parseArray(vo.getItemOptions().toString(), CheckItemOptionVO.class);
                JSONArray answers = vo.getAnswers() != null ? JSONArray.parseArray(vo.getAnswers().toString()) : new JSONArray();
                for (int i = 0; i < checklistItemOptions.size(); i++) {
                    CheckItemOptionVO checkItemOptionVO = checklistItemOptions.get(i);

                    if (StringUtils.isBlank(checkItemOptionVO.getTplVal()))
                        continue;

                    if (answers.size() <= i) {
                        //理论上说，应该要报错，两个数组必须是等长的才正常
                        continue;
                    }

                    if ("single".equals(checkItemOptionVO.getType()) || "multiple".equals(checkItemOptionVO.getType())) {
                        params.put(checkItemOptionVO.getTplVal(), Boolean.parseBoolean(answers.get(i).toString()));
                    } else if ("image".equals(checkItemOptionVO.getType())) {
                        params.put(checkItemOptionVO.getTplVal(), dataRoot + File.separator + answers.get(i));
                    } else if ("signature".equals(checkItemOptionVO.getType())) {
                        JSONObject val = JSONObject.parseObject(answers.get(i).toString());
                        params.put(checkItemOptionVO.getTplVal(), dataRoot + File.separator + val.getString("path"));
                    } else {
                        //text,date,time,data,time,
                        params.put(checkItemOptionVO.getTplVal(), answers.get(i));
                    }
                }
            }

            //生成报告
            CheckListUtil.wordToPdfByTemplate(templatePath, savePath, params, true);

            //保存报告文件位置
            task.setReportFile(outPath.toString());
            baseMapper.updateById(task);

            //PdfTempUtil.exportPdf(templatePath, params, savePath);
        } catch (Exception e) {
            log.error(id + "生成报告失败：", e);
            throw new JeecgBootException("生成报告失败!", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> start(Long checklistId) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        Checklist checklist = checkMapper.selectById(checklistId);
        if (checklist == null) {
            return Result.error("此申请已经不存在，请选择其他申请");
        }

        LambdaQueryWrapper<ChecklistItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChecklistItem::getChecklistId, checklistId);

        List<ChecklistItem> itemList = checkItemMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(itemList)) {
            log.error("此申请尚未配置完成，没有检查条目:" + checklist.getId() + "," + checklist.getName());
            return Result.error("此申请尚未配置完成，请联系管理员配置！");
        }

        List<ProcessNodeVo> processNodes = workflowDefinitionService.getProcessNode(checklist.getWorkflowId());
        if (processNodes == null || processNodes.isEmpty()) {
            log.error("此申请尚未配置完成，没有流程节点:" + checklist.getId() + "," + checklist.getName());
            return Result.error("此申请尚未配置完成，请联系管理员配置！");
        }

        //设置 固定到人员的 审批人员（通常为岸基人员）
        List<ActNode> actNodes = actNodeService.findByProcDefIdAndType(checklist.getWorkflowId(), 1);
        for (ActNode actNode : actNodes) {
            if (StringUtils.isNotEmpty(actNode.getRelateId())) {
                //用户
                LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(SysUser::getUsername, (Object[]) actNode.getRelateId().split(","));
                queryWrapper.eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0);
                List<SysUser> users = sysUserMapper.selectList(queryWrapper);
                if (users.isEmpty()) {
                    log.error("部门或船只，没有此申请所需要的全部审批人员:" + checklist.getId() + "," + checklist.getName() + "," + actNode.getNodeId() + "," + actNode.getRelateId());
                    return Result.error("您所在的相关部门或船只，没有此申请所需要的全部审批人员，无法申请。");
                }
            } else {
                log.error("此申请审批人员尚未配置完全，没有检查条目:" + checklist.getId() + "," + checklist.getName() + "," + actNode.getNodeId() + "," + actNode.getRelateId());
                return Result.error("此申请审批人员尚未配置完全，请联系管理员配置！");
            }
        }

        //设置 按职务绑定的 审批人员
        actNodes = actNodeService.findByProcDefIdAndType(checklist.getWorkflowId(), 7);
        for (ActNode actNode : actNodes) {
            if (StringUtils.isNotEmpty(actNode.getRelateId())) {

                LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0);
                //当前用户所在船只
                queryWrapper.eq(SysUser::getOrgCode, loginUser.getOrgCode());
                //这些职位
                queryWrapper.in(SysUser::getPost, (Object[]) actNode.getRelateId().split(","));

                //以后可能还要排除特定角色的用户，或者选定特定角色的用户
                List<SysUser> users = sysUserService.list(queryWrapper);
                if (users.isEmpty()) {
                    log.error("部门或船只，没有此申请所需要的全部审批人员:" + checklist.getId() + "," + checklist.getName() + "," + actNode.getNodeId() + "," + actNode.getRelateId());
                    return Result.error("您所在的相关部门或船只，没有此申请所需要的全部审批人员，无法申请。");
                }
            } else {
                log.error("此申请审批人员尚未配置完全，没有检查条目:" + checklist.getId() + "," + checklist.getName() + "," + actNode.getNodeId() + "," + actNode.getRelateId());
                return Result.error("此申请审批人员尚未配置完全，请联系管理员配置！");
            }
        }

        ChecklistTask checklistTask = new ChecklistTask();
        BeanUtils.copyProperties(checklist, checklistTask, "id", "categoryId", "disabled", "templateFile", "postFactor", "delFlag", "createBy", "createTime", "updateBy", "updateTime");
        checklistTask.setChecklistId(checklistId);
        checklistTask.setDelFlag(CommonConstant.DEL_FLAG_0);
        checklistTask.setProcessStatus(CommonConstant.BPM_STATUS_DRAFT);
        checklistTask.setOrgCode(loginUser.getOrgCode());
        checklistTask.setWorkflowId(checklist.getWorkflowId());

        taskMapper.insert(checklistTask);

        //申请节点
        ProcessNodeVo applyNodeVo = processNodes.get(0);
        for (ChecklistItem item : itemList) {
            ChecklistTaskItem taskItem = new ChecklistTaskItem();

            taskItem.setChecklistTaskId(checklistTask.getId());
            taskItem.setChecklistItemId(item.getId());
            taskItem.setTitle(item.getTitle());
            taskItem.setItemOptions(item.getOptions());

            if (applyNodeVo.getId().equals(item.getNodeId())) {
                taskItem.setNodeStatus(1);
            } else {
                taskItem.setNodeStatus(0);
            }

            taskItem.setDelFlag(CommonConstant.DEL_FLAG_0);
            taskItemMapper.insert(taskItem);
        }

        return Result.OK(checklistTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submit(ChecklistTaskVO vo) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        SysUser sysUser = sysUserService.getUserByName(loginUser.getUsername());
        ChecklistTask checklistTask = this.baseMapper.selectById(vo.getId());

        //保存用户填写的内容
        taskItemService.saveEntity(vo.getItems());

        //设置当前用户为操作人
        identityService.setAuthenticatedUserId(sysUser.getId());

        //发起申请的人
        SysUser startUser = sysUserService.getById(checklistTask.getCreateBy());

        //执行审批 （单次提交，肯定只能是一个节点的数据，所以按第一个item来找节点ID即可）
        ChecklistTaskItemVO checklistTaskItemVO = vo.getItems().get(0);
        ChecklistTaskItem taskItem = taskItemService.getById(checklistTaskItemVO.getId());
        ChecklistItem item = checkItemMapper.selectById(taskItem.getChecklistItemId());

        //添加变量（不管是第一次，还是后续提交，都得有完整的变量（至少是尚未完成的节点的变量都得提供））
        Map<String, Object> processVariables = new HashMap<>();

        //设置 固定到人员的 审批人员（通常为岸基人员）
        List<ActNode> actNodes = actNodeService.findByProcDefIdAndType(checklistTask.getWorkflowId(), 1);
        for (ActNode actNode : actNodes) {
            if (StringUtils.isNotEmpty(actNode.getRelateId())) {
                //用户
                LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(SysUser::getUsername, (Object[]) actNode.getRelateId().split(","));
                queryWrapper.eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0);
                List<SysUser> users = sysUserMapper.selectList(queryWrapper);
                if (users.isEmpty())
                    throw new IllegalArgumentException("workflowId:" + checklistTask.getWorkflowId() + ",actNode:" + actNode.getNodeId() + " relateId user " + actNode.getRelateId() + " not found");

                //如果有多人，存在 或签 的时候，设置审批列表
                if (actNode.getMultiple() != null && actNode.getMultiple()) {
                    processVariables.put(actNode.getNodeId() + "_userList", users.stream().map(SysUser::getId).collect(Collectors.toList()));
                } else {
                    //单个审批人
                    processVariables.put(actNode.getNodeId(), users.get(0).getId());
                }
            } else {
                throw new IllegalArgumentException("workflowId:" + checklistTask.getWorkflowId() + ",actNode:" + actNode.getNodeId() + " getRelateId is empty");
            }
        }

        //设置 按职务绑定的 审批人员
        actNodes = actNodeService.findByProcDefIdAndType(checklistTask.getWorkflowId(), 7);
        for (ActNode actNode : actNodes) {
            if (StringUtils.isNotEmpty(actNode.getRelateId())) {

                LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0);
                //当前用户所在船只
                queryWrapper.eq(SysUser::getOrgCode, startUser.getOrgCode());
                //这些职位
                queryWrapper.in(SysUser::getPost, (Object[]) actNode.getRelateId().split(","));

                //以后可能还要排除特定角色的用户，或者选定特定角色的用户
                List<SysUser> users = sysUserService.list(queryWrapper);
                if (users.isEmpty())
                    throw new IllegalArgumentException("workflowId:" + checklistTask.getWorkflowId() + ",actNode:" + actNode.getNodeId() + " not fount users for posts:" + actNode.getRelateId());

                //如果有多人，存在 或签 的时候，设置审批列表
                if (actNode.getMultiple() != null && actNode.getMultiple()) {
                    processVariables.put(actNode.getNodeId() + "_userList", users.stream().map(SysUser::getId).collect(Collectors.toList()));
                } else {
                    //单个审批人
                    processVariables.put(actNode.getNodeId(), users.get(0).getId());
                }
            } else {
                throw new IllegalArgumentException("workflowId:" + checklistTask.getWorkflowId() + ",actNode:" + actNode.getNodeId() + " getRelateId is empty");
            }
        }

        processVariables.put(item.getNodeId() + "_approve", 1);
        if (StringUtils.isEmpty(checklistTask.getProcessId())) {

            //前台配置就覆盖
            if (StringUtils.isNotEmpty(vo.getApproveId())) {
                processVariables.put(item.getNodeId(), vo.getApproveId());
            }

            //启动流程
            ProcessInstance processInstance = workflowTaskService.startProcessInstanceById(checklistTask.getWorkflowId(),
                    checklistTask.getId().toString(),
                    loginUser.getId(),
                    processVariables);

            checklistTask.setProcessId(processInstance.getProcessInstanceId());
            checklistTask.setApplyTime(new Date());

        } else {

            Task curTask = workflowTaskService.getCurTask(checklistTask.getProcessId(), item.getNodeId(), loginUser.getId());
            if (curTask == null) {//判断是否当前节点
                throw new JeecgBootException("当前流程没有需要您处理的事项！");
            }

            String taskId = curTask.getId();
            vo.setComment("审批通过");
            taskService.createComment(taskId, checklistTask.getProcessId(), vo.getComment());
            workflowTaskService.completeTask(taskId, checklistTask.getProcessId(), vo.getComment(), processVariables);
        }

        //获取下个节点
        List<TaskVo> tasks = workflowTaskService.tasks(checklistTask.getProcessId());
        String nextNodeId = null;
        if (CollectionUtils.isNotEmpty(tasks)) {
            nextNodeId = tasks.get(0).getTaskDefinitionKey();
            //把后面所有活动的节点，都设置owner为流程发起用户
            for (TaskVo tv : tasks) {
                taskService.setOwner(tv.getId(), checklistTask.getCreateBy());
            }
        }

        if (StringUtils.isNotEmpty(nextNodeId)) {
            checklistTask.setProcessStatus(CommonConstant.BPM_STATUS_FOLLOWING);

            //设置下个节点的nodeStatus 为1
            Map<String, Object> params = Maps.newHashMap();
            params.put("nodeStatus", 1);
            params.put("taskId", checklistTask.getId());
            params.put("nodeId", nextNodeId);
            taskItemMapper.updateNodeStatus(params);
        } else {
            checklistTask.setApproveTime(new Date());
            checklistTask.setProcessStatus(CommonConstant.BPM_STATUS_COMPLETED);
        }

        this.baseMapper.updateById(checklistTask);

        BeanUtils.copyProperties(checklistTask, vo);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reject(Long id, String reason) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }
        ChecklistTask checklistTask = this.baseMapper.selectById(id);
        checklistTask.setProcessStatus(CommonConstant.BPM_STATUS_REJECTED);
        checklistTask.setReason(reason);
        updateById(checklistTask);
        try {
            List<TaskVo> tasks = workflowTaskService.tasks(checklistTask.getProcessId());
            if (tasks.isEmpty()) {//判断是否当前节点
                throw new JeecgBootException("当前流程已完成，不可撤销！");
            }
            String taskId = tasks.get(0).getId();
            bpmService.rollbackProcess(1, checklistTask.getProcessId(), taskId, reason, true);
            HistoricActivityInstance startNode = bpmService.getStartNode(checklistTask.getProcessId());
            Map<String, Object> params = Maps.newHashMap();
            params.put("taskId", id);
            params.put("nodeId", startNode.getActivityId());
            params.put("nodeStatus", 1);
            taskItemMapper.updateNodeStatus(params);
            params.put("exclusionNodeId", "T");
            params.put("nodeStatus", 0);
            taskItemMapper.updateNodeStatus(params);
            actHiTaskInstMapper.updateHiTaskInstByIdArray(Arrays.asList(taskId), CommonConstant.BPM_STATUS_REJECTED);
        } catch (Exception e) {
            log.error("流程异常提醒：" + e.getMessage());
            throw new JeecgBootException("驳回流程异常！");
        }
    }

    @Override
    public void refuse(Long id, String reason) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }
        ChecklistTask checklistTask = this.baseMapper.selectById(id);
        checklistTask.setProcessStatus(CommonConstant.BPM_STATUS_REFUSE);
        checklistTask.setReason(reason);
        updateById(checklistTask);

        String procId = checklistTask.getProcessId();
        List<TaskVo> tasks = workflowTaskService.tasks(procId);
        if (tasks.isEmpty()) {//判断是否当前节点
            throw new JeecgBootException("当前流程已完成，不可撤销！");
        }

        String taskId = tasks.get(0).getId();
        Task curTask = workflowTaskService.getTask(procId, taskId);
        if (!loginUser.getId().equals(curTask.getAssignee())) {
            throw new JeecgBootException("您不是当前流程的处理人，无权处理页面！");
        }

        //添加变量
        Map<String, Object> processVariables = new HashMap<>();
        processVariables.put(curTask.getTaskDefinitionKey() + "_approve", 2);
        taskService.createComment(taskId, checklistTask.getProcessId(), "审批拒绝:" + reason);
        workflowTaskService.completeTask(taskId, checklistTask.getProcessId(), "审批拒绝:" + reason, processVariables);
        actHiTaskInstMapper.updateHiTaskInstByIdArray(tasks.stream().map(TaskVo::getId).collect(Collectors.toList()), CommonConstant.BPM_STATUS_REFUSE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void retrieve(Long id, String reason) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }
        ChecklistTask checklistTask = this.baseMapper.selectById(id);
        checklistTask.setProcessStatus(CommonConstant.BPM_STATUS_RETRIEVE);
        checklistTask.setReason(reason);
        updateById(checklistTask);
        try {
            List<TaskVo> tasks = workflowTaskService.tasks(checklistTask.getProcessId());
            if (tasks.isEmpty()) {//判断是否当前节点
                throw new JeecgBootException("当前流程已完成，不可撤回！");
            }
            String taskId = tasks.get(0).getId();
            bpmService.rollbackProcess(1, checklistTask.getProcessId(), taskId, reason, false);
            HistoricActivityInstance startNode = bpmService.getStartNode(checklistTask.getProcessId());
            if (!loginUser.getId().equals(startNode.getAssignee())) {
                throw new JeecgBootException("您不是申请人，无权操作撤回！");
            }
            Map<String, Object> params = Maps.newHashMap();
            params.put("taskId", id);
            params.put("nodeId", startNode.getActivityId());
            params.put("nodeStatus", 1);
            taskItemMapper.updateNodeStatus(params);
            params.put("exclusionNodeId", "T");
            params.put("nodeStatus", 0);
            taskItemMapper.updateNodeStatus(params);
            actHiTaskInstMapper.updateHiTaskInstByIdArray(Arrays.asList(taskId), CommonConstant.BPM_STATUS_RETRIEVE);
        } catch (Exception e) {
            log.error("流程异常提醒：" + e.getMessage());
            throw new JeecgBootException("申请人撤回流程异常！" + e.getMessage());
        }
    }

    @Override
    public void cancel(Long id, String reason) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }
        ChecklistTask checklistTask = this.baseMapper.selectById(id);
        checklistTask.setProcessStatus(CommonConstant.BPM_STATUS_DELETED);
        checklistTask.setReason(reason);
        updateById(checklistTask);

        String procId = checklistTask.getProcessId();
        if (StringUtils.isEmpty(procId))
            return;

        List<TaskVo> tasks = workflowTaskService.tasks(procId);
        if (tasks.isEmpty()) {//判断是否当前节点
            throw new JeecgBootException("当前流程已完成，不可撤销！");
        }
        String taskId = tasks.get(0).getId();
        TaskVo<?> curTask = tasks.get(0);

        List<String> curTaskIds = tasks.stream().map(TaskVo::getId).collect(Collectors.toList());
        HistoricProcessInstance hisProIns = historyService.createHistoricProcessInstanceQuery().processInstanceId(procId).singleResult();

        if (!loginUser.getId().equals(hisProIns.getStartUserId())) {
            throw new JeecgBootException("您不是流程发起人，无权处理撤销！");
        }

        // 添加变量
        Map<String, Object> processVariables = new HashMap<>();
        if (hisProIns.getStartActivityId().equals(tasks.get(0).getTaskDefinitionKey())) {
            processVariables.put(curTask.getTaskDefinitionKey() + "_approve", 3);
        } else {
            //当前节点不是申请人节点是取消只能与拒绝用同一个变量值为2
            processVariables.put(curTask.getTaskDefinitionKey() + "_approve", 2);
        }

        taskService.createComment(taskId, procId, "申请人：" + loginUser.getUsername() + "取消");
        workflowTaskService.completeTask(taskId, procId, "取消", processVariables);
        actHiTaskInstMapper.updateHiTaskInstByIdArray(curTaskIds, CommonConstant.BPM_STATUS_DELETED);

    }
}
