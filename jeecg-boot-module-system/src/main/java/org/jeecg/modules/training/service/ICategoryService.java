package org.jeecg.modules.training.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.training.entity.Category;
import org.jeecg.modules.training.vo.CategoryTreeVO;

import java.util.List;

/**
 * @Description: 分类
 * @Author: jeecg-boot
 * @Date: 2022-05-04
 * @Version: V1.0
 */
public interface ICategoryService extends IService<Category> {

    /**
     * 根据关键字获取分类信息
     *
     * @param keyWord 搜索词
     * @return
     */
    List<CategoryTreeVO> queryTreeByKeyWord(String types, String keyWord);

    /**
     * 逻辑删除
     *
     * @param id
     */
    Result<Category> deleteById(String id);

    /**
     * 逻辑批量删除
     *
     * @param ids
     */
    Result<Category> deleteBatch(List<String> ids);


    Long shareCategory(Long categoryId, String route, boolean force);
}
