package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.Checklist;
import org.jeecg.modules.training.vo.ChecklistVO;
import org.jeecg.modules.training.vo.PaperVO;

/**
 * @Description: 许可证和检查配置主表
 * @Author: jeecg-boot
 * @Date: 2022-10-23
 * @Version: V1.0
 */
public interface ChecklistMapper extends BaseMapper<Checklist> {

    IPage<ChecklistVO> listChecklists(Page<ChecklistVO> page, @Param("ew") QueryWrapper<ChecklistVO> queryWrapper);

    IPage<ChecklistVO> findChecklists(Page<ChecklistVO> page, @Param("categoryId") Long categoryId, @Param("post") String post, @Param("ew") QueryWrapper<PaperVO> queryWrapper);
}
