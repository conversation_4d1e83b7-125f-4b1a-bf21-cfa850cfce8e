package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.Knowledge;
import org.jeecg.modules.training.vo.KnowledgeVo;

import java.util.Map;

/**
 * @Description: 知识点
 * @Author: huazhengkan
 * @Date: 2022-05-04
 * @Version: V1.0
 */
public interface KnowledgeMapper extends BaseMapper<Knowledge> {

    /**
     * 查询list
     *
     * @param params
     * @param page
     * @return
     */
    IPage<KnowledgeVo> getAll(@Param("mp") Map<String, Object> params, Page<KnowledgeVo> page);

    IPage<KnowledgeVo> getPageList(Page<KnowledgeVo> page, @Param("ew") QueryWrapper<KnowledgeVo> queryWrapper);

    Knowledge getById(@Param("id") Long knowledgeId);

}
