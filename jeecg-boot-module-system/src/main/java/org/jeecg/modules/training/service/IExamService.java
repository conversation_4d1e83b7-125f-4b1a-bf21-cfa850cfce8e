package org.jeecg.modules.training.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.training.entity.Exam;
import org.jeecg.modules.training.vo.ExamListVO;
import org.jeecg.modules.training.vo.ExamVO;

import java.util.List;
import java.util.Map;

/**
 * @Description: 考卷
 * @Author: hzk
 * @Date: 2022-05-04
 * @Version: V1.0
 */
public interface IExamService extends IService<Exam> {

    /**
     * 创建考卷
     *
     * @param categoryId
     * @param type       考卷类型： 1：练习；2：考试
     * @return
     */
    Long createExam(Long categoryId, Long userPaperId, Long paperId);

    /**
     * 获取考卷详情
     *
     * @param id 考卷id
     * @return
     */
    ExamVO examDetail(Long id);

    /**
     * 提交考卷
     *
     * @param vo
     * @param isAdmin 是否管理员操作
     * @return
     */
    boolean submit(ExamVO vo, Boolean isAdmin);

    /**
     * 重做
     *
     * @param id
     * @return
     */
    Result<?> repeat(Long id);

    /**
     * 查询list
     *
     * @param page
     * @param params
     * @return
     */
    IPage<ExamVO> getList(Page<ExamVO> page, Map<String, Object> params);

    /**
     * 管理员刷新
     *
     * @return
     */
    Result<?> personSubmit();

    /**
     * 管理作废考卷
     *
     * @param id
     * @return
     */
    Result<?> cancel(Long id);

    IPage<ExamListVO> getPageList(Page<ExamListVO> page, QueryWrapper<ExamListVO> queryWrapper);

    /**
     * 不分页list
     *
     * @param queryWrapper
     * @return
     */
    List<ExamListVO> getList(QueryWrapper<ExamListVO> queryWrapper);


    List<ExamListVO> getSummaryList(QueryWrapper<ExamListVO> queryWrapper);


    /**
     * 汇总数据根据个人和分类
     *
     * @param queryWrapper
     * @return
     */
    IPage<ExamListVO> getSummaryList(Page<ExamListVO> page, QueryWrapper<ExamListVO> queryWrapper);

    /**
     * 重新计算成绩
     *
     * @param vo
     * @return
     */
    Result<?> reComputeResult(ExamVO vo);

    void processUserPaperStatus(Exam exam);

    Page<ExamListVO> getToBeScoreExam(Page<ExamListVO> page, QueryWrapper<ExamListVO> queryWrapper, String userId);

    // 重置考试
    void reset(String examId);

    /**
     * 生成考试报告
     * 根据考试ID自动判断考试类型并生成对应报告
     *
     * @param examId     考试ID
     * @param outputPath 报告输出路径
     * @throws IllegalArgumentException 当考试ID无效或输出路径为空时抛出
     * @throws RuntimeException         当报告生成过程中发生错误时抛出
     */
    void generateReport(Long examId, String outputPath);
}
