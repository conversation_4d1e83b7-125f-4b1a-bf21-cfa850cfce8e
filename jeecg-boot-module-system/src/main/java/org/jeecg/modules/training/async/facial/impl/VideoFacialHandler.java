package org.jeecg.modules.training.async.facial.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.seeta.sdk.*;
import com.seeta.sdk.util.LoadNativeCore;
import com.seeta.sdk.util.SeetafaceUtil;
import com.xkcoding.http.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.jeecg.modules.training.async.facial.FacialHandler;
import org.jeecg.modules.training.async.facial.ShellCmdExecutor;
import org.jeecg.modules.training.async.facial.VideoInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 进行人脸识别对比
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年09月22日 16:02
 */
@Slf4j
@Component
@Scope("prototype")
public class VideoFacialHandler extends FacialHandler {

    @Value("${WECHAT.model}")
    private String model;

    public VideoFacialHandler() {
        super();
    }

    @Value("${WECHAT.criticalValue}")
    protected Double criticalValue;

    static {
        LoadNativeCore.LOAD_NATIVE(SeetaDevice.SEETA_DEVICE_AUTO);
    }

    @Override
    public void run() {

        try {

            HashMap<String, Float> hashMap = new HashMap<>();

            for (String videoPath : exam.getVideoPaths().substring(0, exam.getVideoPaths().length() - 1).split(",")) {
                ///获取文件长度
                videoPath = parentPath + videoPath;
                VideoInfo videoInfo = getVideoInfo(videoPath);
                hashMap.put(videoPath, videoInfo.getTime());
                log.info("videoPath:{},time:{}", videoPath, videoInfo.getTime());

                String[] cmd = new String[6];
                //cmd[0] = "/home/<USER>/trainsaas/system/tools/ffmpeg";
                cmd[0] = VideoTools.ffmpegFilePath;
                cmd[1] = "-i";
                cmd[2] = videoPath;
                cmd[3] = "-vf";
                cmd[4] = "fps=0.5";
                cmd[5] = videoPath.replace(".mp4", "") + "%d.jpg";

                log.info("cmd:{}", Arrays.toString(cmd));
                ProcessBuilder builder = new ProcessBuilder();
                builder.command(cmd);
                Process process = builder.start();
                int i = process.waitFor();
                log.info("切割图片结果:{}", i);

            }
            log.debug(hashMap.toString());

            ArrayList<String> paths = new ArrayList<>();
            for (Map.Entry<String, Float> entry : hashMap.entrySet()) {
                String key = entry.getKey();
                key = key.replace(".mp4", "");

                log.debug(key + ":{}", Math.round(entry.getValue() / 2));

                for (int i = 1; i <= Math.round(entry.getValue() / 2); i++) {
                    paths.add(key + i + ".jpg");
                }
            }

            log.debug(paths.toString());

            Double similarity = 0.0;
            for (int i = 0; i < paths.size() - 1; i++) {
                similarity += getSimilarity(paths.get(i), paths.get(i + 1));
            }
            log.debug("similarityTotal:{}", similarity);
            log.debug("size:{}", paths.size());
            similarity /= (paths.size() - 1);
            log.debug("similarity:{}", similarity);

            finish(similarity, String.join(",", paths));

        } catch (Exception e) {
            log.error("EXP", e);
        }
    }

    private void finish(Double similarity, String photoPaths) {
        exam.setSimilarity(similarity);
        exam.setPhotoPaths(photoPaths);
        exam.setJudgmentResult(similarity >= criticalValue ? 1 : 0);
        examService.updateById(exam);
    }

    private Double getSimilarity(String filename1, String filename2) throws Exception {
        log.info("进入了人脸比对方法");

        //人脸检测器
        FaceDetector detector = new FaceDetector(new SeetaModelSetting(new String[]{model + "/face_detector.csta"}, SeetaDevice.SEETA_DEVICE_AUTO));
        //关键点定位器 5点
        FaceLandmarker faceLandmarker = new FaceLandmarker(new SeetaModelSetting(new String[]{model + "/face_landmarker_pts5.csta"}, SeetaDevice.SEETA_DEVICE_AUTO));

        //人脸向量特征提取和对比器
        FaceRecognizer faceRecognizer = new FaceRecognizer(new SeetaModelSetting(new String[]{model + "/face_recognizer.csta"}, SeetaDevice.SEETA_DEVICE_AUTO));
        faceRecognizer.dispose();
        log.info(String.valueOf(faceRecognizer.GetExtractFeatureSize()));

        //两张图片
        //String fileName = "D:\\face\\image\\me\\00.jpg";
        String fileName = filename1;
        log.info("name1:{},name2:{}", filename1, filename2);
        //String fileName2 = "D:\\face\\image\\me\\11.jpg";
        String fileName2 = filename2;
        //第1张照片
        SeetaImageData image1 = SeetafaceUtil.toSeetaImageData(fileName);
        log.info("人脸图片读取成功");
        //第一张照片人脸识别
        SeetaRect[] detects1 = detector.Detect(image1);

        SeetaPointF[] pointFS1 = new SeetaPointF[faceRecognizer.GetExtractFeatureSize()];
        int[] masks1 = new int[faceRecognizer.GetExtractFeatureSize()];
        //第一张图片，第一个人脸关键点定位，有多个人脸的情况下，只取第一个人脸（这是测试，先这样写）
        faceLandmarker.mark(image1, detects1[0], pointFS1, masks1);
        //第一张图片，第一个人脸向量特征提取features1
        float[] features1 = new float[faceRecognizer.GetExtractFeatureSize()];
        faceRecognizer.Extract(image1, pointFS1, features1);

        //第2张照片
        SeetaImageData image2 = SeetafaceUtil.toSeetaImageData(fileName2);

        //第二张图片，人脸识别
        SeetaRect[] detects2 = detector.Detect(image2);

        SeetaPointF[] pointFS2 = new SeetaPointF[faceRecognizer.GetExtractFeatureSize()];
        int[] masks2 = new int[faceRecognizer.GetExtractFeatureSize()];
        //第二张图片，第一个人脸，关键点识别
        faceLandmarker.mark(image2, detects2[0], pointFS2, masks2);

        //第二张图片，第一个人脸，向量特征提取features2
        float[] features2 = new float[faceRecognizer.GetExtractFeatureSize()];
        faceRecognizer.Extract(image2, pointFS2, features2);

        //log.info(Arrays.toString(features1));
        //log.info(Arrays.toString(features2));
        //两个人脸向量做对比，得出分数
        /*if (features1 != null && features2 != null) {

        }*/
        float calculateSimilarity = faceRecognizer.CalculateSimilarity(features1, features2);
        log.info("相似度: {}", calculateSimilarity);
        return (double) calculateSimilarity;
    }


    private VideoInfo getVideoInfo(String upFilePath) {
        VideoInfo videoInfo = new VideoInfo();

        //ffprobe -i -print_format json -show_format -show_streams
        ShellCmdExecutor executor = new ShellCmdExecutor(VideoTools.ffprobeFilePath);
        executor.addArgument("-i"); // 添加参数＂-i＂，该参数指定要转换的文件
        executor.addArgument(upFilePath); // 添加要转换格式的视频文件的路径
        executor.addArgument("-print_format"); //
        executor.addArgument("json=string_validation=ignore"); // json 格式
        executor.addArgument("-show_format"); //
        executor.addArgument("-show_streams"); //
        try {

            executor.execute(false);

            String text = executor.readStandardInput();

            executor.waitFor(3000);

            boolean ok = !StringUtil.isEmpty(text) && (executor.getErrorInfo() == null || !executor.getErrorInfo().contains("Error"));

            if (ok) {

                try {
                    String ext = FilenameUtils.getExtension(upFilePath).toLowerCase();
                    JSONObject jsonObject = JSON.parseObject(text);
                    JSONObject format = jsonObject.getJSONObject("format");
                    String[] fns = format.getString("format_name").toLowerCase().split(",");
                    if (Arrays.asList(fns).contains(ext))
                        videoInfo.format = ext;
                    else
                        videoInfo.format = fns[0];

                    videoInfo.time = format.getFloatValue("duration");

                    JSONArray streams = jsonObject.getJSONArray("streams");
                    for (Object o : streams) {
                        JSONObject jo = (JSONObject) o;
                        String ct = jo.getString("codec_type");
                        switch (ct) {
                            case "video": {
                                //                                if(maxBitRate != 0L){
                                //                                    videoInfo.videoBitRate = Integer.valueOf(maxBitRate.toString());
                                //                                }else{
                                videoInfo.videoBitRate = jo.getLongValue("bit_rate");
                                // 获取最大码率,不能用平均码率,否则会导致有些视频因最大码率过大到时播放器无法播放 ==> todo 视频最大码率拿不到
                                //videoInfo.videoBitRate = jo.getIntValue("max_bit_rate");
                                if (videoInfo.videoBitRate == 0)
                                    videoInfo.videoBitRate = format.getLongValue("bit_rate");
                                //                                }
                                videoInfo.width = jo.getIntValue("width");
                                videoInfo.height = jo.getIntValue("height");
                                videoInfo.videoCoder = jo.getString("codec_name");
                                videoInfo.videoCoderTag = jo.getString("codec_tag_string");
                                //                                videoInfo.fps = (float) MathCalculate.calc(jo.getString("r_frame_rate"));
                                break;
                            }
                            case "audio": {
                                videoInfo.audioBitRate = jo.getLongValue("bit_rate");
                                if (videoInfo.audioBitRate == 0)
                                    videoInfo.audioBitRate = format.getLongValue("bit_rate");
                                videoInfo.audioSampleRate = jo.getLongValue("sample_rate");
                                videoInfo.audioCoder = jo.getString("codec_name");
                                break;
                            }
                        }
                    }
                } catch (Exception ex) {
                    log.error("EXE cmd:" + executor.toString());
                    log.error("EXE_err:" + executor.getErrorInfo());
                    log.error("EXE_out:" + text);
                    log.error("EXP", ex);
                    videoInfo = null;
                }

            } else {
                videoInfo = null;
                log.error("run command result is :" + text);
                log.error("run command error is:" + executor.getErrorInfo());
            }

        } catch (Exception ex) {
            videoInfo = null;
            log.error("EXP", ex);
        } finally {
            executor.destroy();
        }
        return videoInfo;
    }


}
