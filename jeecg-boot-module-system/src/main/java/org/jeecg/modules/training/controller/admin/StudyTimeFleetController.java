package org.jeecg.modules.training.controller.admin;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.fields.FieldFilter;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.training.entity.StudyTimeFleet;
import org.jeecg.modules.training.service.StudyTimeFleetService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 船队每日学习时长表(StudyTimeFleet)表控制层
 *
 * <AUTHOR>
 * @since 2023-12-22 10:47:41
 */
@RestController
@RequestMapping("/adm/studyTimeFleet")
@Api(tags = "船队学习时长")
public class StudyTimeFleetController {
    /**
     * 服务对象
     */
    @Resource
    private StudyTimeFleetService studyTimeFleetService;

    @ApiOperation("查询船队学习时间排名")
    @GetMapping("/list")
    public Result<Page<StudyTimeFleet>> list(StudyTimeFleet studyTimeFleet,
                                             @RequestParam String scope,
                                             @RequestParam Integer pageNo,
                                             @RequestParam Integer pageSize,
                                             HttpServletRequest req
    ) {
        Page<StudyTimeFleet> page = new Page<>(pageNo, pageSize);

        QueryWrapper<StudyTimeFleet> queryWrapper = QueryGenerator.initQueryWrapper(studyTimeFleet, req.getParameterMap());
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        Page<StudyTimeFleet> pageList = studyTimeFleetService.getPageList(page,loginUser.getDepRoute(), queryWrapper, scope);

        return Result.ok(pageList);
    }

    // 船队平均学习时长 统计表专用
    @FieldFilter({"avgStudyTime", "fleetName"})
    @GetMapping(value = "/getAvgTime")
    public Result<?> getAvgTime() {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        return Result.OK(studyTimeFleetService.getAvgTime(loginUser.getDepRoute()));
    }

}

