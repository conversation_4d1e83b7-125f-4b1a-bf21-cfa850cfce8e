package org.jeecg.modules.training.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.*;
import org.jeecg.modules.base.service.BaseCommonService;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.entity.SysPosition;
import org.jeecg.modules.system.entity.SysTraineeDepartLog;
import org.jeecg.modules.system.model.DepartIdModel;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.system.service.ISysPositionService;
import org.jeecg.modules.system.service.ISysTraineeDepartLogService;
import org.jeecg.modules.system.service.ISysUserDepartService;
import org.jeecg.modules.training.async.paper.PushPaperService;
import org.jeecg.modules.training.async.phase.PushPhaseService;
import org.jeecg.modules.training.entity.*;
import org.jeecg.modules.training.mapper.ExamMapper;
import org.jeecg.modules.training.mapper.SysTraineeDepartMapper;
import org.jeecg.modules.training.mapper.TraineeMapper;
import org.jeecg.modules.training.mapper.UserPaperMapper;
import org.jeecg.modules.training.service.*;
import org.jeecg.modules.training.util.DateTimeUtils;
import org.jeecg.modules.training.util.ExcelUtils;
import org.jeecg.modules.training.vo.MiniProgramDTO;
import org.jeecg.modules.training.vo.SysTraineeVO;
import org.jeecg.modules.training.vo.TraineeRegisterDTO;
import org.jeecg.modules.training.vo.TraineeVO;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.jetbrains.annotations.NotNull;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 受试人信息表
 */
@Service
@Slf4j
@EnableAspectJAutoProxy(exposeProxy = true)
public class TraineeServiceImpl extends ServiceImpl<TraineeMapper, Trainee> implements TraineeService {


    static final int[] idCardWi = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
    //这是除以11后，可能产生的11位余数对应的验证码
    static final String[] idCardY = {"1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"};

    // 定义判别用户身份证号的正则表达式（15位或者18位，最后一位可以为字母）
    String idExpression = "(^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}$)";
    //手机号
    String phoneExpression = "^1[3-9]\\d{9}$";

    @Autowired(required = false)
    private TraineeMapper traineeMapper;

    @Autowired(required = false)
    private TeamService teamService;

    @Autowired(required = false)
    private ISysUserDepartService sysUserDepartService;

    @Autowired(required = false)
    private SysTraineeDepartService sysTraineeDepartService;

    @Autowired
    private ISysPositionService positionService;

    @Autowired(required = false)
    private ExamMapper examMapper;

    @Autowired(required = false)
    private UserPaperMapper userPaperMapper;

    @Autowired
    private PushPaperService pushPaperService;

    @Resource
    private PushPhaseService userPhaseHandleService;
    @Resource
    private IUserPhaseService userPhaseService;

    @Autowired
    private ISyncStateService syncStateService;

    @Autowired
    private ISysUserLogService sysUserLogService;

    @Resource
    private BaseCommonService baseCommonService;

    @Autowired
    private RedisUtil redisUtil;

    @Resource
    private ISysDepartService sysDepartService;

    @Autowired
    private IPackagesService packagesService;

    @Autowired
    private SysTraineeDepartMapper sysTraineeDepartMapper;

    @Autowired
    private ISysTraineeDepartLogService sysTraineeDepartLogService;

    @Override
    public IPage<SysTraineeVO> getTraineeList(Page<TraineeVO> page, String loginRoute, String loginUserId, QueryWrapper<SysTraineeVO> queryWrapper, String sysDepartId, Integer memberType) {
        return this.baseMapper.getTraineeList(page, loginRoute, loginUserId, queryWrapper, sysDepartId, memberType);
    }


    @Override
    public List<SysTraineeVO> getTraineeList(String loginRoute, String loginUserId, QueryWrapper<SysTraineeVO> queryWrapper, String sysDepartId, Integer memberType) {
        return this.baseMapper.getTraineeList(loginRoute, loginUserId, queryWrapper, sysDepartId, memberType);
    }

    /**
     * 校验用户是否有效
     *
     * @param trainee
     * @return
     */
    @Override
    public Result<?> checkUserIsEffective(Trainee trainee) {
        Result<?> result = new Result<Object>();
        //情况1：根据用户信息查询，该用户不存在
        if (trainee == null) {
            result.error500("该用户不存在，请注册");
            baseCommonService.addLog("用户登录失败，用户不存在！", CommonConstant.LOG_TYPE_1, null);
            return result;
        }
        //情况2：根据用户信息查询，该用户已注销
        //update-begin---author:王帅   Date:20200601  for：if条件永远为falsebug------------
        if (CommonConstant.DEL_FLAG_1.equals(trainee.getDelFlag())) {
            //update-end---author:王帅   Date:20200601  for：if条件永远为falsebug------------
            baseCommonService.addLog("用户登录失败，用户名:" + trainee.getUsername() + "已注销！", CommonConstant.LOG_TYPE_1, null);
            result.error500("该用户已注销");
            return result;
        }
        //情况3：根据用户信息查询，该用户已冻结
        if (CommonConstant.USER_FREEZE.equals(trainee.getStatus())) {
            baseCommonService.addLog("用户登录失败，用户名:" + trainee.getUsername() + "已冻结！", CommonConstant.LOG_TYPE_1, null);
            result.error500("该用户已冻结");
            return result;
        }
        return result;
    }

    @Override
    @CacheEvict(value = {CacheConstant.TRAINEE_CACHE}, allEntries = true)
    public Result<?> changePassword(Trainee trainee) {
        String salt = oConvertUtils.randomGen(8);
        trainee.setSalt(salt);
        String password = trainee.getPassword();
        String passwordEncode = PasswordUtil.encrypt(trainee.getUsername(), password, salt);
        trainee.setPassword(passwordEncode);
        this.traineeMapper.updateById(trainee);
        return Result.ok("密码修改成功!");
    }

    @Override
    @CacheEvict(value = {CacheConstant.TRAINEE_CACHE}, key = "#username")
    public void updateUserDepart(String username, String orgCode) {
        baseMapper.updateTraineeDepart(username, orgCode);
    }

    /**
     * 组装登录后的数据
     *
     * @param trainee
     * @param response
     * @return
     */
    @Override
    public JSONObject appendInfo(Trainee trainee, HttpServletResponse response) {
        JSONObject obj = new JSONObject();
        //用户登录信息
        obj.put("userInfo", trainee);

        //改成用身份证号
        String token = JwtUtil.sign(trainee.getIdentityCard(), trainee.getPassword());
        // 设置超时时间为2个星期
        long expireTime = JwtUtil.EXPIRE_TIME * 2 / 1000;
        redisUtil.set(CommonConstant.PREFIX_TRAINEE_TOKEN + token, token, expireTime);
        response.setHeader("token", token);
        //token 信息
        obj.put("token", token);
        //token过期时间
        obj.put("expired", expireTime);
        return obj;
    }

    @Override
    public Trainee getUserByPhone(String phone) {
        return traineeMapper.getUserByPhone(phone);
    }

    @Override
    public void revertLogicDeleted(String traineeId, Trainee trainee) {
        traineeMapper.revertLogicDeleted(traineeId, trainee);
    }

    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    public Result<?> resetPassword(String username, String oldpassword, String newpassword) {
        Trainee trainee = baseMapper.getUserByName(username);
        String passwordEncode = PasswordUtil.encrypt(username, oldpassword, trainee.getSalt());
        if (!trainee.getPassword().equals(passwordEncode)) {
            return Result.error("旧密码输入错误!");
        }
        if (oConvertUtils.isEmpty(newpassword)) {
            return Result.error("新密码不允许为空!");
        }

        //修复用户名不是身份证号问题,只要修改密码，就会修复username为身份证号
        username = trainee.getIdentityCard();
        String password = PasswordUtil.encrypt(username, newpassword, trainee.getSalt());
        this.baseMapper.update((Trainee) new Trainee().setPassword(password).setUsername(username),
                new LambdaQueryWrapper<Trainee>().eq(Trainee::getId, trainee.getId()));
        return Result.ok("密码重置成功!");
    }

    @Override
    public Trainee getUserByName(String username) {
        return traineeMapper.getTraineeByName(username);
    }

    @Override
    @CacheEvict(value = {CacheConstant.TRAINEE_CACHE}, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUser(String userId, SysDepart sysDepart) {

        // 删除船员时执行的操作
        onTraineeDeleted(userId, sysDepart);

        // 删除船员部门关系记录
        sysTraineeDepartService.lambdaUpdate()
                .eq(SysTraineeDepart::getTraineeId, userId)
                .eq(SysTraineeDepart::getDepId, sysDepart.getId())
                .remove();
        /*
         sysTraineeDepart的删除操作不用重置同步状态，因为中转服务不能同步删除操作，而且
         在岸模式删除记录后，在船模式就同步不到了，且不会影响系统的功能
         */

        return false;
    }

    // 删除船员部门关系前执行的操作
    @Override
    public void onTraineeDeleted(String userId, SysDepart sysDepart) {

        Trainee trainee = getById(userId);
        //删除受试人后要将受试人所在船的受试人数量减一
        teamService.lambdaUpdate()
                .eq(Team::getId, trainee.getTeamId())
                .setSql("trainee_count = trainee_count - 1")
                .update();
        syncStateService.resetSyncState(CommonConstant.TEAM, trainee.getTeamId());

        // 企业会员模式公司，删除用户后，将套餐余额加一
        if (sysDepart.getMemberType() == 1) {
            returnPackagesNum(userId, sysDepart.getId());
        }
    }

    // 归还套餐数量
    private void returnPackagesNum(String traineeId, String sysDepartId) {

        SysTraineeDepart sysTraineeDepart = sysTraineeDepartService.lambdaQuery()
                .eq(SysTraineeDepart::getTraineeId, traineeId)
                .eq(SysTraineeDepart::getDepId, sysDepartId)
                .one();

        if (sysTraineeDepart == null) {
            log.error("套餐数量归还失败，找不到船员公司记录！（船员id = {}）", traineeId);
            throw new JeecgBootException("套餐数量归还失败，系统找不到船员公司记录！");
        }

        if (sysTraineeDepart.getPackagesId() == null) {
            log.error("套餐数量归还失败，找不到船员（id = {}）的企业会员标识", traineeId);
            throw new JeecgBootException("套餐数量归还失败，找不到船员的企业会员标识");
        }

        if (!packagesService.exists(new LambdaQueryWrapper<Packages>()
                .eq(Packages::getId, sysTraineeDepart.getPackagesId()))) {
            log.error("套餐数量归还失败，船员关联的套餐已删除（id = {}）", sysTraineeDepart.getPackagesId());
            throw new JeecgBootException("套餐数量归还失败，船员关联的套餐已删除");
        }

        packagesService.lambdaUpdate()
                .setSql("surplus_num = surplus_num + 1")
                .eq(Packages::getId, sysTraineeDepart.getPackagesId()) // 企业会员套餐
                .update();
        syncStateService.resetSyncState(CommonConstant.PACKAGES, sysTraineeDepart.getPackagesId().toString());

    }

    @Override
    @CacheEvict(value = {CacheConstant.TRAINEE_CACHE}, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatchUsers(List<String> traineeIdList, SysDepart sysDepart) {

        traineeIdList.forEach(traineeId -> {

            // 删除船员时执行的操作
            onTraineeDeleted(traineeId, sysDepart);

            // 删除船员部门关系记录
            sysTraineeDepartService.lambdaUpdate()
                    .eq(SysTraineeDepart::getTraineeId, traineeId)
                    .eq(SysTraineeDepart::getDepId, sysDepart.getId())
                    .remove();
            /*
             sysTraineeDepart的删除操作不用重置同步状态，因为中转服务不能同步删除操作，而且
             在岸模式删除记录后，在船模式就同步不到了，且不会影响系统的功能
            */
        });

        return false;
    }

    @Override
    public TraineeVO getUserByIDCard(String identityCard) {
        return traineeMapper.getTraineeByIDCard(identityCard);
    }

    /**
     * 全部人员导入
     *
     * @param request
     * @return
     * @throws IOException
     */
    @Override
    public Result<?> importExcelForAll(HttpServletRequest request) throws IOException {

        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        // 错误信息
        List<String> errorMessage = new ArrayList<>();
        //上船和下船人员
        List<ImportSysUser> userList = new ArrayList<>();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            Workbook workbook = ExcelUtils.createWorkbook(file);
            Sheet sheet = workbook.getSheet("总名单");
            if (sheet == null) {
                errorMessage.add("导入格式不正确，没有[总名单]表格，请重更换模板再导入！");
                continue;
            }


            try {

                List<String> idCards = new ArrayList<>();

                //数据从第2行开始
                for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
                    Row row = sheet.getRow(rowNum);
                    if (row == null) {
                        continue;
                    }
                    int colNum = 0;
                    String num = ExcelUtils.getStr(row, rowNum, colNum++, "序号", false, 100);
                    if (StringUtils.isEmpty(num)) {
                        break;
                    }

                    if (!NumberUtil.isNumber(num)) {
                        break;
                    }
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                    try {
                        ImportSysUser upUser = new ImportSysUser();

                        //工号
                        String workNo = ExcelUtils.getStr(row, rowNum, colNum++, "工号", false, 100);
                        if (StringUtils.isNotEmpty(workNo)) {
                            upUser.setWorkNo(workNo);
                        }

                        //姓名
                        String name = ExcelUtils.getStr(row, rowNum, colNum++, "姓名", true, 100);
                        upUser.setRealname(name);

                        //出生日期
                        try {
                            String birthday = ExcelUtils.getStr(row, rowNum, colNum++, "出生日期", true, 100);
                            upUser.setBirthday(format.parse(birthday));
                        } catch (Exception e) {
                            errorMessage.add("第" + rowNum + "行[出生日期]格式不正确！");
                            continue;
                        }

                        //现任职务
                        String post = ExcelUtils.getStr(row, rowNum, colNum++, "现任职务", true, 100);
                        upUser.setPost(post);

                        //所在船舶名称
                        String departName = ExcelUtils.getStr(row, rowNum, colNum++, "当前所在船舶名称", false, 100);
                        if (departName.isEmpty()) {
                            //当前登录用户
                            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                            //当前登录用户的部门
                            SysDepart loginSysDepart = sysUserDepartService.getDepartByUserId(loginUser.getId());
                            upUser.setDepartName(loginSysDepart.getDepartName());
                        } else {
                            upUser.setDepartName(departName);
                        }


                        //上船日期
                        try {
                            String onboardDate = ExcelUtils.getStr(row, rowNum, colNum++, "上船日期", true, 100);
                            upUser.setOnboardDate(format.parse(onboardDate));
                        } catch (Exception e) {
                            errorMessage.add("第" + rowNum + "行[上船日期]格式不正确！");
                            continue;
                        }

                        //身份证号
                        String identityCard = ExcelUtils.getStr(row, rowNum, colNum++, "身份证号", true, 100);
                        int idx;
                        String err = checkIdCardNumber(identityCard);
                        if (err != null) {
                            errorMessage.add("第" + rowNum + "行[身份证号]" + err);
                            continue;
                        } else if ((idx = idCards.indexOf(identityCard)) >= 0) {
                            errorMessage.add("第" + rowNum + "行[身份证号]和第" + (idx + 1) + "行重复，忽略!");
                            continue;
                        }
                        upUser.setIdentityCard(identityCard);
                        idCards.add(identityCard);

                        //当前动态
                        String status = ExcelUtils.getStr(row, rowNum, colNum++, "当前动态", true, 100);
                        if (!"上船".equals(status) && !"下船".equals(status)) {
                            errorMessage.add("第" + rowNum + "行[当前动态]不合法，只能填写”上船“ 或者 “下船”！");
                            continue;
                        }
                        upUser.setStatus(status);

                        //移动电话
                        String phone = ExcelUtils.getStr(row, rowNum, colNum++, "移动电话", true, 100);
                        if (!phone.matches(phoneExpression)) {
                            errorMessage.add("第" + rowNum + "行[手机号]不合法!");
                            continue;
                        }
                        upUser.setPhone(phone);

                        userList.add(upUser);

                    } catch (Exception e) {
                        log.error("读取第" + rowNum + "行失败", e);
                        errorMessage.add("第" + rowNum + "行，读取信息失败！");
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                errorMessage.add(e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }

        return importUsers(userList, errorMessage);
    }


    /**
     * 单船导入
     * 下船人员唯一标识:船号+姓名+职务
     *
     * @param request
     * @return
     * @throws IOException
     */
    @Override
    public Result<?> importExcelForDepart(HttpServletRequest request) throws IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        // 错误信息
        List<String> errorMessage = new ArrayList<>();
        //上船和下船人员
        List<ImportSysUser> userList = new ArrayList<>();
        //从excel文件中获取下船和上船的数据，保存在userList中
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象

            // 解析excel中的数据
            Workbook workbook = ExcelUtils.createWorkbook(file);
            Sheet sheet = workbook.getSheet("配员审批表");
            if (sheet == null) {
                errorMessage.add("导入格式不正确，没有[配员审批表]表格，请更换正确的模板重新导入！");
                continue;
            }

            try {

                // 数据第3行开始
                Row row = sheet.getRow(2);

                //部门
                String teamName = ExcelUtils.getStr(row, 2, 2, "船名", false, 100);
                /*if (StringUtils.isEmpty(departName)) {
                    errorMessage.add("船只名称为空，无法导入！");
                    continue;
                }*/

                LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                Team teamExists = new Team();
                if (StringUtils.isNotEmpty(teamName)) {
                    LambdaQueryWrapper<Team> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(Team::getTeamName, teamName);
                    //根据船只名称和导入人的部门路径查询船只信息
                    queryWrapper.eq(Team::getRoute, loginUser.getDepRoute());
                    teamExists = teamService.getOne(queryWrapper);

                    if (teamExists == null) {
                        errorMessage.add("船只" + teamName + "不存在，将忽略下船用户处理！");
                    }
                }


                //用户列表开始
                for (int rowNum = 5; rowNum <= sheet.getLastRowNum(); rowNum++) {

                    row = sheet.getRow(rowNum);
                    if (row == null)
                        continue;

                    int colNum = 0;
                    String num = ExcelUtils.getStr(row, rowNum, colNum++, "序号", false, 100);
                    if (StringUtils.isEmpty(num) || !NumberUtil.isNumber(num)) {
                        break;
                    }

                    //下船信息
                    if (teamExists != null) {

                        //离船员姓名
                        String downName = ExcelUtils.getStr(row, rowNum, colNum++, "姓名", false, 100);
                        //离船人员职务
                        String downPosition = ExcelUtils.getStr(row, rowNum, colNum, "职务", false, 100);

                        if (!StringUtils.isEmpty(downPosition) && StringUtils.isNotEmpty(downName)) {

                            LambdaQueryWrapper<Trainee> sysWrapper = new LambdaQueryWrapper<>();

                            //找出在这艘船上姓名和职务相同的人
                            sysWrapper.eq(Trainee::getTeamId, teamExists.getId());
                            sysWrapper.eq(Trainee::getPost, downPosition);
                            sysWrapper.eq(Trainee::getRealname, downName);
                            List<Trainee> sysUserList = this.baseMapper.selectList(sysWrapper);

                            if (CollectionUtils.isEmpty(sysUserList)) {
                                errorMessage.add("第" + rowNum + "行，下船人员，姓名为 " + downName + " 职务为 " + downPosition + " 的用户不存在，请重新填写！");
                            } else if (sysUserList.size() > 1) {
                                errorMessage.add("第" + rowNum + "行，下船人员：" + downName + "[职务:" + downPosition + "]有重复，请重新填写或者手动修改单个处理下船！");
                            } else {
                                Trainee existUser = sysUserList.get(0);
                                ImportSysUser downUser = new ImportSysUser();
                                downUser.setIdentityCard(existUser.getIdentityCard());
                                downUser.setWorkNo(existUser.getWorkNo());
                                downUser.setBirthday(existUser.getBirthday());
                                downUser.setOnboardDate(existUser.getOnboardDate());
                                downUser.setPhone(existUser.getPhone());
                                downUser.setRealname(existUser.getRealname());
                                downUser.setPost(existUser.getPost());
                                downUser.setDepartName(teamName);
                                downUser.setStatus("下船");
                                userList.add(downUser);
                            }
                        }
                    }

                    //上船信息
                    colNum = 7;
                    String upWorkNo = ExcelUtils.getStr(row, rowNum, colNum++, "工号", false, 100);
                    String upName = ExcelUtils.getStr(row, rowNum, colNum++, "姓名", false, 100);
                    String upPosition = ExcelUtils.getStr(row, rowNum, colNum++, "职务", false, 100);
                    String upIdentityCard = ExcelUtils.getStr(row, rowNum, colNum++, "身份证号码", false, 100);

                    //如果身份证号为空，表示这个没有数据，跳过
                    if (StringUtils.isEmpty(upIdentityCard)) {
                        continue;
                    }

                    String err = checkIdCardNumber(upIdentityCard);
                    if (err != null) {
                        errorMessage.add("第" + rowNum + "行，上船人员[身份证号]，" + err);
                        continue;
                    }

//                    if (StringUtils.isEmpty(upWorkNo)) {
//                        errorMessage.add("第" + rowNum + "行，上船人员[工号]为空，无法导入");
//                        continue;
//                    }

                    if (StringUtils.isEmpty(upName)) {
                        errorMessage.add("第" + rowNum + "行，上船人员[姓名]为空，无法导入");
                        continue;
                    }

                    if (StringUtils.isEmpty(upPosition)) {
                        errorMessage.add("第" + rowNum + "行，上船人员[职务]为空，无法导入");
                        continue;
                    }

                    colNum += 13;
                    String upPhone = ExcelUtils.getStr(row, rowNum, colNum, "联系电话", false, 100);
                    if (!upPhone.matches(phoneExpression)) {
                        errorMessage.add("第" + rowNum + "行，上船人员[手机号]不合法，无法导入!");
                        continue;
                    }

                    ImportSysUser upUser = new ImportSysUser();
                    upUser.setWorkNo(upWorkNo);
                    upUser.setRealname(upName);
                    upUser.setPost(upPosition);
                    upUser.setIdentityCard(upIdentityCard);
                    upUser.setStatus("上船");
                    upUser.setOnboardDate(DateTimeUtils.getCurrentDate());
                    upUser.setPhone(upPhone);
                    upUser.setDepartName(teamName);
                    userList.add(upUser);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                errorMessage.add(e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }

        return importUsers(userList, errorMessage);
    }

    // 判断是否存在指定船员、部门的船员部门记录
    private boolean isTriDepExists(Trainee trainee, String sysDepartId) {
        if (trainee == null) {
            return false;
        } else {
            // sysTraineeDepart中存在指定记录则存在
            return sysTraineeDepartService.lambdaQuery()
                    .eq(SysTraineeDepart::getTraineeId, trainee.getId())
                    .eq(SysTraineeDepart::getDepId, sysDepartId)
                    .exists();
        }
    }

    @NotNull
    public Result<?> importUsers(List<ImportSysUser> userList, List<String> errorMessage) throws IOException {
        List<String> needResetUsers = new ArrayList<>();
        int successLines = 0, errorLines = errorMessage.size();
        Integer[] counts = new Integer[]{0, 0};
        // 公司套餐列表
        List<Packages> packagesList = new ArrayList<>();
        // 所有套餐余额不足标记  true：余额不足  false：余额充足
        boolean isAllPackagesNotEnough = false;

        //当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        //当前登录用户的部门
        SysDepart sysDepart = sysDepartService.getDepartByOrgCode(loginUser.getOrgCode());

        if (sysDepart.getMemberType() == 1) { // 企业会员模式

            // 获取该公司的所有可用套餐，最先过期的排在前面
            packagesList = packagesService.getAllAvailable(sysDepart.getId());
            if (packagesList.isEmpty()) {
                return Result.error("操作失败，该公司没有可用套餐");
            }
        }

        for (ImportSysUser importSysUser : userList) { //收集需要重置的，放到needResetUsers中
            try {
                //是否已经存在
                Trainee existTrainee = this.baseMapper.getTraineeByIDCard(importSysUser.getIdentityCard());

                if (sysDepart.getMemberType() == 1) { // 企业会员模式
                    // 判断是否存在指定船员、部门的船员部门记录
                    if (!isTriDepExists(existTrainee, sysDepart.getId())) { // 不存在，需要扣除套餐余额

                        if (isAllPackagesNotEnough) { // 所有套餐余额不足，直接抛异常
                            throw new JeecgBootException(CommonConstant.NOT_ENOUGH_BALANCE);
                        }

                        Long packagesId = packagesService.deductSurplusNum(packagesList);

                        // 判断套餐剩余数量是否扣除
                        if (packagesId == null) { // 没有扣除，说明余额不足
                            isAllPackagesNotEnough = true;
                            throw new JeecgBootException(CommonConstant.NOT_ENOUGH_BALANCE);
                        } else { // 扣除成功，设置套餐id
                            importSysUser.setPackagesId(packagesId);
                        }
                    }
                }
                try {
                    //判断哪些人员需要重置；更改部门信息等等
                    // doImportOrUpdateUser调用本类方法导致事务回滚失效，注入自身代理对象，保证事务回滚生效
                    ((TraineeServiceImpl) AopContext.currentProxy()).doImportOrUpdateUser(importSysUser, needResetUsers);
                } catch (Exception e) {
                    // 归还套餐数量
                    packagesService.lambdaUpdate()
                            .setSql("surplus_num = surplus_num + 1")
                            .eq(Packages::getId, importSysUser.getPackagesId())
                            .update();
                    log.info("保存船员失败，归还套餐数量成功，套餐id：{}", importSysUser.getPackagesId());
                    throw new RuntimeException(CommonConstant.SAVE_TRAINEE_FAIL);
                }

                if (importSysUser.getStatus().equals("上船")) { // 上船
                    counts[0]++;
                } else { // 下船
                    counts[1]++;
                }

                successLines++;
            } catch (Exception e) {
                StringBuilder buffer = new StringBuilder();
                try {
                    String message = e.getMessage().toLowerCase();
                    if (message.contains(CommonConstant.SQL_INDEX_UNIQ_SYS_USER_PHONE)) {
                        buffer.append("手机号码已经存在，忽略导入。");
                    } else if (message.contains(CommonConstant.SQL_INDEX_UNIQ_CODE)) {
                        buffer.append("职务编码已经存在，忽略导入。");
                    } else if (message.contains(CommonConstant.SQL_INDEX_UNIQ_DEPART_ORG_CODE)) {
                        buffer.append("部门编码已经存在，忽略导入。");
                    } else if (message.contains(CommonConstant.SQL_INDEX_UNIQ_SYS_USER_WORK_NO)) {
                        buffer.append("工号已经存在，忽略导入。");
                    } else if (message.contains(CommonConstant.SQL_PARENT_ID_LOOP)) {
                        buffer.append("导入的船名，是当前用户部门的上级部门");
                    } else if (message.contains(CommonConstant.NOT_ENOUGH_BALANCE)) {
                        buffer.append("套餐余额不足，忽略导入。");
                    } else if (message.contains(CommonConstant.SAVE_TRAINEE_FAIL)) {
                        buffer.append("船员保存失败，忽略导入。");
                    } else {
                        buffer.append("出现不常见的错误，忽略导入");
                    }
                } catch (Exception exception) {
                    exception.printStackTrace();
                }

                errorLines++;
                log.error("全员导入，姓名：{}，职务：{}，状态：{}，失败原因：{}，错误:{}", importSysUser.getRealname(), importSysUser.getPost(), importSysUser.getStatus(), buffer, e.getMessage());
                errorMessage.add("姓名：" + importSysUser.getRealname() + "，职务：" + importSysUser.getPost() + "，状态：" + importSysUser.getStatus() + "，失败原因：" + buffer);
            }
        }

        if (!needResetUsers.isEmpty()) {
            //异步延迟处理，以便等待这个事务完成。
            new Thread(() -> {
                try {
                    Thread.sleep(2000);
                } catch (Exception ex) {
                }
                initUserTraining(needResetUsers);
            }).start();
        }

        String extMsg = String.format("【上船:%d;下船:%d】", counts[0], counts[1]);

        return ImportExcelUtil.importReturnRes(errorLines, successLines, errorMessage, extMsg);
    }

    @Transactional(rollbackFor = Exception.class)
    public String doImportOrUpdateUser(ImportSysUser importTrainee, List<String> needResetUsers) {

        //当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        //当前登录用户的部门
        SysDepart loginSysDepart = sysUserDepartService.getDepartByUserId(loginUser.getId());

        //是否已经存在
        Trainee existTrainee = this.baseMapper.getTraineeByIDCard(importTrainee.getIdentityCard());
        boolean insertFlag = false;
        String oldTeamId = "";

        if (existTrainee == null) {
            existTrainee = new Trainee();

            String username = importTrainee.getIdentityCard();
            BeanUtils.copyProperties(importTrainee, existTrainee);
            existTrainee.setUsername(username);
            existTrainee.setCreateTime(new Date());
            String salt = oConvertUtils.randomGen(8);
            String password = existTrainee.getIdentityCard().substring(existTrainee.getIdentityCard().length() - 6);
            String passwordEncode = PasswordUtil.encrypt(username, password, salt);
            existTrainee.setSalt(salt);
            existTrainee.setPassword(passwordEncode);
            existTrainee.setDelFlag(CommonConstant.DEL_FLAG_0);
            existTrainee.setActivitiSync(CommonConstant.ACT_SYNC_0);
            insertFlag = true;

            //导入时orgCode默认为导入人的部门Code
            existTrainee.setOrgCode(loginUser.getOrgCode());
            existTrainee.setDepRoute(loginUser.getDepRoute());
            existTrainee.setRecommendFrom(loginSysDepart.getId());
        } else {
            oldTeamId = existTrainee.getTeamId();
            //设置部门路径
            existTrainee.setOrgCode(loginUser.getOrgCode());
            existTrainee.setDepRoute(loginUser.getDepRoute());
            //导入信息覆盖原始信息

            BeanUtils.copyProperties(importTrainee, existTrainee);
            existTrainee.setUsername(importTrainee.getIdentityCard());
            // 不用恢复状态，后面要做判断
//            existTrainee.setDelFlag(CommonConstant.DEL_FLAG_0);
        }

        Team team = null;
        //如果导入的船名称不为空，则进行保存到船队信息表中
        if (StringUtils.isNotEmpty(importTrainee.getDepartName())) {
            //船
            team = teamService.getOne(
                    new LambdaQueryWrapper<Team>()
                            .eq(Team::getTeamName, importTrainee.getDepartName())
                            .eq(Team::getRoute, loginUser.getDepRoute()), false);
            if (team == null) {
                //创建新的船
                team = new Team();
                //添加到当前登录用所在部门下面
                team.setParentId(loginSysDepart.getId());
                team.setTeamName(importTrainee.getDepartName());
                teamService.saveTeamData(team, loginUser.getDepRoute());
            }

            //船名存在时，用户对应的船Id为最后一个导入船的id
            existTrainee.setTeamId(team.getId());
        }

        //0 无需重置考试和推送,1 仅重置，2 重置和推送
        int resetExam = 0;
        if ("下船".equals(importTrainee.getStatus())) {
            //不是新船员，才需要处理，否则无需做任何处理
            if (!insertFlag) {
                //只需要重置考试，
                resetExam = 1;
                //不是新船员，之前为未删除并且未冻结状态才需要将原有的team表里的数量减1
                if (NumberUtil.equalsValue(existTrainee.getStatus(), CommonConstant.USER_UNFREEZE) && NumberUtil.equalsValue(existTrainee.getDelFlag(), CommonConstant.DEL_FLAG_0)) {
                    teamService.update(
                            new LambdaUpdateWrapper<Team>()
                                    .eq(Team::getId, oldTeamId)
                                    .setSql("trainee_count = trainee_count - 1")
                    );
                }
            }
            existTrainee.setStatus(CommonConstant.USER_FREEZE);
        } else if ("上船".equals(importTrainee.getStatus())) {
            if (team != null) {
                //判断是新船员还是老船员
                if (insertFlag) {
                    //新船员，将选择的team的受试人数量+1就行了
                    teamService.update(
                            new LambdaUpdateWrapper<Team>()
                                    .eq(Team::getId, team.getId())
                                    .setSql("trainee_count = trainee_count + 1")
                    );
                } else {
                    //不是新船员
                    //判断是否换船
                    if (team.getId().equals(oldTeamId)) {
                        //如果没有换船，判断状态是否为冻结或者删除，如果是，现有的船受试人加一
                        if (NumberUtil.equalsValue(existTrainee.getStatus(), CommonConstant.USER_FREEZE) || NumberUtil.equalsValue(existTrainee.getDelFlag(), CommonConstant.DEL_FLAG_1)) {
                            teamService.update(
                                    new LambdaUpdateWrapper<Team>()
                                            .eq(Team::getId, team.getId())
                                            .setSql("trainee_count = trainee_count + 1")
                            );
                        }
                        //如果是正常状态则不需要处理
                    } else {
                        //如果换船了,无论之前船员是什么状态，现有的船都要加一
                        teamService.update(
                                new LambdaUpdateWrapper<Team>()
                                        .eq(Team::getId, team.getId())
                                        .setSql("trainee_count = trainee_count + 1")
                        );
                        //如果是正常状态，原有的船要数量要减一
                        if (NumberUtil.equalsValue(existTrainee.getStatus(), CommonConstant.USER_UNFREEZE) && NumberUtil.equalsValue(existTrainee.getDelFlag(), CommonConstant.DEL_FLAG_0)) {
                            teamService.update(
                                    new LambdaUpdateWrapper<Team>()
                                            .eq(Team::getId, oldTeamId)
                                            .setSql("trainee_count = trainee_count - 1")
                            );
                        }
                    }
                }
            }

            //需要重置考试和推送
            resetExam = !NumberUtil.equalsValue(existTrainee.getStatus(), CommonConstant.USER_UNFREEZE)
                    || !oldTeamId.equals(existTrainee.getTeamId()) ? 2 : 0;

            existTrainee.setStatus(CommonConstant.USER_UNFREEZE);
        }

        //职务不存在则新建
        SysPosition position = positionService.getByCode(importTrainee.getPost(), loginUser.getDepRoute());
        if (position == null) {
            position = new SysPosition();
            position.setName(importTrainee.getPost());
            //默认code与name一样
            position.setCode(importTrainee.getPost());
            position.setDepRoute(loginUser.getDepRoute());
            positionService.save(position);
        }

        existTrainee.setUpdateTime(new Date());

        if (insertFlag) {
            this.save(existTrainee);
        } else {
            traineeMapper.revertLogicDeleted(existTrainee.getId().toLowerCase(), existTrainee);
            this.baseMapper.updateById(existTrainee);
            // 同步trainee表
            syncStateService.resetSyncState(CommonConstant.TRAINEE, existTrainee.getId());
        }

        if (sysTraineeDepartLogService.lambdaQuery()
                .eq(SysTraineeDepartLog::getTraineeId, existTrainee.getId())
                .eq(SysTraineeDepartLog::getDepId, loginSysDepart.getId())
                .exists()) { // 系统中存在船员船舶关联表，则关联次数加1
            sysTraineeDepartLogService.lambdaUpdate()
                    .setSql("link_times = link_times + 1")
                    .set(SysTraineeDepartLog::getUpdateTime, new Date())
                    .eq(SysTraineeDepartLog::getTraineeId, existTrainee.getId())
                    .eq(SysTraineeDepartLog::getDepId, loginSysDepart.getId())
                    .update();
        } else { // 系统中不存在，则新增
            sysTraineeDepartLogService.save(new SysTraineeDepartLog(existTrainee.getId(),
                    existTrainee.getRealname(), loginSysDepart.getId(),
                    loginSysDepart.getDepartName(), new Date(), 1));
        }

        //判断当前用户是否已经分配了该部门
        List<DepartIdModel> departIdModels = sysTraineeDepartService.queryDepartIdsOfUser(existTrainee.getId());
        boolean saveUserDepartFlag = departIdModels == null || departIdModels.stream().noneMatch(d -> d.getKey().equals(loginSysDepart.getId()));

        //保存用户与部门关系
        if (saveUserDepartFlag) {
            // 导入受试人时，可以不填写船名。导入时，谁导入的，就给这个船员添加一个公司关联关系（即部门）
            //在sys_trainee_depart表中，添加一条记录
            SysTraineeDepart sysTraineeDepart = new SysTraineeDepart(existTrainee.getId(), loginSysDepart.getId());
            sysTraineeDepartService.save(sysTraineeDepart);
            //推送应该和部门关系不大
//            resetExam = 2;
        }

        //如果是新船员，则需要重置、推送考试绑定套餐
        if (insertFlag) {
            resetExam = 2;
        }

        //如果是上船，并且船名有变化的，需要重置
        if (resetExam > 0) {
            needResetUsers.add(existTrainee.getId() + "," + resetExam);
        }

        // 如果套餐扣除了，则船员需要开通会员
        if (existTrainee.getPackagesId() != null) {
            sysTraineeDepartService.changePackagesId(existTrainee, loginSysDepart.getId(), existTrainee.getPackagesId());
        }

        SysUserLog sysUserLog = new SysUserLog();
        BeanUtils.copyProperties(existTrainee, sysUserLog);
        sysUserLog.setId(null);
        sysUserLog.setUserId(existTrainee.getId());
        sysUserLog.setImportTime(new Date());
        sysUserLogService.save(sysUserLog);

        return existTrainee.getId();

    }


    //推送考试级阶段数据
    private void initUserTraining(List<String> changedUsers) {
        if (changedUsers == null || changedUsers.isEmpty())
            return;

        //1,2，都是要重置考试的。
        List<String> ids = changedUsers.stream().map(s -> s.split(",")[0]).collect(Collectors.toList());
        //重置考试和阶段
        List<Trainee> trainees = this.listByIds(ids);
        for (Trainee trainee : trainees) {
            // 重置考试
            int rlt = examMapper.resetUserExam(trainee.getId());
            log.info("Reset exam of user :" + trainee.getId() + ";" + rlt);
            // 重置考试推送
            rlt = userPaperMapper.resetUserPaper(trainee.getId());
            log.info("Reset paper of user :" + trainee.getId() + ";" + rlt);
            // 重置阶段
            rlt = userPhaseService.resetUserPhase(trainee);
            log.info("Reset phase of user :" + trainee.getId() + ";" + rlt);
        }

        //2 是上船，需要重新推送考试和阶段的
        ids = changedUsers.stream().filter(s -> s.split(",")[1].equals("2")).map(s -> s.split(",")[0]).collect(Collectors.toList());
        if (!ids.isEmpty()) {
            trainees = this.listByIds(ids);

            //重新计算考试推送
            pushPaperService.pushUsers(ids);

            //重新计算阶段推送
            userPhaseHandleService.pushTrainee(trainees);
        }
    }

    private String checkIdCardNumber(String idCardNumber) {
        if (idCardNumber == null || !idCardNumber.matches(idExpression))
            return "长度或规则不合法!";
        else if (idCardNumber.length() == 18) {
            try {
                char[] charArray = idCardNumber.toCharArray();
                //前十七位加权因子
                int sum = 0;
                for (int i = 0; i < idCardWi.length; i++) {
                    int current = Integer.parseInt(String.valueOf(charArray[i]));
                    int count = current * idCardWi[i];
                    sum += count;
                }
                char idCardLast = charArray[17];
                int idCardMod = sum % 11;
                if (idCardY[idCardMod].equals(String.valueOf(idCardLast).toUpperCase())) {
                    return null;
                } else {
                    return "校验失败!";
                }

            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }

        return null;
    }

    @Override
    public IPage<Trainee> queryDepartTraineePageList(String departId, String id, String username, String realname, Integer pageSize, Integer pageNo) {
        IPage<Trainee> pageList = null;
        // 部门ID不存在 直接查询用户表即可
        Page<Trainee> page = new Page<Trainee>(pageNo, pageSize);
        if (oConvertUtils.isEmpty(departId)) {
            LambdaQueryWrapper<Trainee> query = new LambdaQueryWrapper<>();
            query.eq(Trainee::getStatus, Integer.parseInt(CommonConstant.STATUS_1))
                    .like(oConvertUtils.isNotEmpty(username), Trainee::getUsername, username)
                    .like(oConvertUtils.isNotEmpty(realname), Trainee::getRealname, realname)
                    .eq(oConvertUtils.isNotEmpty(id), Trainee::getId, id);

            pageList = traineeMapper.selectPage(page, query);
        } else {
            // 有部门ID 需要走自定义sql
            SysDepart sysDepart = sysDepartService.getById(departId);
            pageList = this.baseMapper.queryDepartTraineePageList(page, sysDepart.getOrgCode(), username, realname);
        }
        List<Trainee> userList = pageList.getRecords();
        if (userList != null && userList.size() > 0) {
            List<String> userIds = userList.stream().map(Trainee::getId).collect(Collectors.toList());
            Map<String, Trainee> map = new HashMap<>(5);
            if (userIds.size() > 0) {
                // 查部门名称
                Map<String, String> useDepNames = this.getDepNamesByTraineeIds(userIds);
                userList.forEach(item -> {
                    //TODO 临时借用这个字段用于页面展示
                    item.setOrgCodeTxt(useDepNames.get(item.getId()));
                    item.setSalt("");
                    item.setPassword("");
                    // 去重
                    map.put(item.getId(), item);
                });
            }
            pageList.setRecords(new ArrayList<Trainee>(map.values()));
        }
        return pageList;
    }

    private Map<String, String> getDepNamesByTraineeIds(List<String> userIds) {
        List<TraineeVO> list = traineeMapper.getDepNamesByTraineeIds(userIds);

        Map<String, String> res = new HashMap(5);
        list.forEach(item -> {
                    if (res.get(item.getId()) == null) {
                        res.put(item.getId(), item.getDepartName());
                    } else {
                        res.put(item.getId(), res.get(item.getId()) + "," + item.getDepartName());
                    }
                }
        );
        return res;
    }

    @Override
    public IPage<TraineeVO> getExamUserForPhase(Page<TraineeVO> page, QueryWrapper<TraineeVO> queryWrapper) {
        return baseMapper.getExamUserForPhase(page, queryWrapper);
    }

    @Override
    public IPage<TraineeVO> getExamUserForPhaseItem(Page<TraineeVO> page, QueryWrapper<TraineeVO> queryWrapper) {
        return baseMapper.getExamUserForPhaseItem(page, queryWrapper);
    }


    @Override
    public Trainee getUserByOpenId(String openId) {
        return baseMapper.getUserByOpenId(openId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Trainee signInUser(MiniProgramDTO miniProgramDTO) {

        //部门(船)
        Team team = teamService.queryTeamByName(miniProgramDTO.getShipName(), miniProgramDTO.getDeptRoute());
        //分享人登录用户部门
        SysDepart loginSysDepart = sysUserDepartService.getDepartByUserId(miniProgramDTO.getShareId());
        String identityCard = miniProgramDTO.getIdentityCard();
        String sysDepartId = loginSysDepart.getId();

        Trainee trainee = new Trainee();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        //上船时间为空，使用当前时间
        Date onboardDate = new Date();

        try {
            if (StringUtils.isNotEmpty(miniProgramDTO.getOnboardDate())) {
                onboardDate = format.parse(miniProgramDTO.getOnboardDate());
            }

            if (team == null) {
                //创建新的部门
                team = new Team();
                //添加到当前登录用所在部门下面
                team.setParentId(sysDepartId);
                team.setTeamName(miniProgramDTO.getShipName());
                teamService.saveTeamData(team, miniProgramDTO.getDeptRoute());
            }

            //新增用户信息
            trainee.setOnboardDate(onboardDate);
            trainee.setStatus(Integer.parseInt(miniProgramDTO.getStatus()));
            trainee.setAvatar(miniProgramDTO.getAvatar());
            trainee.setOpenId(miniProgramDTO.getOpenId());
            trainee.setPhone(miniProgramDTO.getPhone());
            trainee.setIdentityCard(identityCard);
            trainee.setRealname(miniProgramDTO.getRealName());
            trainee.setOrgCodeTxt(miniProgramDTO.getShipName());
            trainee.setPost(miniProgramDTO.getPost());
            setCommonTraineeInfo(trainee, loginSysDepart, team);

            this.save(trainee);

            // 初始化船员设置
            initTrainee(trainee, loginSysDepart, team);

        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            trainee = null;
            log.error("注册用户出现异常！", e);
        }

        return trainee;
    }

    // 解除受试人和openId的关系
    @Override
    public void unLinkTraineeToOpenId(Long id) {
        // 设置该用户的openId为null
        this.update(new LambdaUpdateWrapper<Trainee>()
                .set(Trainee::getOpenId, null)
                .eq(Trainee::getId, id));
    }

    @Override
    public List<TraineeVO> getAllExamUserForPhase(QueryWrapper<TraineeVO> wrapper) {
        return baseMapper.getAllExamUserForPhase(wrapper);
    }

    @Override
    public void setTraineeInfo(JSONObject jsonObject, ImportSysUser importSysUser) {

        //获取船名
        String teamName = jsonObject.getString("teamName");

        //设置船名
        importSysUser.setDepartName(teamName);
        //设置出生日期
        importSysUser.setBirthday(getDateByIdentityCard(importSysUser.getIdentityCard()));

        //设置上下船
        if ("1".equals(jsonObject.getString("status"))) {
            importSysUser.setStatus("上船");
        } else {
            importSysUser.setStatus("下船");
        }
    }

    @Override
    public List<Trainee> selectByIds(List<String> list) {
        return baseMapper.selectBatchIds(list);
    }

    private Date getDateByIdentityCard(String identityCard) {

        //获取出生日期字符串
        String birthDate = identityCard.substring(6, 14);
        //字符串转日期
        Date date = DateUtil.parse(birthDate);
        //日期转指定格式
        String format = DateUtil.format(date, "yyyy-MM-dd");
        //返回指定格式的日期
        return DateUtil.parse(format);

    }

    // 船员注册
    @Override
    public Trainee register(TraineeRegisterDTO traineeRegisterDTO, SysDepart sysDepart, Team team) {

        Trainee trainee = new Trainee();
        BeanUtil.copyProperties(traineeRegisterDTO, trainee);

        // 设置新船员常见属性
        setCommonTraineeInfo(trainee, sysDepart, team);
        save(trainee);

        // 初始化船员设置
        initTrainee(trainee, sysDepart, team);

        return trainee;
    }


    // 新船员常见属性赋值
    private void setCommonTraineeInfo(Trainee trainee, SysDepart sysDepart, Team team) {

        //职务不存在则新建
        SysPosition position = positionService.getByCode(trainee.getPost(), sysDepart.getRoute());
        if (position == null) {
            position = new SysPosition();
            position.setName(trainee.getPost());
            //默认code与name一样
            position.setCode(trainee.getPost());
            position.setDepRoute(sysDepart.getRoute());
            positionService.save(position);
        }

        // 设置新增船员信息
        if (trainee.getStatus() == null) {
            trainee.setStatus(1); // 上船
        }
        trainee.setPost(position.getName());
        if (trainee.getOnboardDate() == null) {
            trainee.setOnboardDate(DateTimeUtils.getCurrentDate());
        }
        trainee.setUsername(trainee.getIdentityCard());
        String salt = oConvertUtils.randomGen(8);
        String password = trainee.getIdentityCard().substring(trainee.getIdentityCard().length() - 6);
        String passwordEncode = PasswordUtil.encrypt(trainee.getUsername(), password, salt);
        trainee.setSalt(salt);
        trainee.setPassword(passwordEncode);
        trainee.setDelFlag(CommonConstant.DEL_FLAG_0);
        trainee.setActivitiSync(CommonConstant.ACT_SYNC_0);
        trainee.setOrgCode(sysDepart.getOrgCode());
        trainee.setDepRoute(sysDepart.getRoute());
        trainee.setTeamId(team.getId());
        trainee.setRecommendFrom(sysDepart.getId());
    }

    // 新船员初始化设置
    private void initTrainee(Trainee trainee, SysDepart sysDepart, Team team) {

        // 对应新船的受试人数量加一
        teamService.update(
                new LambdaUpdateWrapper<Team>()
                        .eq(Team::getId, team.getId())
                        .setSql("trainee_count = trainee_count + 1")
        );

        // 新增船员公司日志记录
        sysTraineeDepartLogService.save(new SysTraineeDepartLog(trainee.getId(),
                trainee.getRealname(), sysDepart.getId(),
                sysDepart.getDepartName(), new Date(), 1));

        //保存受试人与部门关系
        SysTraineeDepart sysTraineeDepart = new SysTraineeDepart(trainee.getId(), sysDepart.getId());
        sysTraineeDepartService.save(sysTraineeDepart);

        //考试推送
        pushPaperService.pushUsers(Collections.singletonList(trainee.getId()));

        //阶段推送
        userPhaseHandleService.pushTrainee(Collections.singletonList(trainee));

        //用户操作日志
        SysUserLog sysUserLog = new SysUserLog();
        BeanUtils.copyProperties(trainee, sysUserLog);
        sysUserLog.setId(null);
        sysUserLog.setUserId(trainee.getId());
        sysUserLog.setImportTime(new Date());
        sysUserLogService.save(sysUserLog);
    }

    // 获取即将过期（一个月内到期）和已经到期的会员数量
    @Override
    public JSONArray getWillAndExpireMemberCount() {

        JSONArray jsonArray = new JSONArray();

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("操作失败！获取当前登录用户失败");
        }

        // 获取当前登录公司id
        String sysDepartId = StrUtil.subAfter(loginUser.getDepRoute(), "/", true);
        SysDepart sysDepart = sysDepartService.getById(sysDepartId);
        if (sysDepart == null) {
            log.error("操作失败！找不到sysDepartId：{}对应的公司", sysDepartId);
            throw new JeecgBootException("操作失败！获取不到当前登录部门");
        }

        // 获取已经过期
        jsonArray
                .fluentAdd(new JSONObject()
                        .fluentPut("type", 0) // 获取即将过期
                        .fluentPut("count",traineeMapper.getWillExpireCount(loginUser.getDepRoute(), sysDepart.getMemberType(), sysDepartId)))
                .fluentAdd(new JSONObject()
                        .fluentPut("type", 1) // 获取已经过期
                        .fluentPut("count",traineeMapper.getExpireCount(loginUser.getDepRoute(), sysDepart.getMemberType(), sysDepartId)));

       return jsonArray;
    }

    // 企业会员模式下，对已有船员执行会员数量扣除操作
    @Override
    public JSONArray deductMember() {

        // 成功消息集合
        List<String> successList = new ArrayList<>();
        // 失败消息集合
        List<String> failList = new ArrayList<>();

        // 获取所有企业会员模式的公司
        sysDepartService.lambdaQuery()
                .eq(SysDepart::getMemberType, 1)
                .list()
                .forEach(sysDepart -> {

                    // 获取企业会员公司没有开企业会员的船员
                    List<SysTraineeDepart> sysTraineeDepartList = sysTraineeDepartMapper
                            .getCompanyNoMember(sysDepart.getId());

                    if (sysTraineeDepartList.isEmpty()) {
                        log.info("公司{}没有未开通企业会员的船员，跳过", sysDepart.getDepartName());
                        successList.add(sysDepart.getDepartName() + "公司没有未开通企业会员的船员！跳过");
                        return;
                    }

                    // 获取该公司的所有可用套餐，最先过期的排在前面
                    List<Packages> packagesList = packagesService.getAllAvailable(sysDepart.getId());

                    if (packagesList.isEmpty()) {
                        log.error("公司{}没有可用套餐，跳过", sysDepart.getDepartName());
                        failList.add(sysDepart.getDepartName() + "公司没有可用套餐！跳过");
                        return;
                    }

                    // 有企业套餐
                    for (SysTraineeDepart sysTraineeDepart : sysTraineeDepartList) {
                        // 获取船员对象
                        Trainee trainee = getById(sysTraineeDepart.getTraineeId());
                        // 执行套餐扣除
                        Long packagesId = packagesService.deductSurplusNum(packagesList);

                        // 判断套餐剩余数量是否扣除
                        if (packagesId == null) { // 没有扣除，说明所有套餐余额不足
                            failList.add("船员：" + trainee.getRealname() + "开通企业会员失败。" + sysDepart.getDepartName() + "公司所有套餐余额不足！跳过该公司");
                            return;
                        } else { // 扣除成功，设置套餐id

                            trainee.setPackagesId(packagesId);
                            sysTraineeDepartService.changePackagesId(trainee, sysDepart.getId(), packagesId);

                            successList.add("船员：" + trainee.getRealname() + "开通企业会员成功。");
                        }
                    }
                });
        return new JSONArray()
                .fluentAdd(new JSONObject().fluentPut("successList", successList))
                .fluentAdd(new JSONObject().fluentPut("failList", failList));
    }

    // 检查套餐是否过期
    @Override
    public void checkPackagesExpire() {
        LocalDate today = LocalDate.now();

        packagesService.lambdaQuery()
                .eq(Packages::getStatus, 1) // 获取可用套餐
                .list()
                .stream()
                .filter(Packages::isExpired) // 使用实体类的过期判定方法
                .forEach(pkg -> {

                    if (pkg.getType() == 0) { // 个人付费套餐

                        // 获取相邻的没有过期的套餐id
                        Packages packages = packagesService.getOne(new LambdaQueryWrapper<Packages>()
                                .eq(Packages::getTraineeId, pkg.getTraineeId())
                                .eq(Packages::getType, 0)
                                .eq(Packages::getStatus, 1)
                                .le(Packages::getStartDate, today) // 开始时间小于等于今天
                                .ge(Packages::getExpireDate, today), false); // 结束时间大于等于今天

                        if (packages != null) {
                            // 获取该套餐对应的船员
                            Trainee trainee = getById(pkg.getTraineeId());
                            if (trainee == null) {
                                log.error("定时检查套餐过期时，套餐id={}对应的船员id={}不存在，跳过", pkg.getId(), pkg.getTraineeId());
                                return;
                            }

                            if (!packages.getId().equals(trainee.getCurrentPackagesId())) {
                                lambdaUpdate()
                                        .set(Trainee::getCurrentPackagesId, packages.getId())
                                        .eq(Trainee::getId, pkg.getTraineeId())
                                        .update();

                                // 修改同步状态
                                syncStateService.resetSyncState(CommonConstant.TRAINEE, trainee.getId());

                                log.info("船员{}的个人套餐id={}已过期，切换为id={}", trainee.getRealname(), pkg.getId(), packages.getId());
                            }
                        }
                    }

                    // 修改套餐状态为禁用
                    packagesService.lambdaUpdate()
                            .set(Packages::getStatus, 0)
                            .eq(Packages::getId, pkg.getId())
                            .update();

                    syncStateService.resetSyncState(CommonConstant.PACKAGES, String.valueOf(pkg.getId()));

                    log.info("{}套餐过期，修改状态为禁用", pkg.getId());
                });
    }

    // 船员开通船员会员
    @Override
    public void openMember(Trainee trainee, Long ordersId) {

        log.info("船员：{} 待开通会员", trainee.getRealname());

        // 新增个人付费套餐
        Packages newPackages = new Packages();
        // 是否需要修改使用中的套餐id
        boolean isExpire = false;

        // 设置常用属性
        newPackages.setType(0);
        newPackages.setTraineeId(trainee.getId());
        newPackages.setName(trainee.getRealname());
        newPackages.setMemberNum(1);
        newPackages.setSurplusNum(0);
        newPackages.setOrdersId(ordersId);

        LocalDate today = LocalDate.now();

        if (trainee.getLastPackagesId() == null) { // 第一次购买
            log.info("船员：{} 第一次购买会员", trainee.getRealname());
            // 设置开始时间为今天
            newPackages.setStartDate(today);
            // 需要修改使用中的套餐id
            isExpire = true;
        } else { // 非首次购买
            log.info("船员：{} 非第一次购买会员", trainee.getRealname());
            // 获取最近购买的套餐
            Packages lastPackages = packagesService.getOne(new LambdaQueryWrapper<Packages>()
                    .eq(Packages::getId, trainee.getLastPackagesId())
                    .eq(Packages::getStatus, 1)
                    .gt(Packages::getExpireDate, today) // 过期时间大于今天
                    .eq(Packages::getType, 0), false);

            if (lastPackages != null) { // 最近一次购买的套餐没有过期
                // 新套餐开始时间为最近一次套餐的过期时间加1天
                newPackages.setStartDate(lastPackages.getExpireDate().plusDays(1));
            } else { // 最近一次购买的套餐过期
                // 新套餐开始时间为今天
                newPackages.setStartDate(today);
                isExpire = true;
            }
        }

        // 设置过期时间：开始时间加1年
        // 例如：2024-01-01开始，过期时间为2025-01-01
        LocalDate expireDate = newPackages.getStartDate().plusYears(1);
        newPackages.setExpireDate(expireDate);

        packagesService.save(newPackages);

        // 修改船员对应的套餐id
        lambdaUpdate()
                .set(isExpire, Trainee::getCurrentPackagesId, newPackages.getId()) // 仅在 isExpire 为 true 时更新
                .set(Trainee::getLastPackagesId, newPackages.getId())
                .eq(Trainee::getId, trainee.getId())
                .update();

        // 修改同步状态
        syncStateService.resetSyncState(CommonConstant.TRAINEE, trainee.getId());
    }
}
