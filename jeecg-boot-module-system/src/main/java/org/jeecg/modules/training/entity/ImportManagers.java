package org.jeecg.modules.training.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年11月26日 17:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ImportManagers {

    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 15)
    private String sysUserId;
    /**
     * 用户身份
     */
    @Excel(name = "用户身份", width = 15)
    private String userStatus;
    /**
     * 所属船队
     */
    @Excel(name = "所属船队", width = 15)
    private String fleet;
    /**
     * 船名
     */
    @Excel(name = "船名", width = 15)
    private String departId;

    /**
     * 船名
     */
    @Excel(name = "当前所在船舶名称", width = 15)
    private String departName;
    /**
     * 删除状态（0，正常，1已删除）
     */
    @Excel(name = "删除状态（0，正常，1已删除）", width = 15)
    private Integer delFlag;
}
