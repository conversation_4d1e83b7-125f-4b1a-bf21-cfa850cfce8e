package org.jeecg.modules.training.controller.admin;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.training.service.StudyTimePerFleetService;
import org.jeecg.modules.training.vo.StudyTimePerFleetVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 每个学习资料船队每日时长表(StudyTimePerFleet)表控制层
 *
 * <AUTHOR>
 * @since 2023-12-28 15:39:49
 */
@RestController
@RequestMapping("/adm/studyTimePerFleet")
@Api(tags = "船队学习资料时长")
@Slf4j
public class StudyTimePerFleetController {
    /**
     * 服务对象
     */
    @Resource
    private StudyTimePerFleetService studyTimePerFleetService;

    @ApiOperation("查询船队学习资料时间排名")
    @GetMapping("/list")
    public Result<Page<StudyTimePerFleetVO>> list(StudyTimePerFleetVO studyTimePerFleetVO,
                                                  @RequestParam(name = "scope", required = false) String scope,
                                                  @RequestParam Integer pageNo,
                                                  @RequestParam Integer pageSize,
                                                  HttpServletRequest req
    ) {
        Page<StudyTimePerFleetVO> page = new Page<>(pageNo, pageSize);

        QueryWrapper<StudyTimePerFleetVO> queryWrapper = QueryGenerator.initQueryWrapper(studyTimePerFleetVO, req.getParameterMap());
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        try {
            Page<StudyTimePerFleetVO> pageList = studyTimePerFleetService.getPageList(page, queryWrapper, loginUser.getDepRoute(), scope);

            return Result.ok(pageList);
        } catch (Exception ex) {
            log.error("EXP", ex);
            return Result.error(ex.getMessage());
        }
    }
}

