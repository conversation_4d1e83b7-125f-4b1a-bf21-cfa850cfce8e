package org.jeecg.modules.training.controller.admin;


import java.time.LocalDate;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBoot401Exception;
import org.jeecg.common.fields.FieldFilter;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.training.entity.Packages;
import org.jeecg.modules.training.service.IPackagesLogService;
import org.jeecg.modules.training.service.IPackagesService;
import org.jeecg.modules.training.vo.PackagesVO;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/adm/packages")
public class PackagesController {

    @Autowired
    private IPackagesService packagesService;

    @Autowired
    private ISysDepartService sysDepartService;

    @Autowired
    private IPackagesLogService packagesLogService;

    @Autowired
    private ISyncStateService syncStateService;

    // 分页查询
//    createTime,createTime,expireTime,id,memberNum,name,status,surplusNum,sysDepartId
    @FieldFilter({"createTime", "expireTime", "id", "memberNum", "name", "surplusNum",
            "status", "sysDepartId"})
    @GetMapping(value = "/pageList")
    public Result<?> queryPageList(PackagesVO packagesVO,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<PackagesVO> queryWrapper = QueryGenerator.initQueryWrapper(packagesVO, req.getParameterMap());

        Page<PackagesVO> page = new Page<>(pageNo, pageSize);
        IPage<PackagesVO> pageList = packagesService.listPages(page, queryWrapper);

        return Result.OK(pageList);
    }

    // 新增
    @PostMapping("/add")
    public Result<?> add(@RequestBody PackagesVO packagesVO) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (oConvertUtils.isEmpty(loginUser)) {
            // 直接抛出401异常，会被全局异常处理器处理
            throw new JeecgBoot401Exception(CommonConstant.TOKEN_IS_INVALID_MSG);
        }

        // 权限验证：只有admin用户可以调用此接口
        if (!"admin".equals(loginUser.getUsername())) {
            return Result.error(403, "权限不足，只有admin用户可以执行此操作");
        }

        if (packagesService
                .lambdaQuery()
                .eq(Packages::getName, packagesVO.getName())
                .exists()) {
            return Result.error("操作失败，存在同名的套餐");
        }

        // 获取创建公司的对象
        SysDepart sysDepart = sysDepartService.getById(packagesVO.getSysDepartId());
        if (sysDepart == null) {
            return Result.error("创建失败，所选公司在系统中不存在");
        }

        packagesVO.setDepRoute(sysDepart.getRoute());
        packagesVO.setStatus(1);
        packagesVO.setType(1);
        // 设置购买时间和过期时间
        LocalDate today = LocalDate.now();
        packagesVO.setStartDate(today);

        // 计算过期时间：开始时间加n个月减1天
        // 例如：2024-01-01开始，激活3个月，过期时间为2024-03-31
        LocalDate expireDate = today.plusMonths(packagesVO.getActivationMonths()).minusDays(1);
        packagesVO.setExpireDate(expireDate);
        packagesVO.setSurplusNum(packagesVO.getMemberNum());

        packagesService.save(packagesVO);

        // 记录新增操作日志
        packagesLogService.createAddOrDeleteLog(packagesVO.getId(), loginUser.getId(), 0);

        return Result.ok("新增成功！");
    }

    // 删除
    @DeleteMapping("/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (oConvertUtils.isEmpty(loginUser)) {
            // 直接抛出401异常，会被全局异常处理器处理
            throw new JeecgBoot401Exception(CommonConstant.TOKEN_IS_INVALID_MSG);
        }

        // 权限验证：只有admin用户可以调用此接口
        if (!"admin".equals(loginUser.getUsername())) {
            return Result.error(403, "权限不足，只有admin用户可以执行此操作");
        }

        // 获取要删除的套餐
        Packages packages = packagesService.getById(id);
        if (packages == null) {
            return Result.error("删除失败，所选套餐在系统中不存在");
        }
        if (!packages.getMemberNum().equals(packages.getSurplusNum())) {
            return Result.error("删除失败，已被使用的套餐不能删除！");
        }

        packagesService.removeById(id);
        // 记录删除操作日志
        packagesLogService.createAddOrDeleteLog(packages.getId(), loginUser.getId(), 2);
        // 修改同步状态
        syncStateService.resetSyncState(CommonConstant.PACKAGES, String.valueOf(packages.getId()));

        return Result.ok("删除成功！");
    }

    // 批量删除
    @DeleteMapping("/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (oConvertUtils.isEmpty(loginUser)) {
            // 直接抛出401异常，会被全局异常处理器处理
            throw new JeecgBoot401Exception(CommonConstant.TOKEN_IS_INVALID_MSG);
        }

        // 权限验证：只有admin用户可以调用此接口
        if (!"admin".equals(loginUser.getUsername())) {
            return Result.error(403, "权限不足，只有admin用户可以执行此操作");
        }

        String[] idList = ids.split(",");

        for (String id : idList) {
            // 获取要删除的套餐
            Packages packages = packagesService.getById(id);
            if (packages == null) {
                return Result.error("操作失败，所选套餐在系统中不存在");
            }
            if (!packages.getMemberNum().equals(packages.getSurplusNum())) {
                return Result.error("操作失败，存在已被使用的套餐，不能删除！");
            }
            packagesService.removeById(id);
            // 记录删除操作日志
            packagesLogService.createAddOrDeleteLog(packages.getId(), loginUser.getId(), 2);
            // 修改同步状态
            syncStateService.resetSyncState(CommonConstant.PACKAGES, String.valueOf(packages.getId()));
        }

        return Result.OK("批量删除成功！");
    }

    // 套餐续费
    @PutMapping("/renew")
    public Result<?> renew(@RequestBody PackagesVO packagesVO) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (oConvertUtils.isEmpty(loginUser)) {
            // 直接抛出401异常，会被全局异常处理器处理
            throw new JeecgBoot401Exception(CommonConstant.TOKEN_IS_INVALID_MSG);
        }

        // 权限验证：只有admin用户可以调用此接口
        if (!"admin".equals(loginUser.getUsername())) {
            return Result.error(403, "权限不足，只有admin用户可以执行此操作");
        }

        // 获取续费套餐对象
        Packages packages = packagesService.getById(packagesVO.getId());
        if (packages == null) {
            return Result.error("续费失败，所选套餐在系统中不存在");
        }

        // 设置新过期时长：在原过期时间基础上增加n个月
        LocalDate newExpireDate = packages.getExpireDate().plusMonths(packagesVO.getActivationMonths());
        packagesVO.setExpireDate(newExpireDate);
        packagesVO.setSurplusNum(packages.getMemberNum());

        // 修改
        packagesService
                .lambdaUpdate()
                .set(Packages::getExpireDate, packagesVO.getExpireDate())
                .set(Packages::getStatus, 1)
                .set(Packages::getSurplusNum, packagesVO.getSurplusNum())
                .eq(Packages::getId, packagesVO.getId())
                .update();

        packagesLogService.createRenewLog(packagesVO, packages, loginUser.getId());
        // 修改同步状态
        syncStateService.resetSyncState(CommonConstant.PACKAGES, String.valueOf(packages.getId()));

        return Result.ok("续费成功！");
    }

    // 修改企业套餐等级
    @PutMapping("/changeDepartPackagesLev")
    public Result<?> changeDepartPackagesLev(@RequestParam(name = "depId") String depId,
                                             @RequestParam(name = "level") Short level) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (oConvertUtils.isEmpty(loginUser)) {
            // 直接抛出401异常，会被全局异常处理器处理
            throw new JeecgBoot401Exception(CommonConstant.TOKEN_IS_INVALID_MSG);
        }

        // 权限验证：只有admin用户可以调用此接口
        if (!"admin".equals(loginUser.getUsername())) {
            return Result.error(403, "权限不足，只有admin用户可以执行此操作");
        }

        // 参数验证
        if (depId == null || depId.trim().isEmpty()) {
            return Result.error("参数错误，部门ID不能为空");
        }
        if (level == null || level < 1 || level > 3) {
            return Result.error("参数错误，套餐等级必须是1、2或3");
        }

        // 查询企业信息
        SysDepart sysDepart = sysDepartService.getById(depId);
        if (sysDepart == null) {
            return Result.error("操作失败，指定的企业不存在");
        }

        // 验证企业会员模式
        if (sysDepart.getMemberType() == null || !sysDepart.getMemberType().equals(1)) {
            return Result.error("操作失败，只有企业会员模式的企业才能修改套餐等级");
        }

        // 更新企业套餐等级
        boolean updateResult = sysDepartService
                .lambdaUpdate()
                .set(SysDepart::getPackagesVersionType, level)
                .eq(SysDepart::getId, depId)
                .update();

        if (updateResult) {
            return Result.ok("企业套餐等级修改成功！");
        } else {
            return Result.error("企业套餐等级修改失败");
        }
    }
}

