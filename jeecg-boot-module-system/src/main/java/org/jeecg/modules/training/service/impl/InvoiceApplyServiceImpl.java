package org.jeecg.modules.training.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.training.entity.InvoiceApply;
import org.jeecg.modules.training.entity.Orders;
import org.jeecg.modules.training.mapper.InvoiceApplyMapper;
import org.jeecg.modules.training.mapper.OrdersMapper;
import org.jeecg.modules.training.service.IInvoiceApplyService;
import org.jeecg.modules.training.vo.InvoiceApplyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class InvoiceApplyServiceImpl extends ServiceImpl<InvoiceApplyMapper, InvoiceApply> implements IInvoiceApplyService {

    @Autowired
    private InvoiceApplyMapper invoiceApplyMapper;

    @Autowired
    private OrdersMapper ordersMapper;

    /**
     * app用户提交申请开电子发票接口
     * @param invoiceApply InvoiceApply
     * @return Result<String>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> applyInvoice(InvoiceApply invoiceApply) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }
        LambdaQueryWrapper<Orders> orderQuery = new LambdaQueryWrapper<Orders>()
                .eq(Orders::getOrdersNum, invoiceApply.getOrderNum())
                .eq(Orders::getUserId, loginUser.getId());
        Orders applyOrder = ordersMapper.selectOne(orderQuery);
        if (applyOrder == null)
            return Result.error("订单信息有误，请检查");
        if (applyOrder.getInvoiceStatus() == 1)
            return Result.error("已提交申请,请勿重复提交");
        if (applyOrder.getInvoiceStatus() == 2)
            return Result.error("订单已开票，无需重复开票");
        // if status=3 失败重新提交
        if (applyOrder.getInvoiceStatus() == 3){
            invoiceApply.setStatus(1);
            LambdaUpdateWrapper<InvoiceApply> invoiceApplyUpdate = new LambdaUpdateWrapper<InvoiceApply>()
                    .eq(InvoiceApply::getOrderNum, invoiceApply.getOrderNum())
                    .set(InvoiceApply::getStatus, 1)
                    .set(InvoiceApply::getPayerName, invoiceApply.getPayerName())
                    .set(InvoiceApply::getPayerRegisterNo, invoiceApply.getPayerRegisterNo())
                    .set(InvoiceApply::getEmail, invoiceApply.getEmail());
            invoiceApplyMapper.update(invoiceApplyUpdate);
            LambdaUpdateWrapper<Orders> orderUpdate = new LambdaUpdateWrapper<Orders>().eq(Orders::getOrdersNum, invoiceApply.getOrderNum())
                    .set(Orders::getInvoiceStatus, 1);
            ordersMapper.update(orderUpdate);
            return Result.OK("已重新提交申请");
        }
        invoiceApply.setInvoiceAmount(applyOrder.getAmount());
        // 0 未开  1 开票中  2 已开票  3 开票失败
        invoiceApply.setStatus(1);
        invoiceApply.setUserId(loginUser.getId());
        invoiceApply.setName(applyOrder.getName());
        invoiceApply.setPayType(applyOrder.getPayType());
        invoiceApplyMapper.insert(invoiceApply);
        LambdaUpdateWrapper<Orders> orderUpdate = new LambdaUpdateWrapper<Orders>().eq(Orders::getOrdersNum, invoiceApply.getOrderNum()).set(Orders::getInvoiceStatus, 1);
        ordersMapper.update(orderUpdate);
        return Result.OK("已提交开发票申请");
    }

    /**
     * 管理员提交电子发票开票结果
     * @param invoiceApply Invoice
     * @return String
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> editInvoiceApply(InvoiceApply invoiceApply) {
        InvoiceApply exist = invoiceApplyMapper.selectOne(new LambdaQueryWrapper<InvoiceApply>().eq(InvoiceApply::getOrderNum, invoiceApply.getOrderNum()));
        if (exist == null){
            return Result.error("订单信息有误，请检查");
        }
        // 管理员发电子发票邮件后手动修改开票状态 暂定 todo
        LambdaUpdateWrapper<InvoiceApply> invoiceApplyUpdate = new LambdaUpdateWrapper<InvoiceApply>()
                .eq(InvoiceApply::getOrderNum, invoiceApply.getOrderNum())
                .set(InvoiceApply::getStatus, invoiceApply.getStatus());
        invoiceApplyMapper.update(invoiceApplyUpdate);
        LambdaUpdateWrapper<Orders> ordersUpdate = new LambdaUpdateWrapper<Orders>()
                .eq(Orders::getOrdersNum, invoiceApply.getOrderNum())
                .set(Orders::getInvoiceStatus, invoiceApply.getStatus());
        ordersMapper.update(ordersUpdate);
        return Result.OK("修改成功");
    }

    @Override
    public Page<InvoiceApplyVO> pageQuery(Page<InvoiceApply> page, QueryWrapper<InvoiceApply> queryWrapper) {
        return invoiceApplyMapper.pageQuery(page, queryWrapper);
    }


}
