package org.jeecg.modules.training.controller.admin;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.fields.FieldFilter;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.training.service.IPackagesLogService;
import org.jeecg.modules.training.vo.PackagesLogVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/adm/packagesLog")
public class PackagesLogController {

    @Autowired
    private IPackagesLogService packagesLogService;

    // 分页查询
    @FieldFilter({"actionType", "createTime", "id", "newExpireTime", "newSurplusNum",
            "oldExpireTime", "oldSurplusNum", "operatorId", "operatorName", "packageId",
            "packageName"})
    @GetMapping(value = "/pageList")
    public Result<?> queryPageList(PackagesLogVO packagesLogVO,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<PackagesLogVO> queryWrapper = QueryGenerator.initQueryWrapper(packagesLogVO, req.getParameterMap());

        Page<PackagesLogVO> page = new Page<>(pageNo, pageSize);
        IPage<PackagesLogVO> pageList = packagesLogService.listPages(page, queryWrapper);

        return Result.OK(pageList);
    }
}

