package org.jeecg.modules.training.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.modules.training.entity.ChecklistItem;

import java.util.ArrayList;
import java.util.List;

/**
 * 展示类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年10月27日 22:59
 */
@Data
@ApiModel(value = "ChecklistItemVO", description = "ChecklistItemVO")
public class ChecklistItemVO extends ChecklistItem {

    /**
     * 选项
     */
    @ApiModelProperty(value = "选项")
    transient List<CheckItemOptionVO> options = new ArrayList<>();
}
