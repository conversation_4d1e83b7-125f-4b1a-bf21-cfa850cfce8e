package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 试卷库题目
 * @Author: hzk
 * @Date: 2022-06-15
 * @Version: V1.0
 */
@Data
@TableName("paper_question")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "paper_question对象", description = "试卷库题目")
public class PaperQuestion {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 试卷库id
     */
    @Excel(name = "试卷库id", width = 15)
    @ApiModelProperty(value = "试卷库id")
    private Long paperId;
    /**
     * 题目库id
     */
    @Excel(name = "题目库id", width = 15)
    @ApiModelProperty(value = "题目库id")
    private Long questionId;
    /**
     * 卷套名称属性
     */
    @Excel(name = "卷套名称", width = 15)
    @ApiModelProperty(value = "卷套名称")
    private String groupName;

    /**
     * 配合主表中的是否题号使用
     */
    @Excel(name = "配合主表中的是否题号使用", width = 15)
    @ApiModelProperty(value = "配合主表中的是否题号使用")
    private Integer sort;
    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @Excel(name = "更新人", width = 15)
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @Excel(name = "删除状态（0，正常，1已删除）", width = 15)
    @ApiModelProperty(value = "删除状态（0，正常，1已删除）")
    @TableLogic
    private Integer delFlag;
}
