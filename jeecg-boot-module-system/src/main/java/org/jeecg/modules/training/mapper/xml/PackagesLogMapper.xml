<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.PackagesLogMapper">
    <!-- 分页列表查询-->
    <select id="listPages" resultType="org.jeecg.modules.training.vo.PackagesLogVO">
        select *
        from (select pl.*,
                     s.name      as `package_name`,
                     su.realname as `operator_name`
              from packages_log pl
                       left join packages s on pl.package_id = s.id
                       left join sys_user su on pl.operator_id = su.id) as packages_log
            ${ew.customsqlsegment}
    </select>
</mapper>
