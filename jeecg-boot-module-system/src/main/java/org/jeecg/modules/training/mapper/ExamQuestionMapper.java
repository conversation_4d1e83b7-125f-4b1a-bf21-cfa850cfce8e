package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.ExamQuestion;
import org.jeecg.modules.training.vo.ExamQuestionVO;
import org.jeecg.modules.training.vo.ExamReportFlattenedVO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 考卷题目
 * @Author: jeecg-boot
 * @Date: 2022-05-04
 * @Version: V1.0
 */
@Mapper
public interface ExamQuestionMapper extends BaseMapper<ExamQuestion> {

    /**
     * 清空用户答案
     *
     * @param examId
     */
    void clearAnswer(Long examId);

    /**
     * 查询所有数据
     *
     * @param params
     * @return
     */
    List<ExamQuestionVO> getAll(Map<String, Object> params);

    List<HashMap<String, Integer>> getFactorRate();

    // 获取考卷题目中所有主观题的记录id
    List<Long> getSubjectiveIds(Long examId, String depRoute);

    /**
     * 查询指定考试的扁平化报告数据
     *
     * <p>根据考试ID查询考试报告所需的扁平化数据，返回结果中每条记录代表一个测试题目，包括：
     * <ul>
     *     <li>考试基本信息（考试ID、标题、总分、及格分等）</li>
     *     <li>考生信息（姓名、身份证号等）</li>
     *     <li>题目得分信息（分数、所属因子等）</li>
     * </ul></p>
     *
     * <p>注意：一个因子通常包含多个测试题目，因此结果集中可能包含多条具有相同因子名称的记录。
     * 在进行数据处理时，需要按因子名称进行分组计算。</p>
     *
     * @param examId 考试ID
     * @return 扁平化的考试报告数据列表，每条记录代表一个测试题目的得分及其所属因子信息
     */
    List<ExamReportFlattenedVO> selectFlattenedReportData(@Param("examId") Long examId);
}
