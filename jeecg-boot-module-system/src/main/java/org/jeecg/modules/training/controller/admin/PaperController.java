package org.jeecg.modules.training.controller.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.fields.FieldFilter;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.NumberUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.training.async.paper.PushPaperService;
import org.jeecg.modules.training.async.phase.PushPhaseService;
import org.jeecg.modules.training.async.share.PushShareService;
import org.jeecg.modules.training.entity.MiniProgram;
import org.jeecg.modules.training.entity.Paper;
import org.jeecg.modules.training.entity.PhaseItem;
import org.jeecg.modules.training.service.*;
import org.jeecg.modules.training.vo.PaperVO;
import org.jeecg.modules.training.vo.QrCodeFile;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 试卷库
 * @Author: hzk
 * @Date: 2022-06-15
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "试卷库")
@RestController
@RequestMapping("/adm/paper")
public class PaperController extends JeecgController<Paper, IPaperService> {

    @Value("${justauth.type.WECHAT_OPEN.client-id}")
    private String appid;

    @Value("${justauth.type.WECHAT_OPEN.client-secret}")
    private String secret;

    @Value("${jeecg.path.miniProgramQrCode}")
    private String path;

    @Autowired
    private IUserPaperService userPaperService;

    @Autowired
    private IPhaseItemService phaseItemService;

    @Autowired
    private PushPaperService pushPaperService;

    @Autowired
    private ISyncStateService syncStateService;

    @Resource
    private PushPhaseService userPhaseHandleService;


    @Autowired
    private IMiniProgramService miniProgramService;

    @Autowired
    private ISysDepartService sysDepartService;

    @Autowired
    private IIndustryService industryService;

    @Autowired
    private PushShareService pushShareService;

    @Autowired
    private ICategoryService categoryService;

    @Autowired
    private IRuleService ruleService;

    @Autowired
    private IPaperQuestionService paperQuestionService;

    /**
     * 分页列表查询
     *
     * @param paper
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "试卷库-分页列表查询")
    @ApiOperation(value = "试卷库-分页列表查询", notes = "试卷库-分页列表查询")
    @GetMapping(value = "/list")
    @FieldFilter({"timeFactor", "memo", "qrFile", "status", "allowCheckAnswer", "autoExpire", "autoPush", "categoryId", "categoryName", "chargeStatus", "createBy", "createTime", "delFlag", "depRoute",
            "disabled", "drawType", "expiredDuration", "id", "industryIds", "industryNames", "inputCount", "isQuesOrder", "isQuesSort", "judgeCount", "keepDuration",
            "limitTime", "multiCount", "name", "numExam", "numRetries", "postFactor", "qrCodeFiles", "qualifyScore", "radioCount", "randomOptions", "reportTemplate",
            "ruleId", "ruleName", "scoreType", "shareMode", "showAnswer", "startFlag", "thinkDuration", "type", "updateBy", "updateTime", "warnDuration"})
    public Result<?> queryPageList(PaperVO paper, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {

        QueryWrapper<PaperVO> queryWrapper = QueryGenerator.initQueryWrapper(paper, req.getParameterMap());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.and(q -> q.lambda().eq(Paper::getDepRoute, loginUser.getDepRoute()));

        Page<PaperVO> page = new Page<>(pageNo, pageSize);
        IPage<PaperVO> pageList = service.listPages(page, queryWrapper);

        for (PaperVO paperVO : page.getRecords()) {

            if (paperVO.getPostFactor() != null) {
                while (paperVO.getPostFactor().startsWith(","))
                    paperVO.setPostFactor(paperVO.getPostFactor().substring(1));
                while (paperVO.getPostFactor().endsWith(","))
                    paperVO.setPostFactor(paperVO.getPostFactor().substring(0, paperVO.getPostFactor().length() - 1));
            }

            if (paperVO.getTimeFactor() != null) {
                while (paperVO.getTimeFactor().startsWith(","))
                    paperVO.setTimeFactor(paperVO.getTimeFactor().substring(1));
                while (paperVO.getTimeFactor().endsWith(","))
                    paperVO.setTimeFactor(paperVO.getTimeFactor().substring(0, paperVO.getTimeFactor().length() - 1));
            }

            // 设置行业名称
            paperVO.setIndustryNames(industryService.getNamesByIndustryIds(paperVO.getIndustryIds()));
        }

        return Result.OK(pageList);
    }

    /**
     * 查询所有列表
     *
     * @param req
     * @return
     */
    @AutoLog(value = "试卷库-查询所有列表")
    @ApiOperation(value = "试卷库-查询所有列表", notes = "试卷库-查询所有列表")
    @GetMapping(value = "/allList")
    public Result<?> queryAll(@RequestParam(name = "type", required = false) String type, @RequestParam(name = "categoryId", required = false) Long categoryId, HttpServletRequest req) {
        QueryWrapper<PaperVO> query = new QueryWrapper<>();

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        query.and(q -> q.lambda().eq(Paper::getDepRoute, loginUser.getDepRoute()));


        if (oConvertUtils.isNotEmpty(type)) {
            query.eq("type", type);
        }
        if (NumberUtil.isNotEmpty(categoryId)) {
            query.eq("category_id", categoryId);
        }
        List<PaperVO> list = service.queryAll(query);
        for (PaperVO paperVO : list) {

            if (paperVO.getPostFactor() != null) {
                while (paperVO.getPostFactor().startsWith(","))
                    paperVO.setPostFactor(paperVO.getPostFactor().substring(1));
                while (paperVO.getPostFactor().endsWith(","))
                    paperVO.setPostFactor(paperVO.getPostFactor().substring(0, paperVO.getPostFactor().length() - 1));
            }

            if (paperVO.getTimeFactor() != null) {
                while (paperVO.getTimeFactor().startsWith(","))
                    paperVO.setTimeFactor(paperVO.getTimeFactor().substring(1));
                while (paperVO.getTimeFactor().endsWith(","))
                    paperVO.setTimeFactor(paperVO.getTimeFactor().substring(0, paperVO.getTimeFactor().length() - 1));
            }
        }

        return Result.OK(list);
    }

    /**
     * 添加
     *
     * @param paper
     * @return
     */
    @AutoLog(value = "试卷库-添加")
    @ApiOperation(value = "试卷库-添加", notes = "试卷库-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody Paper paper) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        paper.setDepRoute(loginUser.getDepRoute());

        String industryIds = null;
        if (paper.getIndustryNames() != null && !paper.getIndustryNames().trim().isEmpty()) {
            industryIds = industryService.getIndustryIdsByNames(paper.getIndustryNames());
            paper.setIndustryIds(industryIds);
        }

        paper.setDisabled(Boolean.TRUE);
        paper.setDelFlag(CommonConstant.DEL_FLAG_0);
        fixFactors(paper);
        service.save(paper);

        Long shareId = paper.getId();
        //复制数据
        if ("admin".equals(loginUser.getUsername()) && StringUtils.isNotBlank(paper.getIndustryNames()) && "1".equals(paper.getShareMode())) {
            pushShareService.pushData("paper", shareId.toString());
        }
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param paper
     * @return
     */
    @AutoLog(value = "试卷库-编辑")
    @ApiOperation(value = "试卷库-编辑", notes = "试卷库-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody Paper paper) {
        String industryIds = null;
        if (paper.getIndustryNames() != null && !paper.getIndustryNames().trim().isEmpty()) {
            industryIds = industryService.getIndustryIdsByNames(paper.getIndustryNames());
            paper.setIndustryIds(industryIds);
        }

        //修改试卷信息，可以不要禁用
        paper.setDelFlag(CommonConstant.DEL_FLAG_0);
        fixFactors(paper);

        String error = null;
        try {
            if (paper.getDisabled() != null && !paper.getDisabled()) {
                service.checkPaper(paper);
            }
        } catch (Exception e) {
            paper.setDisabled(true);
            error = "操作失败!" + e.getMessage();
        }

        service.updateById(paper);
        syncStateService.resetSyncState(CommonConstant.PAPER, paper.getId().toString());

        if (error != null) return Result.error(error);

        //有变更，就重新计算推送
        pushPaperService.pushPaper(paper.getId());

        LambdaUpdateWrapper<PhaseItem> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(PhaseItem::getGroupFactor, paper.getGroupFactor()).set(PhaseItem::getDisabled, paper.getDisabled()).set(PhaseItem::getDataType, paper.getType()).set(PhaseItem::getPostFactor, paper.getPostFactor()).set(PhaseItem::getGroupFactor, paper.getGroupFactor()).eq(PhaseItem::getType, 1).eq(PhaseItem::getDataId, paper.getId().toString());

        if (phaseItemService.update(updateWrapper)) {
            userPhaseHandleService.pushAll(true);
        }

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //复制数据,sharemode修改成1的时候才需要修改
        if ("admin".equals(loginUser.getUsername()) && "1".equals(paper.getShareMode())) {

            pushShareService.pushData("paper", paper.getId().toString());

        }

        return Result.OK("编辑成功!");
    }

    private void fixFactors(Paper paper) {
        if (paper.getPostFactor() != null) {
            paper.setPostFactor("," + paper.getPostFactor().trim() + ",");
            if (paper.getPostFactor().equals(",,")) {
                paper.setPostFactor("");
            }
        }

        if (paper.getTimeFactor() != null) {
            paper.setTimeFactor("," + paper.getTimeFactor().trim() + ",");
            if (paper.getTimeFactor().equals(",,")) {
                paper.setTimeFactor("");
            }
        }

        if (StringUtils.isNotEmpty(paper.getGroupFactor())) {
            paper.setGroupFactor(paper.getGroupFactor().trim().replaceAll(" ", "").replace('，', ','));
        }
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "试卷库-通过id删除")
    @ApiOperation(value = "试卷库-通过id删除", notes = "试卷库-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        Result<Paper> paperResult;
        if (service.removeById(id)) {

            LambdaQueryWrapper<PhaseItem> updateWrapper = new LambdaQueryWrapper<>();
            updateWrapper.eq(PhaseItem::getType, 1).eq(PhaseItem::getDataId, id);

            if (phaseItemService.remove(updateWrapper)) {
                userPhaseHandleService.pushAll(true);
                syncStateService.resetSyncState(CommonConstant.PAPER, id);
            }

            paperResult = Result.ok();
        } else paperResult = Result.error("删除失败");
        return paperResult;
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "试卷库-批量删除")
    @ApiOperation(value = "试卷库-批量删除", notes = "试卷库-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {

        if (ids == null || "".equals(ids.trim())) {
            return Result.error("参数不识别！");
        }

        LambdaQueryWrapper<PhaseItem> updateWrapper = new LambdaQueryWrapper<>();
        List<String> collect = Arrays.stream(ids.split(",")).distinct().collect(Collectors.toList());
        updateWrapper.eq(PhaseItem::getType, 1).in(PhaseItem::getDataId, collect);

        for (String id : collect) {
            syncStateService.resetSyncState(CommonConstant.PAPER, id);
        }

        if (phaseItemService.remove(updateWrapper)) {
            userPhaseHandleService.pushAll(true);
        }
        return service.deleteBatch(collect);
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "试卷库-通过id查询")
    @ApiOperation(value = "试卷库-通过id查询", notes = "试卷库-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        Paper paper = service.getById(id);
        return Result.OK(paper);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param paper
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Paper paper) {
        return super.exportXls(request, paper, Paper.class, "试卷库");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Paper.class);
    }

    /**
     * 启用&禁用试卷
     *
     * @param jsonObject
     * @return
     */
    //@RequiresRoles({"admin"})
    @RequestMapping(value = "/frozenBatch", method = RequestMethod.PUT)
    public Result<Paper> frozenBatch(@RequestBody JSONObject jsonObject) {
        Result<Paper> result = new Result<Paper>();
        try {
            String ids = jsonObject.getString("ids");
            String status = jsonObject.getString("disabled");
            Boolean disabled = Boolean.parseBoolean(status);
            String[] arr = ids.split(",");
            boolean ok = true;
            List<Long> idList = new ArrayList<>();
            for (String id : arr) {
                if (oConvertUtils.isNotEmpty(id)) {
                    if (!disabled) {
                        Paper paper = service.getById(Long.parseLong(id));
                        try {
                            service.checkPaper(paper);
                            this.service.update(new Paper().setDisabled(false), new UpdateWrapper<Paper>().lambda().eq(Paper::getId, Long.parseLong(id)));
                            idList.add(Long.parseLong(id));
                            syncStateService.resetSyncState(CommonConstant.PAPER, paper.getId().toString());
                        } catch (Exception e) {
                            ok = false;
                        }
                    } else {
                        this.service.update(new Paper().setDisabled(true), new UpdateWrapper<Paper>().lambda().eq(Paper::getId, Long.parseLong(id)));
                        syncStateService.resetSyncState(CommonConstant.PAPER, id);
                    }
                }
            }

            for (Long id : idList) {
                pushPaperService.pushPaper(id);
            }

            if (!ok) {
                result.error500("操作失败，部分试卷的题目配置不满足抽题要求，无法启用。");
                return result;
            }
            result.success("操作成功!");

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败" + e.getMessage());
        }
        return result;

    }

    /**
     * 复制试卷库
     *
     * @param jsonObject
     * @return
     */
    //@RequiresRoles({"admin"})
    @RequestMapping(value = "/copyPaper", method = RequestMethod.POST)
    public Result<Paper> copyPaper(@RequestBody JSONObject jsonObject) {
        Result<Paper> result = new Result<Paper>();
        try {
            Long paperId = Long.parseLong(jsonObject.getString("paperId"));
            String paperName = jsonObject.getString("paperName");
            Boolean clearGroupName = jsonObject.getBoolean("clearGroupName");
            Boolean clearSort = jsonObject.getBoolean("clearSort");
            service.sharePaper(paperId, paperName, clearGroupName, clearSort);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败" + e.getMessage());
        }
        result.success("操作成功!");
        return result;

    }


    @ApiOperation(value = "微信小程序生成二维码", notes = "微信小程序生成二维码")
    @RequestMapping(value = "/getQRCode", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> getQRCode(@RequestBody PaperVO paperVo) {

        File file = new File(path);
        if (!file.exists()) {
            file.mkdirs();
        }

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        MiniProgram miniProgram = new MiniProgram();
        miniProgram.setPaperId(paperVo.getId());
        miniProgram.setUserId(loginUser.getId());
        miniProgram.setBeforeAnswering(paperVo.getBeforeAnswering());
        miniProgram.setUserDepRoute(loginUser.getDepRoute());
        miniProgram.setCategoryId(paperVo.getCategoryId());
        miniProgram.setOpenCamera(paperVo.getOpenCamera());
        miniProgramService.save(miniProgram);

        OutputStream os = null;
        try {

            String wxCodeURL = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + getAccessToken();
            URL url = new URL(wxCodeURL);

            HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
            httpURLConnection.setRequestMethod("POST");// 提交模式
            // 发送POST请求必须设置如下两行
            httpURLConnection.setDoOutput(true);
            httpURLConnection.setDoInput(true);

            // 获取URLConnection对象对应的输出流
            PrintWriter printWriter = new PrintWriter(httpURLConnection.getOutputStream());

            // 发送请求参数
            JSONObject paramJson = new JSONObject();
            paramJson.put("scene", "miniProgramId=" + miniProgram.getId());
            paramJson.put("page", "components/advertisement/advertisement");
            printWriter.write(String.valueOf(paramJson));

            // flush输出流的缓冲
            printWriter.flush();
            //开始获取数据
            BufferedInputStream bis = new BufferedInputStream(httpURLConnection.getInputStream());
            long time = System.currentTimeMillis();

            // 构造小程序码的文件名
            String fileName = paperVo.getName() + "-" + time + ".png";
            // 替换文件名中的干扰字符，如 "/"
            fileName = fileName.replace('/', '_');

            os = Files.newOutputStream(new File(path, fileName).toPath());
            int len;
            byte[] arr = new byte[1024];
            while ((len = bis.read(arr)) != -1) {
                os.write(arr, 0, len);
                os.flush();
            }

            Paper paper = new Paper();
            QrCodeFile qrCodeFile = new QrCodeFile();
            JSONArray qrCodeFileArray = service.getById(paperVo.getId()).getQrCodeFiles();
            if (qrCodeFileArray == null) {
                qrCodeFileArray = new JSONArray();
            }

            qrCodeFile.setId((long) qrCodeFileArray.size());
            qrCodeFile.setQrFile("miniProgramQrCode" + File.separator + fileName);
            qrCodeFile.setMemo(paperVo.getMemo());
            qrCodeFile.setStatus(true);

            JSONObject qrCodeJson = (JSONObject) JSONObject.toJSON(qrCodeFile);
            qrCodeFileArray.add(qrCodeJson);
            paper.setQrCodeFiles(qrCodeFileArray);
            paper.setId(paperVo.getId());
            service.updateById(paper);
            syncStateService.resetSyncState(CommonConstant.PAPER, paper.getId().toString());

            miniProgramService.update(new LambdaUpdateWrapper<MiniProgram>().set(MiniProgram::getStatus, qrCodeFile.getStatus()).set(MiniProgram::getQrCodeId, qrCodeFile.getId()).eq(MiniProgram::getId, miniProgram.getId()));

            return Result.ok("成功生成小程序码");

        } catch (Exception e) {
            log.error("生成二维码异常", e);
            return Result.error("小程序码生成失败");
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    public String getAccessToken() throws Exception {
        String requestUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appid + "&secret=" + secret;
        URL url = new URL(requestUrl);
        // 打开和URL之间的连接
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        // 设置通用的请求属性
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Connection", "Keep-Alive");
        connection.setUseCaches(false);
        connection.setDoOutput(true);
        connection.setDoInput(true);
        // 得到请求的输出流对象
        DataOutputStream out = new DataOutputStream(connection.getOutputStream());
        out.writeBytes("");
        out.flush();
        out.close();
        // 建立实际的连接
        connection.connect();
        // 定义 BufferedReader输入流来读取URL的响应
        BufferedReader in = null;
        if (requestUrl.contains("nlp"))
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "GBK"));
        else in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
        String result = "";
        String getLine;
        while ((getLine = in.readLine()) != null) {
            result += getLine;
        }
        in.close();
        JSONObject jsonObject = JSON.parseObject(result);
        String accesstoken = jsonObject.getString("access_token");
        log.info("获取的accesstoken为：" + accesstoken);
        return accesstoken;
    }

    /**
     * 查询当前登录用户可获取的分享的试卷列表 sharemodel = 2
     */
    @GetMapping("/getShareList")
    public Result<?> getSharePaperList(PaperVO paperVO, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<PaperVO> queryWrapper = QueryGenerator.initQueryWrapper(paperVO, req.getParameterMap());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        queryWrapper.in("share_mode", 1, 2);

        // 获取该部门所属行业的id集合
        String industryIds = sysDepartService.getDepartByOrgCode(loginUser.getOrgCode()).getIndustryIds();
        if (!industryIds.isEmpty()) {
            // 格式化行业id字符串
            industryIds = industryService.formatIndustryIdsExp(industryIds);
            queryWrapper.apply("industry_ids regexp {0}", industryIds);
        }

        Page<PaperVO> page = new Page<>(pageNo, pageSize);
        IPage<PaperVO> pageList = service.listPages(page, queryWrapper);

        return Result.ok(pageList);
    }


    /**
     * 设置用户选择的公共资料
     */
    @GetMapping("setSharePaper")
    public Result<?> setSharePaper(@RequestParam List<Long> ids) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 共享数据
        ids.forEach(item -> {
            service.sharePaper(item, loginUser.getDepRoute(), true);
        });

        return Result.ok("获取成功");
    }

    // 修改二维码的状态
    @AutoLog(value = "试卷库-修改二维码的状态")
    @ApiOperation(value = "试卷库-修改二维码的状态", notes = "试卷库-修改二维码的状态")
    @PutMapping(value = "/updateQRCodeStatus")
    public Result<?> updateQrCodeStatus(@RequestBody JSONObject jsonObject) {
        // 处理参数
        QrCodeFile qrCodeFile = JSONObject.toJavaObject(jsonObject, QrCodeFile.class);
        Long paperId = jsonObject.getLong("paperId");
        // 修改二维码的状态
        service.updateQrCodeStatus(qrCodeFile, paperId);
        syncStateService.resetSyncState(CommonConstant.PAPER, paperId.toString());

        return Result.OK("修改成功");
    }

    // 删除二维码接口
    @AutoLog(value = "试卷库-删除二维码")
    @ApiOperation(value = "试卷库-删除二维码", notes = "试卷库-删除二维码")
    @DeleteMapping(value = "/deleteQRCode")
    public Result<?> deleteQrCode(@RequestParam(name = "id") Long id, @RequestParam(name = "paperId") Long paperId) {
        // 删除二维码
        service.deleteQrCode(paperId, id);
        return Result.OK("删除成功");
    }


}
