package org.jeecg.modules.training.async.phase;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.service.IPhaseService;
import org.jeecg.modules.training.service.IUserPhaseItemService;
import org.jeecg.modules.training.service.IUserPhaseService;
import org.jeecg.modules.training.service.TraineeService;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-12-16 01:22
 **/
@Slf4j
//@Component
//@Scope("prototype")
public abstract class PushPhaseHandler {
    protected static final long ONE_DAY_MS = 24 * 3600 * 1000L;

    protected static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    @Lazy
    protected ISysBaseAPI sysBaseAPI;

    @Autowired
    protected IUserPhaseService userPhaseService;

    @Autowired
    protected IUserPhaseItemService userPhaseItemService;

    @Autowired
    protected IPhaseService phaseService;

    @Autowired(required = false)
    @Lazy
    protected TraineeService traineeService;

    @Resource
    protected ISyncStateService syncStateService;

    //要处理的用户
    protected List<Trainee> traineeList;

    //要处理的数据
    protected Long dataId;
    public abstract void run();

    public void setTraineeList(List<Trainee> traineeList) {
        this.traineeList = traineeList;
    }
	
    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    protected float printStepLog(String oper, int total, float step, int i) {
        float percent = (i + 1) * 100f / total;
        if (percent >= step) {
            log.info(oper + " phase {}/{} : {}%", i + 1, total, (int) percent);
            step = ((int) (percent / 5) + 1) * 5;
            if (step > 100)
                step = 100;
        }
        return step;
    }

}
