package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 船舶表
 * <p>
 *
 * <AUTHOR>
 * @Since 2019-01-22
 */
@Data
@TableName("team")
@NoArgsConstructor
public class Team implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 父机构ID
     */
    private String parentId;
    /**
     * 机构/部门名称
     */
    @Excel(name = "机构/部门名称", width = 15)
    private String teamName;
    /**
     * 英文名
     */
    @Excel(name = "英文名", width = 15)
    private String teamNameEn;
    /**
     * 缩写
     */
    private String teamNameAbbr;
    /**
     * 排序
     */
    @Excel(name = "排序", width = 15)
    private Integer teamOrder;
    /**
     * 描述
     */
    @Excel(name = "描述", width = 15)
    private String description;
    /**
     * 机构类别 1=公司，2=组织机构，3=岗位
     */
    @Excel(name = "机构类别", width = 15, dicCode = "org_category")
    private String orgCategory;
    /**
     * 机构类型
     */
    private String orgType;
    /**机构编码*/
//	@Excel(name="机构编码",width=15)
//	private String orgCode;
    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 15)
    private String mobile;
    /**
     * 传真
     */
    @Excel(name = "传真", width = 15)
    private String fax;
    /**
     * 地址
     */
    @Excel(name = "地址", width = 15)
    private String address;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    private String memo;
    /**
     * 状态（1启用，0不启用）
     */
    @Dict(dicCode = "depart_status")
    private String status;
    /**
     * 删除状态（0，正常，1已删除）
     */
    @Dict(dicCode = "del_flag")
    @TableLogic
    private Integer delFlag;
    /**
     * 对接企业微信的ID
     */
    private String qywxIdentifier;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 树状多层级结构的路径
     * 做索引
     * ID ?
     * orgCode ?
     * name ?
     * xxx_xxx_xxx
     * like xxx_%
     */
    private String route;

    /**
     * 部门id
     */
    private String departId;

    //update-begin---author:wangshuai ---date:20200308  for：[JTC-119]在部门管理菜单下设置部门负责人，新增字段负责人ids和旧的负责人ids
    /**
     * 部门负责人的ids
     */
    @TableField(exist = false)
    private String directorUserIds;
    /**
     * 旧的部门负责人的ids(用于比较删除和新增)
     */
    @TableField(exist = false)
    private String oldDirectorUserIds;
    //update-end---author:wangshuai ---date:20200308  for：[JTC-119]新增字段负责人ids和旧的负责人ids

    // 只赋值船名的构造方法
    public Team(String teamName) {
        this.teamName = teamName;
    }

    /**
     * 受试人数量
     */
    private Integer traineeCount;

    /**
     * 重写equals方法
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Team team = (Team) o;
        return Objects.equals(id, team.id) &&
                Objects.equals(parentId, team.parentId) &&
                Objects.equals(teamName, team.teamName) &&
                Objects.equals(teamNameEn, team.teamNameEn) &&
                Objects.equals(teamNameAbbr, team.teamNameAbbr) &&
                Objects.equals(teamOrder, team.teamOrder) &&
                Objects.equals(description, team.description) &&
                Objects.equals(orgCategory, team.orgCategory) &&
                Objects.equals(orgType, team.orgType) &&
                Objects.equals(mobile, team.mobile) &&
                Objects.equals(fax, team.fax) &&
                Objects.equals(address, team.address) &&
                Objects.equals(memo, team.memo) &&
                Objects.equals(status, team.status) &&
                Objects.equals(delFlag, team.delFlag) &&
                Objects.equals(createBy, team.createBy) &&
                Objects.equals(createTime, team.createTime) &&
                Objects.equals(updateBy, team.updateBy) &&
                Objects.equals(updateTime, team.updateTime);
    }

    /**
     * 重写hashCode方法
     */
    @Override
    public int hashCode() {

        return Objects.hash(super.hashCode(), id, parentId, teamName,
                teamNameEn, teamNameAbbr, teamOrder, description, orgCategory,
                mobile, fax, address, memo, status,
                delFlag, createBy, createTime, updateBy, updateTime);
    }
}
