<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.ManagersMapper">

  <select id="getPageList" resultType="org.jeecg.modules.training.vo.ManagersVO">
    select *
    from (select m.*, su.realname, t.team_name
          from managers m,
               sys_user su,
               team t
          where m.sys_user_id = su.id
            and m.team_id = t.id) manager
      ${ew.customSqlSegment}
  </select>
</mapper>
