package org.jeecg.modules.training.controller.app;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.common.exception.JeecgBoot401Exception;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.training.entity.Paper;
import org.jeecg.modules.training.service.IPaperService;
import org.jeecg.modules.training.service.IPackagePriceCalculationService;
import org.jeecg.modules.training.service.IUserPaperService;
import org.jeecg.modules.training.vo.PaperVO;
import org.jeecg.modules.system.service.ISysDepartService;

import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: 试卷库
 * @Author: hzk
 * @Date: 2022-06-15
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "试卷库")
@RestController
@RequestMapping("/app/paper")
public class AppPaperController extends JeecgController<Paper, IPaperService> {

    @Autowired
    private IUserPaperService userPaperService;

    @Autowired
    private IPackagePriceCalculationService packagePriceCalculationService;

    @Autowired
    private ISysDepartService sysDepartService;

    @Value("${training.memberEnable:false}")
    private boolean memberEnable;

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "试卷库-通过id查询")
    @ApiOperation(value = "试卷库-通过id查询", notes = "试卷库-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        Paper paper = service.getById(id);
        return Result.OK(paper);
    }

    /**
     * 查询当前用户可用试卷
     *
     * @param categoryId
     * @return
     */
    @AutoLog(value = "试卷库-查询当前用户可用试卷(App)")
    @ApiOperation(value = "试卷库-当前用户可用试卷(App)", notes = "试卷库-当前用户可用试卷(App)")
    @GetMapping(value = "/findPapers")
    public Result<?> findPages(@RequestParam(name = "categoryId") Long categoryId) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (oConvertUtils.isEmpty(loginUser)) {
            // 直接抛出401异常，会被全局异常处理器处理
            throw new JeecgBoot401Exception(CommonConstant.TOKEN_IS_INVALID_MSG);
        }

        List<PaperVO> pageList;

        // 如果启用了会员机制，使用带有权限过滤和排序机制的查询方法
        if (memberEnable) {
            // 获取部门ID
            String departId = StrUtil.subAfter(loginUser.getDepRoute(), "/", true);

            // 获取部门信息和会员类型
            Integer memberType = null;
            Short memberLevel = 0;

            try {
                // 获取部门信息
                SysDepart sysDepart = sysDepartService.getById(departId);
                if (sysDepart != null && sysDepart.getDelFlag() == 0) {
                    memberType = sysDepart.getMemberType();
                    // 获取用户当前的套餐等级
                    memberLevel = packagePriceCalculationService.getMemberPackageLevel(loginUser.getId(), departId);

                    pageList = userPaperService.findPapers(loginUser.getId(), categoryId, departId, memberType, memberLevel);
                }
            } catch (Exception e) {
                // 如果获取会员信息失败，记录日志但不影响查询
                throw new IllegalArgumentException("获取用户会员信息失败。", e);
            }
        }

        // 如果有有效的会员信息，使用增强版本查询
        if (memberType != null) {
        } else {
            // 否则使用基础版本查询
            pageList = userPaperService.findPapers(loginUser.getId(), categoryId);
        }

        return Result.OK(pageList);
    }

    /**
     * 获取考试购买信息
     * 包括升级套餐价格和单独购买考试价格两部分信息
     *
     * @param id 考试ID
     * @return 考试购买信息
     */
    @ApiOperation(value = "获取考试购买信息")
    @GetMapping("/getPaymentInfo")
    public Result<?> getPaymentInfo(@RequestParam(name = "id") Long id) {
        try {
            // 获取当前登录用户信息
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (oConvertUtils.isEmpty(loginUser)) {
                throw new JeecgBoot401Exception(CommonConstant.TOKEN_IS_INVALID_MSG);
            }

            // 获取船员ID和部门ID
            String traineeId = loginUser.getId();
            String departId = StrUtil.subAfter(loginUser.getDepRoute(), "/", true);

            // 调用通用方法获取考试购买信息
            return Result.OK(packagePriceCalculationService.getIteamPaymentInfo(traineeId, departId, (short) 2, id));
        } catch (JeecgBoot401Exception e) {
            throw e;
        } catch (Exception e) {
            log.error("获取考试购买信息失败", e);
            return Result.error("获取考试购买信息失败：" + e.getMessage());
        }
    }
}
