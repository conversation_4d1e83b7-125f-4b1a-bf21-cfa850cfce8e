package org.jeecg.modules.training.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.fields.FieldFilter;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.training.async.share.PushShareService;
import org.jeecg.modules.training.entity.Knowledge;
import org.jeecg.modules.training.service.ICategoryService;
import org.jeecg.modules.training.service.IIndustryService;
import org.jeecg.modules.training.service.IKnowledgeService;
import org.jeecg.modules.training.vo.KnowledgeVo;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 知识点
 * @Author: jeecg-boot
 * @Date: 2022-05-04
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "知识点")
@RestController
@RequestMapping("/adm/knowledge")
public class KnowledgeController extends JeecgController<Knowledge, IKnowledgeService> {
    @Autowired
    private IKnowledgeService knowledgeService;

    @Resource
    private ISyncStateService syncStateService;

    @Autowired
    private ISysDepartService sysDepartService;

    @Autowired
    private IIndustryService industryService;

    @Autowired
    private PushShareService pushShareService;

    @Autowired
    private ICategoryService categoryService;

    /**
     * 分页列表查询
     * fixme: J记录日志有点问题
     *
     * @param vo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "知识点-分页列表查询")
    @ApiOperation(value = "知识点-分页列表查询", notes = "知识点-分页列表查询")
    @GetMapping(value = "/list")
    @FieldFilter({"id", "categoryName", "title", "industryNames", "shareMode", "content", "depRoute", "categoryId"})
    public Result<?> queryPageList(KnowledgeVo vo, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        vo.setDelFlag(CommonConstant.DEL_FLAG_0);
        QueryWrapper<KnowledgeVo> queryWrapper = QueryGenerator.initQueryWrapper(vo, req.getParameterMap());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        queryWrapper.and(q -> q.lambda().eq(Knowledge::getDepRoute, loginUser.getDepRoute()));

        Page<KnowledgeVo> page = new Page<>(pageNo, pageSize);
        IPage<KnowledgeVo> pageList = knowledgeService.getPageList(page, queryWrapper);
        pageList.getRecords().forEach(item -> {
            item.setIndustryNames(industryService.getNamesByIndustryIds(item.getIndustryIds()));
        });

        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param knowledge
     * @return
     */
    @AutoLog(value = "知识点-添加")
    @ApiOperation(value = "知识点-添加", notes = "知识点-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody Knowledge knowledge) {
        knowledge.setDelFlag(CommonConstant.DEL_FLAG_0);

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        knowledge.setDepRoute(loginUser.getDepRoute());

        String industryIds = null;
        if (knowledge.getIndustryNames() != null && !knowledge.getIndustryNames().trim().isEmpty()) {
            industryIds = industryService.getIndustryIdsByNames(knowledge.getIndustryNames());
            knowledge.setIndustryIds(industryIds);
        }

        knowledgeService.save(knowledge);

        //复制数据
        if ("admin".equals(loginUser.getUsername()) && StringUtils.isNotBlank(knowledge.getIndustryNames()) && "1".equals(knowledge.getShareMode())) {

            pushShareService.pushData("knowledge", knowledge.getId().toString());

        }
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param knowledge
     * @return
     */
    @AutoLog(value = "知识点-编辑")
    @ApiOperation(value = "知识点-编辑", notes = "知识点-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody Knowledge knowledge) {
        Long shareId = knowledge.getId();

        String industryIds = null;
        knowledge.setDelFlag(CommonConstant.DEL_FLAG_0);
        if (knowledge.getIndustryNames() != null && !knowledge.getIndustryNames().trim().isEmpty()) {
            industryIds = industryService.getIndustryIdsByNames(knowledge.getIndustryNames());
            knowledge.setIndustryIds(industryIds);
        }

        knowledgeService.updateById(knowledge);

        syncStateService.resetSyncState(CommonConstant.KNOWLEDGE, knowledge.getId().toString());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //复制数据,sharemode修改成1的时候才需要修改
        if ("admin".equals(loginUser.getUsername()) && "1".equals(knowledge.getShareMode())) {

            pushShareService.pushData("knowledge", knowledge.getId().toString());

        }

        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "知识点-通过id删除")
    @ApiOperation(value = "知识点-通过id删除", notes = "知识点-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        if (knowledgeService.removeById(id)) {
            syncStateService.resetSyncState(CommonConstant.KNOWLEDGE, id);
            return Result.ok();
        }

        return Result.error("删除失败");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "知识点-批量删除")
    @ApiOperation(value = "知识点-批量删除", notes = "知识点-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        if (ids == null || ids.trim().isEmpty()) {
            return Result.error("参数不识别！");
        }

        List<String> collect = Arrays.stream(ids.split(",")).distinct().collect(Collectors.toList());

        if (knowledgeService.removeBatchByIds(collect)) {
            collect.forEach(id -> syncStateService.resetSyncState(CommonConstant.KNOWLEDGE, id));
            return Result.ok();
        }

        return Result.error("删除失败");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "知识点-通过id查询")
    @ApiOperation(value = "知识点-通过id查询", notes = "知识点-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        Knowledge knowledge = knowledgeService.getById(id);
        return Result.OK(knowledge);
    }


    /**
     * 获取所有知识点下拉用
     *
     * @return
     */
    @RequestMapping(value = "/queryall", method = RequestMethod.GET)
    @FieldFilter({"categoryId", "content", "createBy", "createTime", "delFlag", "depRoute",
            "id", "industryIds", "shareMode", "title", "updateBy", "updateTime"})
    public Result<Map<String, Object>> queryall(HttpServletRequest req) {
        Result<Map<String, Object>> result = new Result<>();
        Map<String, Object> map = new HashMap(5);
        LambdaQueryWrapper<Knowledge> wrapper = new LambdaQueryWrapper<>();

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        wrapper.and(q -> q.eq(Knowledge::getDepRoute, loginUser.getDepRoute()));


        wrapper.eq(Knowledge::getDelFlag, CommonConstant.DEL_FLAG_0);
        if (req.getParameter("categoryId") != null)
            wrapper.eq(Knowledge::getCategoryId, req.getParameter("categoryId"));

        List<Knowledge> list = knowledgeService.list(wrapper);
        map.put("list", list);
        result.setResult(map);
        result.setSuccess(true);
        return result;
    }

    /**
     * 导出excel
     *
     * @param request
     * @param knowledge
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Knowledge knowledge) {
        return super.exportXls(request, knowledge, Knowledge.class, "知识点");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Knowledge.class);
    }

    /**
     * 查询当前登录用户可获取的分享的试卷列表 sharemodel = 2
     */
    @GetMapping("/getShareList")
    @FieldFilter({"categoryId", "categoryName", "content", "createBy", "createTime", "delFlag", "depRoute", "id", "industryIds", "shareMode", "title", "updateBy", "updateTime"})
    public Result<?> getSharePaperList(KnowledgeVo knowledgeVo, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<KnowledgeVo> queryWrapper = QueryGenerator.initQueryWrapper(knowledgeVo, req.getParameterMap());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        queryWrapper.in("share_mode", 1, 2);

        // 获取该部门所属行业的id集合
        String industryIds = sysDepartService.getDepartByOrgCode(loginUser.getOrgCode()).getIndustryIds();
        if (!industryIds.isEmpty()) {
            // 格式化行业id字符串
            industryIds = industryService.formatIndustryIdsExp(industryIds);
            queryWrapper.apply("industry_ids regexp {0}", industryIds);
        }

        Page<KnowledgeVo> page = new Page<>(pageNo, pageSize);
        IPage<KnowledgeVo> pageList = knowledgeService.getPageList(page, queryWrapper);

        return Result.ok(pageList);
    }


    /**
     * 设置用户选择的公共资料
     */
    @GetMapping("setShareKnowledge")
    public Result<?> setShareKnowledge(@RequestParam List<Long> ids) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 共享数据
        ids.forEach(item -> {
            knowledgeService.shareKnowledge(item, loginUser.getDepRoute(), true);
        });

        return Result.ok("获取成功");
    }

}
