package org.jeecg.modules.training.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.modules.training.entity.Exam;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 考卷视图模型
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年05月07日 18:54
 */
@Data
public class ExamListVO extends Exam {
    /**
     * 员工工号
     */
    @Excel(name = "编号", width = 15)
    String workNo;

    /**
     * 用户名称
     */
    @Excel(name = "用户姓名", width = 15)
    String realname;

    String identityCard;

    String phone;

    @Excel(name = "船名", width = 15)
    String teamName;

    @Excel(name = "考试船名", width = 15)
    String teamNameOld;

    @Excel(name = "职务", width = 15)
    String post;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上船时间", width = 20, format = "yyyy-MM-dd")
    Date onboardDate;

    /**
     * 分类名称
     */
    @Excel(name = "分类", width = 15)
    String categoryName;

    /**
     * 各分类总时间
     */
    Long useTimeTotal;

    /**
     * 所在部门路径
     */
    String route;

    String ruleName;

    /**
     * 关联行业名称集合
     */
    private String industryNames;

    /**
     * 是否可导出成绩报告
     * 此字段通过判断 report_template 和 state 字段实时生成，不存储在数据库中
     * (目前仅供前端判断是否显示下载按钮)
     */
    private Boolean reportExportable;
}
