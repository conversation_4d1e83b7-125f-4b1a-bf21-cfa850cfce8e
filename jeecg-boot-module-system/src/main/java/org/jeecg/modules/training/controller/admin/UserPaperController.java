package org.jeecg.modules.training.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.fields.FieldFilter;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.poi.CustomJeecgEntityExcelView;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.system.service.ISysUserDepartService;
import org.jeecg.modules.training.async.paper.PushPaperService;
import org.jeecg.modules.training.async.phase.PushPhaseService;
import org.jeecg.modules.training.entity.Paper;
import org.jeecg.modules.training.entity.UserPaper;
import org.jeecg.modules.training.service.IPaperService;
import org.jeecg.modules.training.service.IUserPaperService;
import org.jeecg.modules.training.service.TraineeService;
import org.jeecg.modules.training.util.DateTimeUtils;
import org.jeecg.modules.training.vo.TraineeVO;
import org.jeecg.modules.training.vo.UserPaperVO;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户试卷关系
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年12月17日 15:02
 */
@Slf4j
@Api(tags = "用户试卷关系")
@RestController
@RequestMapping("/adm/userPaper")
public class UserPaperController extends JeecgController<UserPaper, IUserPaperService> {
    final static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    @Autowired
    private TraineeService traineeService;

    @Autowired
    private PushPaperService pushPaperService;

    @Resource
    private ISyncStateService syncStateService;

    @Resource
    private PushPhaseService userPhaseHandleService;

    @Autowired
    private ISysDepartService sysDepartService;

    @Resource
    private IPaperService paperService;

    @Autowired
    private ISysUserDepartService sysUserDepartService;


    /**
     * 分页列表查询
     *
     * @param vo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "用户试卷关系-分页列表查询")
    @ApiOperation(value = "用户试卷关系-分页列表查询", notes = "用户试卷关系-分页列表查询")
    @GetMapping(value = "/list")
    @FieldFilter({"id", "userName", "teamName", "userPost", "post", "teamNameOld", "boardTime", "phone", "paperName", "categoryName", "paperType", "status", "totalTimes", "doneTimes", "passedTimes", "failedTimes",
        "totalDuration", "type", "createTime", "startTime", "warnTime", "endTime", "phaseLevel", "chargeType", "iteamPrice"
    })
    public Result<?> queryPageList(UserPaperVO vo, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {

        if (StringUtils.isEmpty(vo.getUserId())) vo.setUserId(null);

        //因为变更船舶后，推送可能会被保留，所以不能默认过滤当前船舶和职务
//        else {
//            //保证获取当前所在船只的考试推送
//            SysUser sysUser = sysUserService.getById(vo.getUserId());
//            vo.setOrgCode(sysUser.getOrgCode());
//            vo.setPost(sysUser.getPost());
//        }

        QueryWrapper<UserPaperVO> queryWrapper = QueryGenerator.initQueryWrapper(vo, req.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        queryWrapper.eq(!sysUser.getUsername().equals("admin"), "user_dep_route", sysUser.getDepRoute()).lambda().and(q -> q.likeRight(UserPaperVO::getDepRoute, sysUser.getDepRoute()));

        Page<UserPaperVO> page = new Page<>(pageNo, pageSize);
        IPage<UserPaperVO> userPaperList = service.listPages(page, queryWrapper);
        return Result.OK(userPaperList);
    }

    /**
     * 添加
     *
     * @param vo
     * @return
     */
    @AutoLog(value = "用户试卷关系-添加")
    @ApiOperation(value = "用户试卷关系-添加", notes = "用户试卷关系-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody UserPaperVO vo) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }
        Paper paper = paperService.getById(vo.getPaperId());
        vo.setDelFlag(CommonConstant.DEL_FLAG_0);
        vo.setDepRoute(paper.getDepRoute());
        service.add(vo);
        pushPaperService.notifyChecker();
        userPhaseHandleService.checkTrainee(traineeService.getById(vo.getUserId()));
        return Result.OK("添加成功！");
    }

    /**
     * 修改
     *
     * @param vo
     * @return
     */
    @AutoLog(value = "用户试卷关系-修改")
    @ApiOperation(value = "用户试卷关系-修改", notes = "用户试卷关系-修改")
    @PostMapping(value = "/edit")
    public Result<?> edit(@RequestBody UserPaperVO vo) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }
        vo.setUpdateBy(loginUser.getId());
        vo.setUpdateTime(new Date());
        vo.setDelFlag(CommonConstant.DEL_FLAG_0);
        setupTime(vo);
        UserPaper userPaper = new UserPaper();
        BeanUtils.copyProperties(vo, userPaper);
        service.updateById(userPaper);
        syncStateService.resetSyncState(CommonConstant.USER_PAPER, userPaper.getId().toString());
        userPhaseHandleService.checkTrainee(traineeService.getById(vo.getUserId()));
        return Result.OK("修改成功！");
    }


    private void setupTime(UserPaperVO vo) {

        if (vo.getStartTime() == null) {

            if (StringUtils.isEmpty(vo.getStartTimeVal())) {
                vo.setStartTimeVal("0");
            }

            switch (vo.getStartTimeVal()) {
                case "0":
                    vo.setStartTime(null);
                    break;
                case "1":
                    SysUser user = traineeService.getById(vo.getUserId());
                    vo.setStartTime(user.getOnboardDate());
                    break;
                case "2":
                    vo.setStartTime(DateTimeUtils.getCurrentDate());
                    break;
                default:
                    try {
                        vo.setStartTime(dateFormat.parse(vo.getStartTimeVal()));
                    } catch (ParseException e) {
                        e.printStackTrace();
                        vo.setStartTime(DateTimeUtils.getCurrentDate());
                    }
                    break;
            }

            if (vo.getStartTime() != null) {
                if (vo.getEndTimeVal() != null && !vo.getEndTimeVal().equals("0")) {
                    vo.setEndTime(DateTimeUtils.addDate(vo.getStartTime(), Integer.parseInt(vo.getEndTimeVal())));
                } else {
                    vo.setEndTime(null);
                }
                if (vo.getWarnTimeVal() != null && !vo.getWarnTimeVal().equals("0")) {
                    vo.setWarnTime(DateTimeUtils.addDate(vo.getStartTime(), Integer.parseInt(vo.getWarnTimeVal())));
                } else {
                    vo.setWarnTime(null);
                }
            } else {
                vo.setEndTime(null);
                vo.setWarnTime(null);
            }
        }
    }

    /**
     * 更新状态
     *
     * @param vo
     * @return
     */
    @AutoLog(value = "用户试卷关系-更新状态")
    @ApiOperation(value = "用户试卷关系-更新状态", notes = "用户试卷关系-更新状态")
    @PostMapping(value = "/updateStatus")
    public Result<?> updateStatus(@RequestBody UserPaperVO vo) {
        service.updateStatus(vo);
        userPhaseHandleService.checkTrainee(traineeService.getById(vo.getUserId()));
        return Result.OK("更新成功！");
    }

    /**
     * 批量更新状态
     *
     * @return
     */
    @AutoLog(value = "用户试卷关系-批量更新状态")
    @ApiOperation(value = "用户试卷关系-批量更新状态", notes = "用户试卷关系-批量更新状态")
    @PostMapping(value = "/batchUpdateStatus")
    public Result<?> batchUpdateStatus(@RequestParam(name = "ids") String ids, @RequestParam(name = "status") Integer status) {
        List<String> idList = Arrays.stream(ids.split(",")).collect(Collectors.toList());
        service.updateStatusBatch(idList, status);
        List<String> uids = service.listByIds(idList).stream().map(UserPaper::getUserId).distinct().collect(Collectors.toList());
        if (!uids.isEmpty()) userPhaseHandleService.checkTrainee(traineeService.listByIds(uids));
        return Result.OK("批量更新成功！");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "用户试卷关系-通过id删除")
    @ApiOperation(value = "用户试卷关系-通过id删除", notes = "用户试卷关系-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        syncStateService.resetSyncState(CommonConstant.USER_PAPER, id);
        UserPaper userPaper = service.getById(id);
        if (service.removeById(id)) {
            userPhaseHandleService.checkTrainee(traineeService.getById(userPaper.getUserId()));
            return Result.ok();
        }
        return Result.error("删除失败");
    }


    /**
     * 通过id批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "用户试卷关系-通过id批量删除")
    @ApiOperation(value = "用户试卷关系-通过id删除", notes = "用户试卷关系-通过id批量删除")
    @DeleteMapping(value = "/batchDelete")
    public Result<?> batchDelete(@RequestParam(name = "ids") String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).collect(Collectors.toList());
        if (service.removeBatchByIds(idList)) {
            idList.forEach(id -> syncStateService.resetSyncState(CommonConstant.USER_PAPER, id));
            List<String> uids = service.listByIds(idList).stream().map(UserPaper::getUserId).distinct().collect(Collectors.toList());
            if (!uids.isEmpty()) userPhaseHandleService.checkTrainee(traineeService.listByIds(uids));
            return Result.ok();
        }

        return Result.error("批量删除失败");
    }


    /**
     * 重新考试
     *
     * @param vo
     * @return
     */
    @AutoLog(value = "用户试卷关系-重新考试")
    @ApiOperation(value = "用户试卷关系-重新考试", notes = "用户试卷关系-重新考试")
    @PostMapping(value = "/reset")
    public Result<?> reset(@RequestParam("id") Long id) {
        service.reset(id);
        UserPaper vo = service.getById(id);
        userPhaseHandleService.checkTrainee(traineeService.getById(vo.getUserId()));
        return Result.OK("更新成功！");
    }

    /**
     * 重新考试,批量
     *
     * @return
     */
    @AutoLog(value = "用户试卷关系-批量重新考试")
    @ApiOperation(value = "用户试卷关系-批量重新考试", notes = "用户试卷关系-批量重新考试")
    @PostMapping(value = "/batchReset")
    public Result<?> batchReset(@RequestParam(name = "ids") String ids) {
        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList());
        service.resetBatch(idList);
        List<String> uids = service.listByIds(idList).stream().map(UserPaper::getUserId).distinct().collect(Collectors.toList());
        if (!uids.isEmpty()) userPhaseHandleService.checkTrainee(traineeService.listByIds(uids));
        return Result.OK("批量更新成功！");
    }


    /**
     * 获取需要考试但是还未考试的人员信息
     */
    @GetMapping("/listExamWarnList")
    @FieldFilter({"categoryName", "createBy", "createTime", "delFlag", "depRoute", "examResults", "id", "identityCard", "onboardDate", "orgCode", "paperId", "paperName", "phone", "post", "post_dictText", "realname", "route", "salt", "status",
            "status_dictText", "teamName", "totalTimes", "updateTime", "userIdentity", "userPaperId", "userPaperStatus", "userPhaseItemId", "userPhaseItemStatus", "username", "paperTime"})
    public Result<IPage<TraineeVO>> listExamWarnList(TraineeVO traineeVO, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        Result<IPage<TraineeVO>> result = new Result<>();

        Page<TraineeVO> page = new Page<>(pageNo, pageSize);

        QueryWrapper<TraineeVO> queryWrapper = QueryGenerator.initQueryWrapper(traineeVO, req.getParameterMap());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.lambda().and(q -> q.likeRight(TraineeVO::getDepRoute, loginUser.getDepRoute()));
        queryWrapper.lambda().likeRight(TraineeVO::getRoute, loginUser.getDepRoute());

        IPage<TraineeVO> traineeVOList = service.listExamWarnList(page, queryWrapper);

        result.setSuccess(true);
        result.setResult(traineeVOList);
        return result;

    }


    /**
     * 导出excel
     *
     * @param request
     * @param traineeVO
     */
    @RequestMapping(value = "/exportWarnXls")
    public ModelAndView exportWarnXls(TraineeVO traineeVO, HttpServletRequest request) {

        String title;
        if (traineeVO.getExamResults() == null) {
            title = "未考人员列表";
        } else {
            title = traineeVO.getExamResults() == 0 ? "未完成人员列表" : "未通过人员列表";
        }

        Page<TraineeVO> page = new Page<>(1, 10000000);

        QueryWrapper<TraineeVO> queryWrapper = QueryGenerator.initQueryWrapper(traineeVO, request.getParameterMap());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.lambda().and(q -> q.likeRight(TraineeVO::getDepRoute, loginUser.getDepRoute()));
        queryWrapper.lambda().likeRight(TraineeVO::getRoute, loginUser.getDepRoute());

        IPage<TraineeVO> traineeVOList = service.listExamWarnList(page, queryWrapper);

        // Step.2 获取导出数据
        List<TraineeVO> pageList = traineeVOList.getRecords();

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new CustomJeecgEntityExcelView());
        if (request.getParameter(QueryGenerator.QUERY_COLUMNS) != null) {
            mv.addObject(NormalExcelConstants.EXPORT_FIELDS, request.getParameter(QueryGenerator.QUERY_COLUMNS));
        }
        //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, title);
        mv.addObject(NormalExcelConstants.CLASS, TraineeVO.class);
        ExportParams exportParams = new ExportParams(title + "报表", "导出人:" + loginUser.getRealname(), title);

        exportParams.setType(ExcelType.XSSF);
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }
}
