package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 管理者
 * @Author: hzk
 * @Date: 2022-11-24
 * @Version: V1.0
 */
@Data
@TableName("managers")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "managers对象", description = "管理者")
@NoArgsConstructor
public class Managers {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键id")
    private Integer id;
    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 15)
    @ApiModelProperty(value = "用户id")
    private String sysUserId;

    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 15)
    @ApiModelProperty(value = "用户id")
    private String phone;

    /**
     * 用户身份
     */
    @Excel(name = "用户身份", width = 15)
    @ApiModelProperty(value = "用户身份")
    private String identity;

    /**
     * 管理船舶ID
     */
    @Excel(name = "管理船舶ID", width = 15)
    @ApiModelProperty(value = "管理船舶ID")
    private String teamId;

    // 管理船队
    private String fleet;

    /**
     * 删除状态（0，正常，1已删除）
     */
    @Excel(name = "删除状态（0，正常，1已删除）", width = 15)
    @ApiModelProperty(value = "删除状态（0，正常，1已删除）")
    @TableLogic
    private Integer delFlag;
    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @Excel(name = "更新人", width = 15)
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    // 过滤路径
    private String depRoute;

    public Managers(String sysUserId, String phone, String identity, String teamId, String fleet, String depRoute, Integer delFlag ) {
        this.sysUserId = sysUserId;
        this.phone = phone;
        this.identity = identity;
        this.teamId = teamId;
        this.fleet = fleet;
        this.depRoute = depRoute;
        this.delFlag = delFlag;
    }
}
