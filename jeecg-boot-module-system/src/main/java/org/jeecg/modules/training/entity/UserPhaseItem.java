package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 用户阶段数据（自动推送+手动赋值）
 *
 * @TableName user_phase_item
 */
@TableName(value = "user_phase_item")
public class UserPhaseItem {

    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 阶段ID
     */
    private Long phaseId;
    /**
     * 阶段数据数据ID
     */
    private Long phaseItemId;

    /**
     * 完成状态,
     * paper
     * 0 未完成，1 已完成，2，未通过
     * <p>
     * study
     * -1需要完成，0未完成，>=1已完成次数
     */
    private Integer status;

    /**
     * 已经完成数量
     */
    private Integer done;

    /**
     * 需要完成的总数量
     */
    private Integer total;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改时间x
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     *
     */
    public Long getId() {
        return id;
    }

    /**
     *
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 用户ID
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 用户ID
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * 阶段ID
     */
    public Long getPhaseItemId() {
        return phaseItemId;
    }

    /**
     * 阶段ID
     */
    public void setPhaseItemId(Long phaseItemId) {
        this.phaseItemId = phaseItemId;
    }

    /**
     * 完成状态,
     * paper
     * 0 未完成，1 已完成，2，未通过
     * <p>
     * study
     * -1需要完成，0未完成，>=1已完成次数
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 完成状态,
     * paper
     * 0 未完成，1 已完成，2，未通过
     * <p>
     * study
     * -1需要完成，0未完成，>=1已完成次数
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 创建人
     */
    public String getCreateBy() {
        return createBy;
    }

    /**
     * 创建人
     */
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 修改人
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     * 修改人
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(Long phaseId) {
        this.phaseId = phaseId;
    }

    public Integer getDone() {
        return done;
    }

    public void setDone(Integer done) {
        this.done = done;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
}