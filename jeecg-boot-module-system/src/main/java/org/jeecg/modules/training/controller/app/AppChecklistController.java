package org.jeecg.modules.training.controller.app;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.training.entity.Checklist;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.service.IChecklistService;
import org.jeecg.modules.training.service.TraineeService;
import org.jeecg.modules.training.vo.ChecklistVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: 许可证和检查配置主表
 * @Author: jeecg-boot
 * @Date: 2022-10-23
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "许可证和检查配置主表")
@RestController
@RequestMapping("/app/checklist")
public class AppChecklistController extends JeecgController<Checklist, IChecklistService> {

    @Autowired
    private IChecklistService checklistService;

    @Autowired
    private TraineeService traineeService;

    /**
     * APP根据分类获取分页列表
     *
     * @param
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "APP根据分类获取分页列表")
    @ApiOperation(value = "APP根据分类获取分页列表", notes = "APP根据分类获取分页列表")
    @GetMapping(value = "/getChecklists")
    public Result<?> getChecklists(@RequestParam(name = "categoryId") Long categoryId,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }
        Trainee trainee = traineeService.getUserByIDCard(loginUser.getUsername());
        String post = trainee.getPost();
        Page<ChecklistVO> page = new Page<ChecklistVO>(pageNo, pageSize);
        IPage<ChecklistVO> pageList = checklistService.findChecklists(page, categoryId, post);
        return Result.OK(pageList);
    }

}
