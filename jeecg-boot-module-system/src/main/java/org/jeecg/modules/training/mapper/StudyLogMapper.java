package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.StudyLog;
import org.jeecg.modules.training.vo.StudyLogOneMonth;
import org.jeecg.modules.training.vo.StudyLogVO;

import java.util.List;

/**
 * @Description: 学习日志表
 * @Author: hzk
 * @Date: 2022-09-02
 * @Version: V1.0
 */
public interface StudyLogMapper extends BaseMapper<StudyLog> {

    IPage<StudyLogVO> getPageList(Page<StudyLogVO> page, @Param("ew") QueryWrapper<StudyLogVO> queryWrapper);

    IPage<StudyLogVO> getStudyList(Page<StudyLogVO> page, @Param("ew") QueryWrapper<StudyLogVO> queryWrapper);

    /**
     * 查询所有符合条件的学习记录
     *
     * @param queryWrapper
     * @return
     */
    List<StudyLogVO> getPageList(@Param("ew") QueryWrapper<StudyLogVO> queryWrapper);

    /**
     * 获取用户需要阅读并且已经阅读的数量
     *
     * @param userId
     * @param categoryId
     * @param boardTime
     * @param post
     * @param groupInfo
     * @return
     */
    Long getAlreadyReadNumber(@Param("userId") String userId, @Param("categoryId") Long categoryId, @Param("boardTime") String boardTime, @Param("post") String post, @Param("groupInfo") String groupInfo);

    /**
     * 获取用户需要关注并且已经关注的数量
     *
     * @param userId
     * @param boardTime
     * @param post
     * @param groupInfo
     * @return
     */
    Long getAlreadyAttentionNumber(@Param("userId") String userId, @Param("categoryRoute") String categoryRoute, @Param("boardTime") String boardTime, @Param("post") String post, @Param("groupInfo") String groupInfo);


    List<StudyLogVO> getStudyList(@Param("ew") QueryWrapper<StudyLogVO> queryWrapper);

    List<StudyLogOneMonth> getOneMonthRecordList(@Param("ew") QueryWrapper<StudyLogOneMonth> queryWrapper, @Param("depRoute") String depRoute);

    IPage<StudyLogOneMonth> getOneMonthRecordLisPage(Page<StudyLogOneMonth> page, @Param("ew") QueryWrapper<StudyLogOneMonth> queryWrapper, @Param("depRoute") String depRoute);
}
