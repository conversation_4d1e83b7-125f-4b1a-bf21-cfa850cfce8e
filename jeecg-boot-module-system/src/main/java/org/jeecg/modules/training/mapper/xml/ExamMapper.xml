<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.ExamMapper">
  <update id="repeat">
    UPDATE `exam`
    SET `state`      = 1,
        `result`     = NULL,
        `user_score` = NULL,
        `start_time` = NULL,
        `end_time`   = NULL,
        `use_time`   = NULL
    WHERE id = #{id}
  </update>
  <select id="getAll" resultType="org.jeecg.modules.training.vo.ExamVO">
    SELECT root.*,
    su.username username,
    ca.name category_name
    FROM exam root
    LEFT JOIN category ca ON root.category_id = ca.id
    left join trainee su on su.id = root.user_id
    WHERE 1 = 1
    <if test="mp.id != null and mp.id != ''">AND root.id=#{mp.id}</if>
    <if test="mp.result != null and mp.result != ''">AND root.result like concat('%', #{mp.result},'%')</if>
    <if test="mp.categoryId != null and mp.categoryId != ''">AND root.category_id= #{mp.categoryId}</if>
    <if test="mp.categoryName != null and mp.categoryName != ''">AND ca.name like concat('%',
      #{mp.categoryName},'%')
    </if>
    <if test="mp.userId != null and mp.userId != ''">AND root.user_id = #{mp.userId}</if>
    <if test="mp.username != null and mp.username != ''">AND su.username like concat('%', #{mp.username},'%')</if>
    <if test="mp.delFlag != null and mp.delFlag != ''">AND root.del_flag= #{mp.delFlag}</if>
    <if test="mp.state != null and mp.state != ''">AND root.state = ${mp.state}</if>
    ORDER BY root.create_time
  </select>

  <!--    -->
  <update id="resetUserExam">

    UPDATE exam e, paper p
    SET e.del_flag = 1
    WHERE e.user_id = #{userId}
      AND (e.del_flag = 0 OR e.del_flag IS NULL)
      AND e.paper_id = p.id
      AND p.auto_expire
      AND (p.keep_duration = 0 OR (TIMESTAMPDIFF(DAY, e.create_time, NOW()) > p.keep_duration))
  </update>

  <select id="getPageList" resultType="org.jeecg.modules.training.vo.ExamListVO">
    SELECT *
    FROM (SELECT e.*,
                 CASE
                     WHEN e.report_template IS NOT NULL AND e.report_template != 'UNSUPPORTED' AND e.state IN (2) THEN TRUE
                     ELSE FALSE
                 END             AS report_exportable,
                 c.name          AS category_name,
                 t.realname,
                 t.identity_card,
                 t.phone,
                 t.post,
                 t.onboard_date,
                 t.work_no,
                 te.team_name,
                 sd.route,
                 te1.team_name AS team_name_old,
                 r.name          AS ruleName
          FROM exam e
               LEFT JOIN category c ON e.category_id = c.id
               LEFT JOIN trainee t ON t.id = e.user_id
               LEFT JOIN team te1 ON te1.id = e.team_id
               LEFT JOIN rule r ON e.rule_id = r.id
               LEFT JOIN team te ON t.team_id = te.id
               LEFT JOIN sys_trainee_depart td ON td.trainee_id = t.id
               LEFT JOIN sys_depart sd ON td.dep_id = sd.id
          WHERE t.id IS NOT NULL) exam
      ${ew.customSqlSegment}
  </select>

  <select id="getSummaryList" resultType="org.jeecg.modules.training.vo.ExamListVO">
    SELECT *, SUM(use_time) AS use_time_total
    FROM (SELECT e.*,
                 c.name          AS category_name,
                 t.realname,
                 t.identity_card,
                 t.phone,
                 t.post,
                 t.onboard_date,
                 t.work_no,
                 te.team_name,
                 sd.route,
                 te1.team_name AS team_name_old,
                 r.name          AS ruleName
          FROM exam e
               LEFT JOIN category c ON e.category_id = c.id
               LEFT JOIN trainee t ON t.id = e.user_id
               LEFT JOIN team te1 ON te1.id = e.team_id
               LEFT JOIN rule r ON e.rule_id = r.id
               LEFT JOIN team te ON t.team_id = te.id
               LEFT JOIN sys_trainee_depart td ON td.trainee_id = t.id
               LEFT JOIN sys_depart sd ON td.dep_id = sd.id
          WHERE t.id IS NOT NULL) exam
      ${ew.customSqlSegment}
  </select>
  <select id="getToBeScoreExam" resultType="org.jeecg.modules.training.vo.ExamListVO">
      SELECT *
      FROM (SELECT e.*,
                   c.name       AS category_name,
                   su.realname,
                   su.identity_card,
                   su.phone,
                   su.post,
                   su.onboard_date,
                   su.work_no,
                   t1.team_name,
                   t2.team_name AS team_name_old,
                   e.dep_route     route,
                   r.name       AS ruleName
            FROM exam e
                     LEFT JOIN category c ON e.category_id = c.id
                     LEFT JOIN trainee su ON su.id = e.user_id
                     LEFT JOIN rule r ON e.rule_id = r.id
                     LEFT JOIN team t1 ON su.team_id = t1.id
                     LEFT JOIN team t2 ON e.team_id = t1.id
            WHERE e.team_id IN (SELECT m.team_id
                                FROM managers m
                                WHERE m.sys_user_id =  #{userId})
              AND e.result = '未打分') exam
          ${ew.customSqlSegment};
  </select>
</mapper>
