package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 阶段数据
 *
 * @TableName phase_item
 */
@TableName(value = "phase_item")
public class PhaseItem {
    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 使用模式
     * 0 全员通用
     * 1 用户专用
     */
    private Integer mode;

    /**
     * 0 category,1 paper ,2 study
     */
    private Integer type;

    /**
     * 阶段ID
     */
    private Long phaseId;

    /**
     * 所在分类ID
     */
    private Long categoryId;

    /**
     * App操作方式
     * category：
     * 0 下拉展开
     * 1 跳转菜单画面
     * paper
     * 无意义
     * study
     * 无意义
     */
    private Integer action;

    /**
     * 显示顺序
     */
    private Integer seq;

    /**
     * 数据ID
     */
    private String dataId;

    /**
     * 数据类型
     */
    private Integer dataType;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 用户专用的数据
     */
    private String forUserId;

    /**
     *
     */
    private String timeFactor;

    /**
     *
     */
    private String postFactor;

    /**
     *
     */
    private String groupFactor;

    /**
     *
     */
    private Boolean disabled;


    /**
     *
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *
     */
    private String createBy;

    /**
     *
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     *
     */
    private String updateBy;

    /**
     *
     */
    @TableLogic
    private Integer delFlag;


    /**
     * 关联行业id集合
     */
    private String industryIds;

    /**
     * 分享ID
     */
    private Long shareId;

    public String getIndustryIds() {
        return industryIds;
    }

    public void setIndustryIds(String industryIds) {
        this.industryIds = industryIds;
    }

    /**
     *
     */
    public Long getId() {
        return id;
    }

    /**
     *
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     */
    public Long getCategoryId() {
        return categoryId;
    }

    /**
     *
     */
    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    /**
     *
     */
    public String getName() {
        return name;
    }

    /**
     *
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 0 category,1 paper ,2 study
     */
    public Integer getType() {
        return type;
    }

    /**
     * 0 category,1 paper ,2 study
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     *
     */
    public String getDataId() {
        return dataId;
    }

    /**
     *
     */
    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    /**
     *
     */
    public Integer getDataType() {
        return dataType;
    }

    /**
     *
     */
    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    /**
     *
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     *
     */
    public String getCreateBy() {
        return createBy;
    }

    /**
     *
     */
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     *
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     *
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    public Long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(Long phaseId) {
        this.phaseId = phaseId;
    }

    public Integer getMode() {
        return mode;
    }

    public void setMode(Integer mode) {
        this.mode = mode;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getForUserId() {
        return forUserId;
    }

    public void setForUserId(String forUserId) {
        this.forUserId = forUserId;
    }

    public String getTimeFactor() {
        return timeFactor;
    }

    public void setTimeFactor(String timeFactor) {
        this.timeFactor = timeFactor;
    }

    public String getPostFactor() {
        return postFactor;
    }

    public void setPostFactor(String postFactor) {
        this.postFactor = postFactor;
    }

    public String getGroupFactor() {
        return groupFactor;
    }

    public void setGroupFactor(String groupFactor) {
        this.groupFactor = groupFactor;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    public Long getShareId() {
        return shareId;
    }

    public void setShareId(Long shareId) {
        this.shareId = shareId;
    }
}