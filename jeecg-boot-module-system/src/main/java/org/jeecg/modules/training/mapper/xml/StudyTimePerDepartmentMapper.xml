<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.StudyTimePerDepartmentMapper">
  <select id="getPageList" resultType="org.jeecg.modules.training.vo.StudyTimePerDepartmentVO">
      select * from (
      select sd.team_name as department_name,
      s.name as study_name,
      sum(study_time) as study_time
      from study_time_per_department stpd,
      team sd,
      study s
      where stpd.department_id = sd.id
      <if test="scope == 'week'">
          and WEEK(stpd.date, 1) = WEEK(now(), 1)
          and YEAR(stpd.date) = YEAR(CURDATE())
      </if>
      <if test="scope == 'month'">
          and MONTH(stpd.date) = MONTH(NOW())
          and YEAR(stpd.date) = YEAR(CURDATE())
      </if>
      and stpd.study_id = s.id
      and sd.route like concat(#{depRoute},'%')
      group by stpd.department_id, stpd.study_id
      ) as t
      ${ew.customSqlSegment}
  </select>
  <select id="completeRate" resultType="org.jeecg.modules.training.vo.StudyTimePerDepartmentVO">
      SELECT
      *
      FROM
      (SELECT
      IFNULL(COUNT(t1.user_id) / t.trainee_count, 0) AS complete_rate,
      s.name AS study_name,
      t1.depart_name AS department_name
      FROM
      (SELECT DISTINCT
      sl.user_id,
      sl.study_id,
      sd.id AS depart_id,
      sd.team_name AS depart_name
      FROM
      study_log sl, trainee te, team sd
      WHERE
      sl.user_id = te.id
      AND te.team_id = sd.id
      AND te.status = 1
      AND te.del_flag = 0
      AND sd.route LIKE CONCAT(#{depRoute}, '%')
      AND sl.type = 0
      AND sl.end_time IS NOT NULL
      <if test="scope == 'week'">
          and WEEK(sl.end_time, 1) = WEEK(now(), 1)
          and YEAR(sl.end_time) = YEAR(CURDATE())
      </if>
      <if test="scope == 'month'">
          and MONTH(sl.end_time) = MONTH(NOW())
          and YEAR(sl.end_time) = YEAR(CURDATE())
      </if>) t1, study s,team t
      WHERE
      t1.study_id = s.id and t1.depart_id = t.id
      GROUP BY t1.depart_id , t1.study_id) t2
      ${ew.customSqlSegment}
  </select>

</mapper>

<!--
team 表新增 trainee_count 字段，在导入、下船、删除受试人时，更新该字段的值。
completeRate 要优化成下面这样：
SELECT
    *
FROM
    (SELECT
        IFNULL(COUNT(t1.user_id) / t.trainee_count, 0 , 0) AS complete_rate,
            s.name AS study_name,
            t1.depart_name AS department_name
    FROM
        (SELECT DISTINCT
        sl.user_id,
            sl.study_id,
            sd.id AS depart_id,
            sd.team_name AS depart_name
    FROM
        study_log sl, trainee te, team sd
    WHERE
        sl.user_id = te.id
            AND te.team_id = sd.id
            AND sd.route LIKE CONCAT('/e5e941a5c0084ceca41677acda7aed76/e8b43e9beafe49c59a6854ab8e0ac057', '%')
            AND sl.type = 0
            AND sl.end_time IS NOT NULL) t1, study s,team t
    WHERE
        t1.study_id = s.id and t1.depart_id = t.id
    GROUP BY t1.depart_id , t1.study_id) t2
LIMIT 10;
-->