package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.jeecg.modules.training.entity.SysTraineeDepart;

import java.util.List;

/**
 * @Description: 受试人部门信息表
 */
@Mapper
public interface SysTraineeDepartMapper extends BaseMapper<SysTraineeDepart> {
    // 获取指定套餐下的所有会员船员id
    List<String> listTraineeIdByPackagesId(Long packagesId);

    // 获取企业会员公司没有开会员的部门船员对象
    List<SysTraineeDepart> getCompanyNoMember(String sysDepartId);

    // 通过trainee_id和dep_id,获取packages_id
    Long getPackagesIdByTraineeIdAndDepIdLong(String traineeId, String depId);
}
