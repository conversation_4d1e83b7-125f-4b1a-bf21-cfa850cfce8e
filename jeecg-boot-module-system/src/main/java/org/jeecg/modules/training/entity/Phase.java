package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 培训成长阶段
 *
 * @TableName phase
 */
@TableName(value = "phase")
@Data
public class Phase {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 阶段名称
     */
    private String name;

    /**
     * 阶段等级
     */
    private Integer level;

    /**
     * 切换规则
     * 0：手动切换
     * 1：全部考试完成则切换
     * 2：上船时间超过N天强制切换
     */
    private Integer rule;

    /**
     * rule = 2 时，天数
     * 其他为空
     */
    private String ruleData;

    /**
     * 自动推送
     * 0 否
     * 1 是
     */
    private Integer autoPush;

    /**
     * 备注
     */
    private String memo;

    /**
     *
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *
     */
    private String createBy;

    /**
     *
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     *
     */
    private String updateBy;

    /**
     *
     */
    @TableLogic
    private Integer delFlag;

    private String shareMode;

    private Long shareId;

    /**
     * 关联行业名称集合
     */
    @TableField(exist = false)
    private String industryNames;

    /**
     * 关联行业id集合
     */
    private String industryIds;

    private String depRoute;

    /**
     * 培训阶段图标
     */
    private String icon;
}