package org.jeecg.modules.training.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.training.entity.*;
import org.jeecg.modules.training.mapper.PackagesMapper;
import org.jeecg.modules.training.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import org.jeecg.modules.training.vo.PriceInfoVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * @Description: 套餐价格计算服务实现
 */
@Service
@Slf4j
public class PackagePriceCalculationServiceImpl implements IPackagePriceCalculationService {

    @Autowired
    private ISysDepartService sysDepartService;

    @Autowired
    private IPackagePriceService packagePriceService;

    @Autowired
    private IPaperService paperService;

    @Autowired
    private IStudyService studyService;

    @Autowired
    private TraineeService traineeService;

    @Autowired
    private IPackagesService packagesService;

    @Autowired
    private ITraineeOrderedIteamsService traineeOrderedIteamsService;

    @Autowired
    private IDepartOrderedIteamsService departOrderedIteamsService;

    @Autowired
    private PackagesMapper packagesMapper;

    /**
     * 计算套餐升级价格
     * 升级价格 = 目标套餐价格 - 当前套餐价格 * (剩余天数 / 365)
     */
    private BigDecimal calculateUpgradePrice(BigDecimal currentPrice, BigDecimal targetPrice, long remainingDays) {
        if (remainingDays <= 0) {
            return targetPrice;
        }

        // 计算当前套餐的剩余价值
        BigDecimal daysInYear = new BigDecimal(365);
        BigDecimal remainingDaysBD = new BigDecimal(remainingDays);
        BigDecimal remainingValue = currentPrice.multiply(remainingDaysBD).divide(daysInYear, 2, RoundingMode.HALF_UP);

        // 计算升级价格
        BigDecimal upgradePrice = targetPrice.subtract(remainingValue);

        // 确保升级价格不低于0
        return upgradePrice.max(BigDecimal.ZERO);
    }

    /**
     * 获取套餐&学习资料价格信息（从当前登录用户中获取船员ID）
     * 此函数应该只有船员会员制企业会调用
     */
    /**
     * 获取套餐&学习资料价格信息
     * 此函数应该只有船员会员制企业会调用
     */
    @Override
    public PriceInfoVO getPrice(String traineeId, String departId, Integer productCategorie, Long productDetail) {
        // 获取当前日期
        Date now = new Date();
        // 获取当前登录公司
        SysDepart sysDepart = sysDepartService.getById(departId);
        if (sysDepart == null || sysDepart.getDelFlag() == 1) {
            log.error("操作失败，sysDepartId：{}对应的公司不存在", departId);
            throw new JeecgBootException("操作失败！找不到该船员的所属公司");
        }
        if (sysDepart.getMemberType() == 1) {
            throw new IllegalArgumentException("企业会员制企业不应该调用此接口");
        }

        // 根据商品类型获取价格
        BigDecimal price;
        String description = "";
        Date startTime = null;
        Date expireTime = null;

        // 套餐升级或套餐续费
        if (productCategorie == 0 || productCategorie == 1) {
            // 设置商品描述
            if (productCategorie == 0) {
                description = "套餐升级";
            } else {
                description = "套餐续费";
            }

            if (productDetail == (long) CommonConstant.PACKAGE_LEV1) {
                description += " - " + CommonConstant.PACKAGE_NAME_LEV1;    // 基础版
            } else if (productDetail == (long) CommonConstant.PACKAGE_LEV2) {
                description += " - " + CommonConstant.PACKAGE_NAME_LEV2;    // 标准版
            } else if (productDetail == (long) CommonConstant.PACKAGE_LEV3) {
                description += " - " + CommonConstant.PACKAGE_NAME_LEV3;    // 尊享版
            }

            // 套餐起止时间 (默认从本日开始，时长1年)
            // 这段代码是在getPrice方法内部使用的，每次调用都会创建一个新的Calendar实例，
            // 并且这个实例只在当前方法内部使用，不会被其他线程访问。
            // 因此，在这种特定情况下，虽然Calendar本身不是线程安全的，但这段代码的使用方式是安全的。
            startTime = now;
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startTime);
            calendar.add(Calendar.YEAR, 1);
            expireTime = calendar.getTime();

            // 获取船员信息
            Trainee trainee = traineeService.getById(traineeId);
            if (trainee == null) {
                throw new RuntimeException("船员信息获取失败。");
            }

            // 获取套餐价格
            Integer packagePriceId = sysDepart.getPackagePriceId();
            PackagePrice packagePrice = packagePriceService.getById(packagePriceId);

            if (packagePrice == null) {
                throw new RuntimeException("获取套餐价格失败。");
            }

            // 根据套餐级别获取价格
            BigDecimal basePrice;
            if (productDetail == (long) CommonConstant.PACKAGE_LEV1) {
                basePrice = packagePrice.getLev1Price();
            } else if (productDetail == (long) CommonConstant.PACKAGE_LEV2) {
                basePrice = packagePrice.getLev2Price();
            } else if (productDetail == (long) CommonConstant.PACKAGE_LEV3) {
                basePrice = packagePrice.getLev3Price();
            } else {
                throw new IllegalArgumentException("未知套餐等级。");
            }

            // 如果是套餐升级，需要计算差价
            if (productCategorie == 0) {
                // 获取当前套餐信息
                Packages currentPackage = null;
                if (trainee.getCurrentPackagesId() != null) {
                    currentPackage = packagesService.getById(trainee.getCurrentPackagesId());
                }

                // 如果有当前套餐，计算升级价格
                if (currentPackage != null && currentPackage.getDelFlag() == 0 && currentPackage.getStatus() == 1 &&
                        currentPackage.getVersionType() != null && currentPackage.getExpireDate() != null
                        && currentPackage.getExpireDate().isAfter(LocalDate.now())) {
                    // 获取当前套餐价格
                    BigDecimal currentPrice;
                    short currentVersionType = currentPackage.getVersionType();

                    if (currentVersionType == CommonConstant.PACKAGE_LEV1) {
                        currentPrice = packagePrice.getLev1Price();
                    } else if (currentVersionType == CommonConstant.PACKAGE_LEV2) {
                        currentPrice = packagePrice.getLev2Price();
                    } else if (currentVersionType == CommonConstant.PACKAGE_LEV3) {
                        currentPrice = packagePrice.getLev3Price();
                    } else {
                        throw new IllegalArgumentException("未知套餐类型。");
                    }

                    // 计算剩余天数
                    LocalDate expireDate = currentPackage.getExpireDate();
                    LocalDate today = LocalDate.now();
                    long remainingDays = ChronoUnit.DAYS.between(today, expireDate);

                    // 计算升级价格
                    price = calculateUpgradePrice(currentPrice, basePrice, remainingDays);
                } else {
                    // 如果没有当前套餐，直接返回一年价格
                    price = basePrice;
                }
            } else {
                // 如果是套餐续费
                // 获取最后购买的套餐信息
                Packages lastPackage = null;
                if (trainee.getLastPackagesId() != null) {
                    lastPackage = packagesService.getById(trainee.getLastPackagesId());
                }
                LocalDate today = LocalDate.now();
                if (lastPackage != null && lastPackage.getDelFlag() == 0 && lastPackage.getStatus() == 1 &&
                    lastPackage.getExpireDate() != null && lastPackage.getExpireDate().isAfter(today)) {
                    LocalDate startDate = lastPackage.getExpireDate().plusDays(1);  // 开始日：最后近一次购买的套餐过期日+1天
                    LocalDate expireDate = startDate.plusYears(1);                  // 截止日：本套餐开始日+1年

                    // 转换为Date类型用于格式化显示
                    startTime = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    expireTime = Date.from(expireDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                }

                // 直接返回一年价格
                price = basePrice;
            }
            // 向商品详情中追加起止日期
            description += String.format(", %1$tY.%1$tm.%1$td ~ %2$tY.%2$tm.%2$td", startTime, expireTime);

        }
        // 单独购买考试
        else if (productCategorie == 2) {
            Paper paper = paperService.getById(productDetail);
            if (paper == null) {
                log.error("操作失败，paperId：{}对应的考试不存在", productDetail);
                throw new JeecgBootException("操作失败！该考试不存在");
            }
            // 从属于企业的记录中获取商品说明
            description = "考试购买-" + paper.getName();
            Long shareId = paper.getShareId();
            if (shareId == null) {
                // 只有从admin共享的资料或考试，支持单独购买
                price = null;   // price设为null, 表示不支持单独购买
            } else {
                // 通过share_id从属于admin的记录中获取价格(防止租户自行修改价格)
                paper = paperService.getById(shareId);
                price = paper.getIteamPrice();
            }
        }
        // 单独购买学习资料
        else if (productCategorie == 3) {
            Study study = studyService.getById(productDetail);
            if (study == null) {
                log.error("操作失败，studyId：{}对应的学习资料不存在", productDetail);
                throw new JeecgBootException("操作失败！该学习资料不存在");
            }
            // 从属于企业的记录中获取商品说明
            description = "学习资料购买-" + study.getName();
            Long shareId = study.getShareId();
            if (shareId == null) {
                // 只有从admin共享的资料或考试，支持单独购买
                price = null;   // price设为null, 表示不支持单独购买
            } else {
                // 通过share_id从属于admin的记录中获取价格(防止租户自行修改价格)
                study = studyService.getById(shareId);
                price = study.getIteamPrice();
            }
        }
        // 非法参数
        else {
            log.error("操作失败，商品类型不支持：{}", productCategorie);
            throw new IllegalArgumentException("操作失败！商品类型不支持");
        }

        // 限制description的长度在64个字符以下，超出则截断并输出日志信息
        if (description.length() > 64) {
            String originalDescription = description;
            description = description.substring(0, 64);
            log.warn("商品描述超过64个字符被截断，原描述：{}，截断后：{}", originalDescription, description);
        }

        // Price精确到小数点后2位(四舍五入)
        if (price != null) {
            price = price.setScale(2, RoundingMode.HALF_UP);
        }

        // 注意
        // 当购买套餐（升级或续费）时，返回的 PriceInfoVO 对象包含套餐的起始日期和终止日期
        // 当购买学习资料或考试时，返回的 PriceInfoVO 对象中的起始日期和终止日期字段为 null
        return new PriceInfoVO(price, description, startTime, expireTime);
    }

    /**
     * 检查用户是否有权限访问指定的学习资料或考试
     *
     * @param traineeId 船员ID
     * @param departId 部门ID
     * @param categorieType 资料类型, 2:考试(paper), 3:学习资料(study)
     * @param iteamId 资料ID
     * @return 是否有权限访问
     */
    @Override
    public Boolean checkAccessPermission(String traineeId, String departId, Short categorieType, Long iteamId) {
        // 获取船员信息
        Trainee trainee = traineeService.getById(traineeId);
        if (trainee == null) {
            log.error("船员信息不存在，traineeId: {}", traineeId);
            throw new IllegalArgumentException("船员信息不存在。");
        }

        // 获取公司信息
        SysDepart sysDepart = sysDepartService.getById(departId);
        if (sysDepart == null || sysDepart.getDelFlag() == 1) {
            log.error("用户所属公司不存在，departId: {}, traineeId: {}", departId, traineeId);
            throw new IllegalArgumentException("用户所属公司不存在。");
        }

        // 1. 检查船员套餐等级满足资料所需的套餐等级
        // 1.1 获取资料所需的套餐等级
        Short requiredPackageLevel = getIteamRequiredPackageLevel(departId, categorieType, iteamId);
        // 1.2 获取船员当前的套餐等级
        Short memberPackageLevel = getMemberPackageLevel(traineeId, departId);
        // 1.3 若船员等级满足，直接返回有权限
        if (memberPackageLevel >= requiredPackageLevel) {
            return true;
        }

        // 4. 检查是否已单独购买
        // 4.1 通过iteamId获取shareId
        Long shareId = null;
        if (categorieType == 2) {   // paper
            Paper paper = paperService.getById(iteamId);
            if (paper == null) {
                log.error("操作失败，paperId：{}对应的考试不存在", iteamId);
                throw new JeecgBootException("操作失败！该考试不存在");
            }
            shareId = paper.getShareId();
        } else if (categorieType == 3) {  // study
            Study study = studyService.getById(iteamId);
            if (study == null) {
                log.error("操作失败，paperId：{}对应的学习资料不存在", iteamId);
                throw new JeecgBootException("操作失败！该学习资料不存在");
            }
            shareId = study.getShareId();
        } else {
            log.error("操作失败，商品类型不支持：{}", categorieType);
            throw new IllegalArgumentException("操作失败！商品类型不支持");
        }
        // 没有share_id 说明此iteam不是通过admin分享的, 不可单独购买, 也不会有购买记录
        if (shareId == null) {
            return false;   // 拒绝访问
        }
        // 4.2 使用shareId检索购买记录
        if (sysDepart.getMemberType() == 0) { // 船员会员模式
            // 查询trainee_ordered_iteams表
            LambdaQueryWrapper<TraineeOrderedIteams> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TraineeOrderedIteams::getTraineeId, traineeId)
                    .eq(TraineeOrderedIteams::getCategorieType, categorieType)
                    .eq(TraineeOrderedIteams::getIteamId, shareId);
            return traineeOrderedIteamsService.exists(queryWrapper);
        } else { // 企业会员模式
            // 查询depart_ordered_iteams表
            LambdaQueryWrapper<DepartOrderedIteams> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DepartOrderedIteams::getDepartId, departId)
                    .eq(DepartOrderedIteams::getCategorieType, categorieType)
                    .eq(DepartOrderedIteams::getIteamId, shareId);
            return departOrderedIteamsService.exists(queryWrapper);
        }
    }

    /**
     * 获取资料所需的套餐等级
     *
     * @param departId 部门ID
     * @param productCategorie 商品类型 2:单独购买考试, 3:单独购买学习资料
     * @param productDetail 商品详情 资料id:(paper_id or study_id)
     * @return 资料所需的套餐等级: 0:免费, 1:基础版, 2:标准版, 3:尊享版, 99:套餐之外的资料,只能单独购买
     */
    @Override
    public Short getIteamRequiredPackageLevel(String departId, Short productCategorie, Long productDetail) {
        // 检查参数有效性
        if (productDetail == null || (productCategorie != 2 && productCategorie != 3)) {
            log.error("参数无效，productCategorie: {}, productDetail: {}", productCategorie, productDetail);
            throw new IllegalArgumentException("参数无效，商品类型必须为2(考试)或3(学习资料)");
        }

        // 获取资料信息
        boolean needPackage = false;
        Short chargeType = null;

        if (productCategorie == 2) { // 2: 考试
            Paper paper = paperService.getById(productDetail);
            if (paper == null) {
                log.error("考试不存在，paperId: {}", productDetail);
                throw new IllegalArgumentException("考试不存在");
            }
            needPackage = paper.getChargeStatus() == 1;
            chargeType = paper.getChargeType().shortValue();
        } else { // 3: 学习资料
            Study study = studyService.getById(productDetail);
            if (study == null) {
                log.error("学习资料不存在，studyId: {}", productDetail);
                throw new IllegalArgumentException("学习资料不存在");
            }
            needPackage = study.getChargeStatus() == 1;
            chargeType = study.getChargeType().shortValue();
        }

        // 如果资料不需要开通套餐，套餐等级可认为是 0
        if (!needPackage) { chargeType = CommonConstant.PACKAGE_LEV0; }

        // 返回资料所需的套餐等级
        return chargeType;
    }

    /**
     * 获取指定船员所拥有的套餐等级
     *
     * @param traineeId 船员ID
     * @param departId 部门ID
     * @return 船员所拥有的套餐等级: 0:无有效套餐, 1:基础版, 2:标准版, 3:尊享版
     */
    @Override
    public Short getMemberPackageLevel(String traineeId, String departId) {
        // 检查参数有效性
        if (oConvertUtils.isEmpty(traineeId) || oConvertUtils.isEmpty(departId)) {
            log.error("参数无效，traineeId: {}, departId: {}", traineeId, departId);
            throw new IllegalArgumentException("参数无效，船员ID和部门ID不能为空");
        }

        // 获取公司信息
        SysDepart sysDepart = sysDepartService.getById(departId);
        if (sysDepart == null || sysDepart.getDelFlag() == 1) {
            log.error("公司不存在，departId: {}", departId);
            throw new IllegalArgumentException("公司不存在");
        }

        // 根据企业类型获取船员套餐等级
        Short level;
        if (sysDepart.getMemberType() == 0) { // 船员会员模式
            // 从trainee表关联packages表查询会员状态
            level = packagesMapper.getTraineeMemberLevel(traineeId);
        } else { // 企业会员模式
            // 从sys_trainee_depart关联packages表查询会员状态
            level = packagesMapper.getDepartMemberLevel(traineeId, departId);
        }

        return level;
    }

    /**
     * 获取资料购买信息
     * 包括升级套餐价格和单独购买价格两部分信息
     *
     * @param traineeId 船员ID
     * @param departId 部门ID
     * @param categorieType 资料类型, 2:考试(paper), 3:学习资料(study)
     * @param iteamId 资料ID
     * @return 资料购买信息，包含升级套餐价格和单独购买价格
     */
    @Override
    public JSONObject getIteamPaymentInfo(String traineeId, String departId, Short categorieType, Long iteamId) {
        try {
            // 准备返回数据
            JSONObject result = new JSONObject();

            // 1. 获取资料所需的套餐等级
            Short requiredPackageLevel = getIteamRequiredPackageLevel(departId, categorieType, iteamId);

            // 2. 获取用户当前的套餐等级
            Short memberPackageLevel = getMemberPackageLevel(traineeId, departId);

            // 3. 获取公司信息
            SysDepart sysDepart = sysDepartService.getById(departId);
            if (sysDepart == null) {
                log.error("获取公司信息失败，departId: {}", departId);
                throw new IllegalArgumentException("获取公司信息失败");
            }

            // 4. 获取套餐价格
            Integer packagePriceId = sysDepart.getPackagePriceId();
            PackagePrice packagePrice = packagePriceService.getById(packagePriceId);

            if (packagePrice == null) {
                log.error("获取套餐价格信息失败，packagePriceId: {}", packagePriceId);
                throw new IllegalArgumentException("获取套餐价格信息失败");
            }

            // 5. 获取升级套餐价格信息
            JSONArray upgradePackages = new JSONArray();

            // 如果资料需要套餐，且用户当前套餐等级低于所需等级，则提供升级套餐选项
            if (requiredPackageLevel > CommonConstant.PACKAGE_LEV0 &&
                    requiredPackageLevel < CommonConstant.PACKAGE_LEV99 &&
                    memberPackageLevel < requiredPackageLevel) {
                // 获取所有大于等于资料所需套餐等级的套餐价格信息
                for (short i = requiredPackageLevel; i <= CommonConstant.PACKAGE_LEVMAX; ++i) {
                    // 获取套餐升级价格
                    PriceInfoVO priceInfoVO = getPrice(traineeId, departId, 0, (long) i);

                    // 获取套餐全年价格及套餐名称
                    BigDecimal oneYearPrice;
                    String versionName;
                    if (i == CommonConstant.PACKAGE_LEV1) {
                        oneYearPrice = packagePrice.getLev1Price();
                        versionName = CommonConstant.PACKAGE_NAME_LEV1; // 基础版
                    } else if (i == CommonConstant.PACKAGE_LEV2) {
                        oneYearPrice = packagePrice.getLev2Price();
                        versionName = CommonConstant.PACKAGE_NAME_LEV2; // 标准版
                    } else if (i == CommonConstant.PACKAGE_LEV3) {
                        oneYearPrice = packagePrice.getLev3Price();
                        versionName = CommonConstant.PACKAGE_NAME_LEV3; // 尊享版
                    } else {
                        throw new IllegalArgumentException("未知套餐等级: " + i);
                    }
                    // 构建套餐信息
                    JSONObject packageInfo = new JSONObject();
                    packageInfo.put("versionName", versionName);
                    packageInfo.put("upgradePrice", priceInfoVO.getPrice());
                    packageInfo.put("oneYearPrice", oneYearPrice);
                    packageInfo.put("description", priceInfoVO.getDescription());

                    upgradePackages.add(packageInfo);
                }
            }

            // 6. 获取单独购买价格信息
            JSONObject singlePurchaseInfo = null;

            // 根据资料类型获取对应的资料信息
            if (categorieType == 2) { // 考试
                Paper paper = paperService.getById(iteamId);
                if (paper != null && paper.getIteamPrice() != null) {
                    PriceInfoVO priceInfoVO = getPrice(traineeId, departId, 2, iteamId);

                    // 如果可以单独购买（价格不为null）
                    if (priceInfoVO != null && priceInfoVO.getPrice() != null) {
                        singlePurchaseInfo = new JSONObject();
                        singlePurchaseInfo.put("price", priceInfoVO.getPrice());
                        singlePurchaseInfo.put("description", priceInfoVO.getDescription());
                    }
                }
            } else if (categorieType == 3) { // 学习资料
                Study study = studyService.getById(iteamId);
                if (study != null && study.getIteamPrice() != null) {
                    PriceInfoVO priceInfoVO = getPrice(traineeId, departId, 3, iteamId);

                    // 如果可以单独购买（价格不为null）
                    if (priceInfoVO != null && priceInfoVO.getPrice() != null) {
                        singlePurchaseInfo = new JSONObject();
                        singlePurchaseInfo.put("price", priceInfoVO.getPrice());
                        singlePurchaseInfo.put("description", priceInfoVO.getDescription());
                    }
                }
            }

            // 7. 组装返回数据
            result.put("upgradePackages", upgradePackages);
            if (singlePurchaseInfo != null) {
                result.put("singlePurchase", singlePurchaseInfo);
            }

            return result;
        } catch (Exception e) {
            log.error("获取资料购买信息失败", e);
            throw new RuntimeException("获取资料购买信息失败: " + e.getMessage());
        }
    }
}
