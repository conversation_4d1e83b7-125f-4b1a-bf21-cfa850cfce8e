package org.jeecg.modules.training.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.training.entity.Exam;
import org.jeecg.modules.training.entity.InvoiceApply;
import org.jeecg.modules.training.entity.Orders;
import org.jeecg.modules.training.service.IInvoiceApplyService;
import org.jeecg.modules.training.service.IOrdersService;
import org.simpleframework.xml.core.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/app/invoice")
public class AppInvoiceApplyController {


    @Autowired
    private IInvoiceApplyService invoiceApplyService;

    @Autowired
    private IOrdersService ordersService;

    /**
     * app申请开发票
     *
     * @param invoiceApply Invoice
     * @return String
     */
    @PostMapping("/apply")
    @ApiOperation(value = "app申请开发票", notes = "app申请开发票")
    public Result<String> applyInvoice(@Validated @RequestBody InvoiceApply invoiceApply) {
        return invoiceApplyService.applyInvoice(invoiceApply);
    }


}
