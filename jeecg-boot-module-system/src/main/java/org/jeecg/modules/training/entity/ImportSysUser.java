package org.jeecg.modules.training.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 导入使用用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ImportSysUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工号，唯一键
     */
    @Excel(name = "工号", width = 15)
    private String workNo;

    /**
     * 姓名
     */
    @Excel(name = "姓名", width = 15)
    private String realname;

    /**
     * 出生日期
     */
    @Excel(name = "出生日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /**
     * 职务，关联职务表
     */
    @Excel(name = "现任职务", width = 15)
    private String post;

    /**
     * 部门
     */
    @Excel(name = "当前所在船舶名称", width = 15)
    private String departName;

    /**
     * 上船日期
     */
    @Excel(name = "上船日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date onboardDate;

    /**
     * 身份证号码
     */
    @Excel(name = "身份证号", width = 20)
    private String identityCard;

    /**
     * 当前动态(上船---》1：正常  下船---》2：冻结 ）
     */
    @Excel(name = "当前动态", width = 15)
    @Dict(dicCode = "user_status")
    private String status;

    /**
     * 移动电话
     */
    @Excel(name = "移动电话", width = 15)
    private String phone;

    // 绑定企业套餐id
    private Long packagesId;
}
