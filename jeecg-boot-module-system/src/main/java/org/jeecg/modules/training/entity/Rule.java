package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 规则表
 * @Author: huazhengkna
 * @Date: 2022-05-04
 * @Version: V1.0
 */
@Data
@TableName("rule")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "rule对象", description = "阅卷规则")
public class Rule {

    /**
     * 规则ID
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "规则ID")
    private Long id;
    /**
     * 规则名称
     */
    @Excel(name = "规则名称", width = 15)
    @ApiModelProperty(value = "规则名称")
    private String name;
    /**
     * 规则类型
     */
    @Excel(name = "规则类型", width = 15)
    @ApiModelProperty(value = "规则类型")
    private Integer type;
    /**
     * 规则描述
     */
    @Excel(name = "规则描述", width = 15)
    @ApiModelProperty(value = "规则描述")
    private String content;
    /**
     * 删除状态（0，正常，1已删除）
     */
    @Excel(name = "删除状态（0，正常，1已删除）", width = 15)
    @ApiModelProperty(value = "删除状态（0，正常，1已删除）")
    @TableLogic
	private Integer delFlag;
    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @Excel(name = "更新人", width = 15)
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "部门路由")
    private String depRoute;

    @ApiModelProperty(value = "所属部门路径")
    private String shareMode;

    @ApiModelProperty(value = "分享")
    private String shareId;

    /**
     * 关联行业id集合，前后都有分隔符","
     */
    private String industryIds;

    /**
     * 关联行业名称集合
     */
    @TableField(exist = false)
    private String industryNames;

}
