package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.Exam;
import org.jeecg.modules.training.vo.ExamListVO;
import org.jeecg.modules.training.vo.ExamVO;

import java.util.List;
import java.util.Map;

/**
 * @Description: 考卷
 * @Author: jeecg-boot
 * @Date: 2022-05-04
 * @Version: V1.0
 */
public interface ExamMapper extends BaseMapper<Exam> {

    /**
     * 重置结果、分数、状态。开始和结束时间
     *
     * @param id
     */
    void repeat(Long id);

    IPage<ExamVO> getAll(@Param("mp") Map<String, Object> params, Page<ExamVO> page);

    int resetUserExam(@Param("userId") String userId);


    IPage<ExamListVO> getPageList(Page<ExamListVO> page, @Param("ew") QueryWrapper<ExamListVO> queryWrapper);

    /**
     * 不分页list
     *
     * @param queryWrapper
     * @return
     */
    List<ExamListVO> getPageList(@Param("ew") QueryWrapper<ExamListVO> queryWrapper);

    IPage<ExamListVO> getSummaryList(Page<ExamListVO> page, @Param("ew") QueryWrapper<ExamListVO> queryWrapper);

    /**
     * 不分页list
     *
     * @param queryWrapper
     * @return
     */
    List<ExamListVO> getSummaryList(@Param("ew") QueryWrapper<ExamListVO> queryWrapper);

    Page<ExamListVO> getToBeScoreExam(Page<ExamListVO> page, @Param("ew") QueryWrapper<ExamListVO> queryWrapper, @Param("userId") String userId);
}
