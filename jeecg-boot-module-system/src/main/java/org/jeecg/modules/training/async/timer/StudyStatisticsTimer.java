package org.jeecg.modules.training.async.timer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.training.entity.*;
import org.jeecg.modules.training.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class StudyStatisticsTimer {

    @Resource
    private StudyTimeUserService studyTimeUserService;

    @Resource
    private IStudyLogService studyLogService;

    @Resource
    private StudyTimeDepartmentService studyTimeDepartmentService;

    @Resource
    private StudyTimeFleetService studyTimeFleetService;

    @Resource
    private IManagersService managersService;

    @Resource
    private StudyTimePerDepartmentService studyTimePerDepartmentService;

    @Resource
    private StudyTimePerFleetService studyTimePerFleetService;

    @Autowired
    private IStudyLogDailyStatisticsService studyLogDailyStatisticsService;

    @Autowired
    private TraineeService traineeService;

    //@Scheduled(cron = "0 49 * * * ?")
//    @Scheduled(initialDelay = 0, fixedRate = Long.MAX_VALUE) 注释下面的语句，修改系统时间后，可以触发该代码
    @Scheduled(cron = "0 0 1 * * ?")
    public void computerStudyTime() throws ParseException {
        log.info("开始执行凌晨1点0分0秒的任务");
        log.info("开始计算学习时间");
        //查询出昨天一整天的有效学习记录,阅读类型为阅读
        LambdaQueryWrapper<StudyLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StudyLog::getType, 0);
        queryWrapper.isNotNull(StudyLog::getEndTime);
        LocalDate yesterday = LocalDate.now().minusDays(1);
        queryWrapper.between(StudyLog::getEndTime, yesterday, LocalDate.now());

        // 生成学习资料每日学习次数统计表 StudyLogDailyStatistics
        createStudyLogDailyStatistics(yesterday, LocalDate.now());

        //查询昨天的学习数据
        List<StudyLog> studyLogs = studyLogService.list(queryWrapper);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        //将时间去除时分秒
        for (StudyLog studyLog : studyLogs) {
            Date endTime = studyLog.getEndTime();
            endTime = sdf.parse(sdf.format(endTime));
            studyLog.setEndTime(endTime);
        }

        statisticsUser(studyLogs);

        //TODO 以下方法没有改造完善，以后启用时，注意可能需要调整参数和返回值
        statisticsShipMaterial(studyLogs);
        //statisticsShipTeam(studyLogs);
    }

    // 凌晨0点0分5秒执行的任务
    @Scheduled(cron = "5 0 0 * * ?")
    public void anotherScheduledTask() {
        log.info("开始执行凌晨0点0分5秒的任务");

        log.info("开始检查会员套餐是否过期");
        traineeService.checkPackagesExpire();
    }

    // 生成学习资料每日学习次数统计表
    private void createStudyLogDailyStatistics(LocalDate yesterday, LocalDate now) {
        List<StudyLogDailyStatistics> timeAndTypeList = studyLogDailyStatisticsService.getTimeAndTypeList(0, yesterday, now);
        studyLogDailyStatisticsService.saveBatch(timeAndTypeList);
    }

    /**
     * 船员学习统计
     */
    private void statisticsUser(List<StudyLog> studyLogs) {
        Map<String, Map<String, Map<Date, Long>>> studyTimePerDays = studyLogs.stream().filter(sl -> sl.getUserId() != null && sl.getDepRoute() != null).collect(Collectors.groupingBy(StudyLog::getUserId, Collectors.groupingBy(StudyLog::getDepRoute, Collectors.groupingBy(StudyLog::getEndTime, Collectors.summingLong(StudyLog::getUseTime)))));

        //生成受试人每日学习时长表
        ArrayList<StudyTimeUser> studyTimeUsers = new ArrayList<>();
        studyTimePerDays.forEach(
                (userId, v) -> v.forEach(
                        (depRoute, f) -> f.forEach(
                                (date, studyTime) -> {
                                    StudyTimeUser studyTimeUser = new StudyTimeUser();
                                    studyTimeUser.setUserId(userId);
                                    studyTimeUser.setDate(date);
                                    studyTimeUser.setStudyTime((double) studyTime / 60);
                                    studyTimeUser.setDepartmentId(depRoute);
                                    studyTimeUsers.add(studyTimeUser);
                                }
                        )
                )
        );
        studyTimeUserService.saveBatch(studyTimeUsers);
        log.info("受试人每日学习时长表生成完成");
    }

    /**
     * 船舶学习资料统计
     */
    private void statisticsShipMaterial(List<StudyLog> studyLogs) {
        //生成船舶每日学习时长表
        Map<String, Map<Date, Long>> departmentStudyTimeMap = studyLogs.stream().filter(sl -> sl.getDepartment() != null).collect(Collectors.groupingBy(StudyLog::getDepartment, Collectors.groupingBy(StudyLog::getEndTime, Collectors.summingLong(StudyLog::getUseTime))));

        ArrayList<StudyTimeDepartment> studyTimeDepartments = new ArrayList<>();
        departmentStudyTimeMap.forEach(
                (teamId, v) -> {
                    v.forEach(
                            (date, studyTime) -> {
                                StudyTimeDepartment studyTimeDepartment = new StudyTimeDepartment();
                                studyTimeDepartment.setDepartmentId(teamId);
                                studyTimeDepartment.setDate(date);
                                studyTimeDepartment.setStudyTime((double) studyTime / 60);

                                LambdaQueryWrapper<Managers> wrapper = new LambdaQueryWrapper<>();
                                wrapper.eq(Managers::getTeamId, teamId);
                                List<Managers> list = managersService.list(wrapper);
                                if (!list.isEmpty()) {
                                    String fleet = list.get(0).getFleet();
                                    studyTimeDepartment.setFleetName(fleet);
                                }
                                studyTimeDepartments.add(studyTimeDepartment);
                            }
                    );
                }
        );
        studyTimeDepartmentService.saveBatch(studyTimeDepartments);
        log.info("生成船舶每日学习时长表完成");

        /**
         * 生成船舶学习资料学习时长表
         */
        Map<String, Map<Long, Map<Date, Long>>> countByDepartment = studyLogs.stream().filter(stpd -> stpd.getDepartment() != null).collect(Collectors.groupingBy(StudyLog::getDepartment, Collectors.groupingBy(StudyLog::getStudyId, Collectors.groupingBy(StudyLog::getEndTime, Collectors.summingLong(StudyLog::getUseTime)))));

        ArrayList<StudyTimePerDepartment> studyTimePerDepartments = new ArrayList<>();
        countByDepartment.forEach(
                (depId, v) -> {
                    v.forEach(
                            (studyId, f) -> {
                                f.forEach(
                                        (date, studyTime) -> {
                                            StudyTimePerDepartment studyTimePerDepartment = new StudyTimePerDepartment();
                                            studyTimePerDepartment.setDepartmentId(depId);
                                            studyTimePerDepartment.setStudyId(studyId.toString());
                                            studyTimePerDepartment.setStudyTime((double) studyTime / 60);
                                            studyTimePerDepartment.setDate(date);

                                            LambdaQueryWrapper<Managers> wrapper = new LambdaQueryWrapper<>();
                                            wrapper.eq(Managers::getTeamId, depId);
                                            List<Managers> list = managersService.list(wrapper);
                                            if (!list.isEmpty()) {
                                                String fleet = list.get(0).getFleet();
                                                studyTimePerDepartment.setFleetName(fleet);
                                            }
                                            studyTimePerDepartments.add(studyTimePerDepartment);
                                        }
                                );
                            }
                    );
                }
        );
        studyTimePerDepartmentService.saveBatch(studyTimePerDepartments);

        log.info("生成船舶学习资料学习时长表完成");

        //生成船队每日学习时长表
        Map<String, Map<Date, Double>> fleetStudyTimeMap = studyTimeDepartments.stream().filter(studyTimeDepartment -> studyTimeDepartment.getFleetName() != null).collect(Collectors.groupingBy(StudyTimeDepartment::getFleetName, Collectors.groupingBy(StudyTimeDepartment::getDate, Collectors.summingDouble(StudyTimeDepartment::getStudyTime))));

        fleetStudyTimeMap.forEach(
                (fleetName, v) -> v.forEach((date, studyTime) -> {
                    StudyTimeFleet studyTimeFleet = new StudyTimeFleet();
                    studyTimeFleet.setFleetName(fleetName);
                    studyTimeFleet.setDate(date);
                    studyTimeFleet.setStudyTime(studyTime);
                    studyTimeFleetService.save(studyTimeFleet);
                })
        );
        log.info("生成船舶学习资料学习时长表完成");

        //生成船队学习资料每日时长表
        Map<String, Map<String, Map<Date, Double>>> countByFleet = studyTimePerDepartments.stream().filter(stpd -> stpd.getFleetName() != null).collect(Collectors.groupingBy(StudyTimePerDepartment::getFleetName, Collectors.groupingBy(StudyTimePerDepartment::getStudyId, Collectors.groupingBy(StudyTimePerDepartment::getDate, Collectors.summingDouble(StudyTimePerDepartment::getStudyTime)))));
        countByFleet.forEach(
                (fleetName, v) -> v.forEach(
                        (studyId, f) -> f.forEach(
                                (date, studyTime) -> {
                                    StudyTimePerFleet studyTimePerFleet = new StudyTimePerFleet();
                                    studyTimePerFleet.setFleetName(fleetName);
                                    studyTimePerFleet.setStudyId(studyId);
                                    studyTimePerFleet.setStudyTime(studyTime);
                                    studyTimePerFleet.setDate(date);
                                    studyTimePerFleetService.save(studyTimePerFleet);
                                }
                        )
                )
        );
        log.info("生成船队学习资料每日时长表完成");
    }


    /**
     * 船舶船队学习统计
     */
    private void statisticsShipTeam(List<StudyLog> studyLogs) {

        //根据受试人每日学习生成船舶每日学习时长表
       /* Map<String, Double> departmentStudyTimeMap = studyTimeUsers.stream().collect(Collectors.toMap(StudyTimeUser::getDepartmentId, StudyTimeUser::getStudyTime, Double::sum));

        ArrayList<StudyTimeDepartment> studyTimeDepartments = new ArrayList<>();
        departmentStudyTimeMap.forEach(
                (k, v) -> {
                    StudyTimeDepartment studyTimeDepartment = new StudyTimeDepartment();
                    studyTimeDepartment.setDepartmentId(k);
                    studyTimeDepartment.setDate(date);

                    studyTimeDepartment.setStudyTime(v);
                    studyTimeDepartment.setWeek(week);

                    LambdaQueryWrapper<Managers> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(Managers::getDepartId, k);
                    List<Managers> list = managersService.list(wrapper);
                    if (list.size() != 0) {
                        String fleet = list.get(0).getFleet();
                        studyTimeDepartment.setFleetName(fleet);
                    }
                    studyTimeDepartments.add(studyTimeDepartment);
                }
        );
        studyTimeDepartmentService.saveBatch(studyTimeDepartments);

        //根据船队每日学习生成船队每日学习时长表
        Map<String, Double> fleetStudyTimeMap = studyTimeDepartments.stream().filter(studyTimeDepartment -> studyTimeDepartment.getFleetName() != null).collect(Collectors.toMap(StudyTimeDepartment::getFleetName, StudyTimeDepartment::getStudyTime, Double::sum));
        fleetStudyTimeMap.forEach(
                (k, v) -> {
                    StudyTimeFleet studyTimeFleet = new StudyTimeFleet();
                    studyTimeFleet.setFleetName(k);
                    studyTimeFleet.setDate(date);
                    studyTimeFleet.setStudyTime(v);
                    studyTimeFleet.setWeek(week);
                    studyTimeFleetService.save(studyTimeFleet);
                }
        );*/

    }
}
