<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.StudyTimePerFleetMapper">
  <select id="getPageList" resultType="org.jeecg.modules.training.vo.StudyTimePerFleetVO">
    select * from (
    select stpf.fleet_name,
    s.name as study_name,
    sum(study_time) as study_time
    from (select fleet
    from managers
    where dep_route like concat(#{depRoute},'%')
    group by fleet) f
    left join study_time_per_fleet stpf on f.fleet = stpf.fleet_name
    left join study s on stpf.study_id = s.id
    where 1 = 1
    <if test="scope == 'week'">
      and WEEK(stpf.date, 1) = WEEK(now(), 1)
      and YEAR(stpf.date) = YEAR(CURDATE())
    </if>
    <if test="scope == 'month'">
      and MONTH(stpf.date) = MONTH(NOW())
      and YEAR(stpf.date) = YEAR(CURDATE())
    </if>
    group by stpf.fleet_name, stpf.study_id
    ) as t
    ${ew.customSqlSegment}
  </select>
</mapper>

