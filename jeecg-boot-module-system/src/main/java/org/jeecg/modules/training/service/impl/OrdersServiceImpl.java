package org.jeecg.modules.training.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.training.entity.Orders;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.mapper.OrdersMapper;
import org.jeecg.modules.training.service.IOrdersService;
import org.jeecg.modules.training.service.TraineeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Slf4j
@Service
public class OrdersServiceImpl extends ServiceImpl<OrdersMapper, Orders> implements IOrdersService {

    @Autowired
    private OrdersMapper ordersMapper;

    @Autowired
    private TraineeService traineeService;


    // 开通会员
    @Override
    public void openMember(Orders orders) {

        // 获取指定船员id
        Trainee trainee = traineeService.getById(orders.getUserId());
        if (trainee == null) {
            log.error("开通会员失败！orders：id={}关联的用户id{}不存在", orders.getUserId(), orders.getUserId());
            throw new JeecgBootException("开通会员失败！用户不存在");
        }

        // 修改状态
        update(new LambdaUpdateWrapper<Orders>()
                .set(Orders::getStatus, 1)
                .set(Orders::getPayTime, DateUtil.date())
                .eq(Orders::getId, orders.getId()));

        traineeService.openMember(trainee, orders.getId());

        log.info("trainee：id={}，name= {}开通会员成功", trainee.getId(), trainee.getRealname());
    }

    /**
     *
     * @param loginUser 创建人
     * @param outTradeNo 订单号
     * @param amount 订单金额
     * @param payType 支付类型
     * @param productCategorie 商品类型 0:套餐升级, 1:套餐续费, 2:单独购买考试, 3:单独购买学习资料
     * @param productDetail 商品详情 productCategorie=0 or 1，此处返回套餐lev:( 1:基础版, 2:标准版, 3: 尊享版)；productCategorie=2 or 3，此处返回资料id:(paper_id or study_id)
     * @param productDescription 商品描述
     * @return 订单对象
     */
    @Override
    public Orders initOrders(LoginUser loginUser, String outTradeNo, BigDecimal amount, Integer payType,
                             Integer productCategorie, Long productDetail, String productDescription) {
        Orders orders = new Orders();

        orders.setName(loginUser.getRealname());
        orders.setUserId(loginUser.getId());
        orders.setOrdersNum(outTradeNo);
        orders.setPayType(payType);
        orders.setAmount(amount);
        orders.setStatus(0); // 订单状态 0 未支付，1 已支付，2 已过期
        orders.setDepRoute(loginUser.getDepRoute());
        orders.setProductCategorie(productCategorie);
        orders.setProductDetail(productDetail);
        orders.setProductDescription(productDescription);

        this.save(orders);

        return orders;
    }

    /**
     * app可开发票订单展示页
     * @param
     * @param
     * @return Page<Orders>
     */
    @Override
    public Page<Orders> invoiceOrderList(Page<Orders> page, QueryWrapper<Orders> queryWrapper) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }
        queryWrapper.and(item -> item.eq("user_id", loginUser.getId())).eq("status", 1);
        return ordersMapper.invoiceOrderList(page, queryWrapper);
    }

}
