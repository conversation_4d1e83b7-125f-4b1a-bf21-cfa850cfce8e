package org.jeecg.modules.training.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.training.entity.Category;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.service.*;
import org.jeecg.modules.training.util.ExamUtils;
import org.jeecg.modules.training.vo.CategoryVO;
import org.jeecg.modules.training.vo.PaperVO;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 分类
 * @Author: huazhengkan
 * @Date: 2022-05-04
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "分类")
@RestController
@RequestMapping("/app/category")
public class AppCategoryController extends JeecgController<Category, ICategoryService> {
    @Autowired
    private ICategoryService categoryService;

    @Autowired
    private TraineeService traineeService;

    @Autowired
    private IStudyService studyService;

    @Autowired
    private IStudyLogService studyLogService;

    @Autowired
    private IUserPaperService userPaperService;


    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "分类-通过id查询")
    @ApiOperation(value = "分类-通过id查询", notes = "分类-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {

        Category category = categoryService.getById(id);
        CategoryVO categoryVo = new CategoryVO();
        BeanUtils.copyProperties(category, categoryVo);

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        LambdaQueryWrapper<Category> queryChildren = new LambdaQueryWrapper<Category>()
                .eq(Category::getParentId, category.getId())
                .eq(Category::getDelFlag, CommonConstant.DEL_FLAG_0)
                .and(q -> q.likeRight(Category::getDepRoute, loginUser.getDepRoute()));

        categoryVo.setHasChildren(categoryService.count(queryChildren) > 0);

        return Result.OK(categoryVo);
    }

    /**
     * 获取所有分类
     * huazhengkan
     *
     * @return
     */
    @ApiOperation("获取所有分类接口")
    @RequestMapping(value = "/listByType", method = RequestMethod.GET)
    public Result<List<CategoryVO>> queryByType(@RequestParam(name = "id", required = false) String id,
                                                @RequestParam(name = "type", required = false) String type,
                                                @RequestParam(name = "fill", required = false, defaultValue = "false") Boolean fill) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        Result<List<CategoryVO>> result = new Result<>();
        LambdaQueryWrapper<Category> query = new LambdaQueryWrapper<Category>();

        log.info("queryByType user dep_route:{}", loginUser.getDepRoute());

        query.eq(Category::getDepRoute, loginUser.getDepRoute());

        query.isNull(Category::getParentId);

        if (oConvertUtils.isNotEmpty(id)) {
            Object[] arr = id.split(",");
            query.in(Category::getId, arr);
        }

        if (oConvertUtils.isNotEmpty(type)) {
            if (type.contains(",")) {
                Object[] vals = Arrays.stream(type.split(",")).map(Integer::decode).toArray();
                query.in(Category::getType, vals);
            } else {
                query.eq(Category::getType, type);
            }
        }

        query.eq(Category::getDelFlag, CommonConstant.DEL_FLAG_0);
        query.orderByAsc(Category::getShowOrder);
        List<Category> categoryList = categoryService.list(query);

        if (categoryList == null || categoryList.size() <= 0) {
            result.error500("未找到分类");
        } else {
            String userId = loginUser.getId();
            Trainee trainee = traineeService.getUserByIDCard(loginUser.getUsername());

            //上船时间
            String boardTime = ExamUtils.getUserBoardTimeName(trainee);

            //职务
            String post = "," + trainee.getPost() + ",";

            //组
            String groupInfo = trainee.getGroupInfo();
            if (StringUtils.isNotEmpty(groupInfo)) {
                groupInfo = "," + groupInfo + ",";
            }

            List<CategoryVO> list = new ArrayList<>();
            for (Category category : categoryList) {
                CategoryVO vo = fillCategoryVO(category, userId, post, groupInfo, boardTime, fill);
                list.add(vo);
            }

            result.setResult(list);
            result.setSuccess(true);
            result.setCode(200);
        }
        return result;
    }

    /**
     * 获取所有分类
     * huazhengkan
     *
     * @return
     */
    @ApiOperation("获取所有分类接口")
    @RequestMapping(value = "/listByMenu", method = RequestMethod.GET)
    public Result<List<CategoryVO>> queryByMenu(@RequestParam(name = "menu") String menu,
                                                @RequestParam(name = "fill", required = false) Boolean fill) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        log.info("queryByMenu user dep_route:{}", loginUser.getDepRoute());

        Result<List<CategoryVO>> result = new Result<>();
        LambdaQueryWrapper<Category> query = new LambdaQueryWrapper<>();

        query.eq(Category::getDepRoute, loginUser.getDepRoute());

        query.isNull(Category::getParentId);

        if (menu.contains(",")) {
            Object[] vals = Arrays.stream(menu.split(",")).map(Integer::decode).toArray();
            query.in(Category::getAppMenu, vals);
        } else {
            query.eq(Category::getAppMenu, menu);
        }

        query.orderByAsc(Category::getShowOrder);
        List<Category> categoryList = categoryService.list(query);

        if (categoryList == null || categoryList.size() <= 0) {
            result.error500("未找到分类");
        } else {
            String userId = loginUser.getId();
            Trainee trainee = traineeService.getUserByIDCard(loginUser.getUsername());

            //上船时间
            String boardTime = ExamUtils.getUserBoardTimeName(trainee);

            //职务
            String post = "," + trainee.getPost() + ",";

            //组
            String groupInfo = trainee.getGroupInfo();
            if (StringUtils.isNotEmpty(groupInfo)) {
                groupInfo = "," + groupInfo + ",";
            }

            List<CategoryVO> list = new ArrayList<>();
            for (Category category : categoryList) {
                CategoryVO vo = fillCategoryVO(category, userId, post, groupInfo, boardTime, fill);
                list.add(vo);
            }

            result.setResult(list);
            result.setSuccess(true);
            result.setCode(200);
        }
        return result;
    }

    /**
     * 根据父分类获取子分类
     * huazhengkan
     *
     * @return
     */
    @ApiOperation("根据父分类获取子分类")
    @RequestMapping(value = "/childList", method = RequestMethod.GET)
    public Result<List<CategoryVO>> queryChildList(@RequestParam(name = "parentId", required = false) String parentId,
                                                   @RequestParam(name = "fill", required = false) Boolean fill) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        Result<List<CategoryVO>> result = new Result<>();
        LambdaQueryWrapper<Category> query = new LambdaQueryWrapper<>();

        query.and(q -> q.eq(Category::getDepRoute, loginUser.getDepRoute()));

        query.eq(Category::getParentId, parentId);
        query.eq(Category::getDelFlag, CommonConstant.DEL_FLAG_0);
        query.orderByAsc(Category::getShowOrder);
        List<Category> categoryList = categoryService.list(query);

        if (categoryList == null || categoryList.size() <= 0) {
            result.error500("未找到子分类");
        } else {

            String userId = loginUser.getId();
            Trainee trainee = traineeService.getUserByIDCard(loginUser.getUsername());

            //上船时间
            String boardTime = ExamUtils.getUserBoardTimeName(trainee);

            //职务
            String post = "," + trainee.getPost() + ",";

            //组
            String groupInfo = trainee.getGroupInfo();
            if (StringUtils.isNotEmpty(groupInfo)) {
                groupInfo = "," + groupInfo + ",";
            }

            List<CategoryVO> list = new ArrayList<>();
            for (Category category : categoryList) {
                CategoryVO vo = fillCategoryVO(category, userId, post, groupInfo, boardTime, fill);
                list.add(vo);
            }

            result.setResult(list);
            result.setSuccess(true);
            result.setCode(200);
        }
        return result;
    }


    @NotNull
    private CategoryVO fillCategoryVO(Category category, String userId, String post, String groupInfo, String boardTime, Boolean fill) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        int type = category.getType();

        CategoryVO vo = new CategoryVO();

        BeanUtils.copyProperties(category, vo);

        LambdaQueryWrapper<Category> queryChildren = new LambdaQueryWrapper<Category>()
                .eq(Category::getParentId, category.getId())
                .eq(Category::getDelFlag, CommonConstant.DEL_FLAG_0)
                .likeRight(Category::getDepRoute, loginUser.getDepRoute());

        //TODO !!!!!! 此处可能会有性能问题，category 数据量比较大时，会有点慢，可以通过 增加ParentId索引提高性能
        // 通常子分类不会很多，初步预计10-20个就很多了。
        // 暂时忽略此处性能问题
        vo.setHasChildren(categoryService.count(queryChildren) > 0);

        if (fill != null && !fill) {
            return vo;
        }

        //0:考试;1:检测类;2:许可证类;3:学习
        switch (type) {
            case 0:  //考试

                //查询当前用户的每个分类是否都考试过,考试的
                List<PaperVO> papers = userPaperService.findPapers(userId, category.getId());
                int remainderExams = 0, numberExams = 0, notPassed = 0;
                if (!papers.isEmpty()) {

                    // 只统计 考试 并且 应考次数 大于 0 的
                    // 练习和可以无限次考试的，无需统计
                    for (PaperVO p : papers) {

                        if (p.getType() == 2 && p.getTotalTimes() > 0) {

                            fixPaperNumbers(p);

                            numberExams += p.getTotalTimes();

                            int allTimes = p.getTotalTimes() * (p.getRetryTimes() + 1);
                            int usedTimes = p.getPassedTimes() * (p.getRetryTimes() + 1) + p.getFailedTimes();

                            if (allTimes > usedTimes) {
                                //未完成数量
                                remainderExams += p.getTotalTimes() - p.getPassedTimes();
                            } else {
                                //应该通过但未通过的次数
                                //这里不要用 p.failedTimes，这个是总的失败次数
                                notPassed += p.getTotalTimes() - p.getPassedTimes();
                            }
                        }
                    }
                }

                vo.setRemainderExams(remainderExams);
                vo.setNumberExams(numberExams);
                vo.setNoPassed(notPassed);
                break;
            case 1://检测类
                break;
            case 2://许可证
                break;
            case 3://学习
                //查询当前用户在当前分类中未阅读和未关注的数量

                //先查询用户应该阅读的数量
                Long needReadNumber = studyService.getNeedReadNumber(userId, boardTime, post, groupInfo, category);
                //再查询用户已经阅读的数量
                Long alreadyReadNumber = studyLogService.getAlreadyReadNumber(userId, boardTime, post, groupInfo, category);
                vo.setUnread((int) Math.max(0, needReadNumber - alreadyReadNumber));

//                //查询用户需要关注的数量
//                Long needAttentionNumber = studyService.getNeedAttentionNumber(userId, boardTime, post, groupInfo, category);
//                //查询用户已经关注的数量
//                Long alreadyAttentionNumber = studyLogService.getAlreadyAttentionNumber(userId, boardTime, post, groupInfo, category);
//                vo.setNotFollowed((int) Math.max(0, needAttentionNumber - alreadyAttentionNumber));
                break;
        }

        return vo;
    }

    private void fixPaperNumbers(PaperVO p) {
        if (p.getRetryTimes() == null)
            p.setRetryTimes(0);
        if (p.getPassedTimes() == null)
            p.setPassedTimes(0);
        if (p.getDoneTimes() == null)
            p.setDoneTimes(0);
        if (p.getFailedTimes() == null)
            p.setFailedTimes(0);
    }


}
