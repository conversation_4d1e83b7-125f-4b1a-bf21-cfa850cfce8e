package org.jeecg.modules.training.util;

import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.training.entity.Category;
import org.jeecg.modules.training.vo.CategoryIdModel;
import org.jeecg.modules.training.vo.CategoryTreeVO;

import java.util.ArrayList;
import java.util.List;

/**
 * 对应分类的表,处理并查找树级数据
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年05月05日 15:55
 */
public class FindsCategoryChildrenUtil {

    /**
     * queryTreeList的子方法 ====1=====
     * 该方法是将ExamCategory类型的list集合转换成CategoryTreeVO类型的集合
     */
    public static List<CategoryTreeVO> wrapTreeDataToTreeList(List<Category> recordList) {
        List<CategoryIdModel> idList = new ArrayList<CategoryIdModel>();
        List<CategoryTreeVO> records = new ArrayList<>();
        for (int i = 0; i < recordList.size(); i++) {
            Category category = recordList.get(i);
            records.add(new CategoryTreeVO(category));
        }
        List<CategoryTreeVO> tree = findChildren(records, idList);
        setEmptyChildrenAsNull(tree);
        return tree;

    }

    /**
     * queryTreeList的子方法 ====2=====
     * 该方法是找到并封装顶级父类的节点到TreeList集合
     */
    private static List<CategoryTreeVO> findChildren(List<CategoryTreeVO> recordList,
                                                     List<CategoryIdModel> catagorytIdlList) {

        List<CategoryTreeVO> treeList = new ArrayList<>();
        for (int i = 0; i < recordList.size(); i++) {
            CategoryTreeVO branch = recordList.get(i);
            if (oConvertUtils.isEmpty(branch.getParentId())) {
                treeList.add(branch);
                CategoryIdModel categoryIdModel = new CategoryIdModel().convert(branch);
                catagorytIdlList.add(categoryIdModel);
            }
        }
        getGrandChildren(treeList, recordList, catagorytIdlList);

        return treeList;
    }

    /**
     * queryTreeList的子方法====3====
     * 该方法是找到顶级父类下的所有子节点集合并封装到TreeList集合
     */
    private static void getGrandChildren(List<CategoryTreeVO> treeList, List<CategoryTreeVO> recordList, List<CategoryIdModel> idList) {
        for (int i = 0; i < treeList.size(); i++) {
            CategoryTreeVO model = treeList.get(i);
            CategoryIdModel idModel = idList.get(i);
            for (int i1 = 0; i1 < recordList.size(); i1++) {
                CategoryTreeVO m = recordList.get(i1);
                if (m.getParentId() != null && m.getParentId().equals(model.getId())) {
                    model.getChildren().add(m);
                    CategoryIdModel dim = new CategoryIdModel().convert(m);
                    idModel.getChildren().add(dim);
                }
            }
            getGrandChildren(treeList.get(i).getChildren(), recordList, idList.get(i).getChildren());
        }

    }

    /**
     * queryTreeList的子方法 ====4====
     * 该方法是将子节点为空的List集合设置为Null值
     */
    private static void setEmptyChildrenAsNull(List<CategoryTreeVO> treeList) {

        for (int i = 0; i < treeList.size(); i++) {
            CategoryTreeVO model = treeList.get(i);
            if (model.getChildren().size() == 0) {
                model.setChildren(null);
//                model.setIsLeaf(true);
            } else {
                setEmptyChildrenAsNull(model.getChildren());
//                model.setIsLeaf(false);
            }
        }
    }

}
