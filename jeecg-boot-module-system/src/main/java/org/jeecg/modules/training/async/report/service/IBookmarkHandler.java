package org.jeecg.modules.training.async.report.service;

import com.aspose.words.Bookmark;
import com.aspose.words.Document;
import org.jeecg.modules.training.async.report.model.DetailExamBaseVO;

/**
 * 书签处理器接口
 * 定义了处理Word文档中书签的标准方法
 */
public interface IBookmarkHandler {
    /**
     * 处理指定的书签
     *
     * @param bookmark      当前书签
     * @param detail        考试详情数据（使用基类以支持所有类型的考试详情）
     * @param document      当前文档对象
     * @throws Exception    当处理过程中发生错误时抛出
     */
    void process(Bookmark bookmark, DetailExamBaseVO detail, Document document) throws Exception;
} 