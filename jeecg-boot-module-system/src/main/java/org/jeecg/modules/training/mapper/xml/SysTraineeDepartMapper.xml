<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.SysTraineeDepartMapper">

    <select id="listTraineeIdByPackagesId" resultType="java.lang.String">
        select std.trainee_id
        from sys_trainee_depart std
                 join trainee on std.trainee_id = trainee.id
        where std.packages_id = #{packagesId}
          and trainee.del_flag = 0
          and trainee.member_status = 1;
    </select>

    <!--    获取企业会员公司没有开会员的船员-->
    <select id="getCompanyNoMember" resultType="org.jeecg.modules.training.entity.SysTraineeDepart">
        select *
        from sys_trainee_depart std
                 join trainee on std.trainee_id = trainee.id
        where trainee.del_flag = 0
          and std.packages_id is null
          and std.dep_id = #{sysDepartId}
    </select>

    <select id="getPackagesIdByTraineeIdAndDepIdLong" resultType="java.lang.Long">
        select
            packages_id
        from sys_trainee_depart
        where trainee_id = #{traineeId}
          and dep_id = #{depId}
    </select>
</mapper>