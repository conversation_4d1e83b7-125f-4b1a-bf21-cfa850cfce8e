package org.jeecg.modules.training.controller.miniProgram;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.training.async.facial.FacialHandlerService;
import org.jeecg.modules.training.async.phase.PushPhaseService;
import org.jeecg.modules.training.entity.Exam;
import org.jeecg.modules.training.entity.Paper;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.entity.UserPaper;
import org.jeecg.modules.training.service.IExamService;
import org.jeecg.modules.training.service.IPaperService;
import org.jeecg.modules.training.service.IUserPaperService;
import org.jeecg.modules.training.service.TraineeService;
import org.jeecg.modules.training.vo.ExamVO;
import org.jeecg.modules.training.vo.UserPaperVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * MiniProgramExamController类用于处理小程序考试相关的请求。
 */
@Slf4j
@RestController
@RequestMapping("/miniProgram/exam")
public class MiniProgramExamController extends JeecgController<Exam, IExamService> {

    @Autowired
    private TraineeService traineeService;
    @Resource
    private PushPhaseService userPhaseHandleService;

    @Autowired
    private IUserPaperService userPaperService;

    @Autowired
    private IPaperService paperService;

    @Resource
    private FacialHandlerService faciallHandlerService;

    @Value("${WECHAT.faceVideoPath}")
    private String parentPath;

    /**
     * 获取考试详情
     *
     * @param id 考试的唯一标识符
     * @return 返回考试详情，如果出错返回错误信息
     */
    @GetMapping("/getExamDetails")
    public Result<?> getExamDetails(@RequestParam(value = "id") Long id) {
        try {
            // 通过考试ID查询考试详情
            ExamVO ExamVO = service.examDetail(id);
            // 返回成功的考试详情
            return Result.ok(ExamVO);
        } catch (Exception e) {
            log.error("获取考试详情异常", e);
            // 出现异常，返回错误信息
            return Result.error(e.getMessage());
        }
    }

    //获取历史考卷信息
    @GetMapping("/historyList")
    public Result<?> getHistoryExam(@RequestParam(name = "pageNo", required = false, defaultValue = "1") Integer pageNo,
                                    @RequestParam(name = "pageSize", required = false, defaultValue = "10") Integer pageSize) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        LambdaQueryWrapper<Exam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Exam::getId);
        queryWrapper.eq(Exam::getDelFlag, CommonConstant.DEL_FLAG_0);
        queryWrapper.eq(Exam::getUserId, loginUser.getId());
        queryWrapper.eq(Exam::getState, 2);
        queryWrapper.ne(Exam::getResult, CommonConstant.EXAM_RESULT_NO_SCORE);
        queryWrapper.orderByDesc(Exam::getCreateTime);
        queryWrapper.inSql(Exam::getPaperId, "select id from paper where type=2");

        Page<Exam> page = new Page<>(pageNo, pageSize);
        IPage<Exam> pageList = service.page(page, queryWrapper);

        List<Exam> list = pageList.getRecords();
        List<ExamVO> result = new ArrayList<>();
        for (Exam e : list) {
            try {
                result.add(service.examDetail(e.getId()));
            } catch (Exception exception) {
                exception.printStackTrace();
            }
        }

        return Result.OK(result);
    }

    //获取用户待考试卷
    @GetMapping(value = "/getExam")
    public Result<?> getForPaper(@RequestParam(name = "userPaperId", required = false, defaultValue = "1") Long userPaperId,
                                 @RequestParam(name = "pageNo", required = false, defaultValue = "1") Integer pageNo,
                                 @RequestParam(name = "pageSize", required = false, defaultValue = "10") Integer pageSize) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }


        LambdaQueryWrapper<Exam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Exam::getDelFlag, CommonConstant.DEL_FLAG_0);
        queryWrapper.eq(Exam::getUserId, loginUser.getId());
        queryWrapper.eq(Exam::getState, 1);
        queryWrapper.eq(Exam::getUserPaperId, userPaperId);
        queryWrapper.orderByDesc(Exam::getCreateTime);
        queryWrapper.inSql(Exam::getPaperId, "select id from paper where type=2");

        Page<Exam> page = new Page<>(pageNo, pageSize);
        IPage<Exam> pageList = service.page(page, queryWrapper);

        List<Exam> list = pageList.getRecords();

        return Result.OK(list);
    }


    /**
     * 获取考试详情
     *
     * @param id 考试的唯一标识符
     * @return 返回考试详情的结果对象，如果成功则包含考试详情，如果失败则包含错误信息
     */
    @GetMapping(value = "/examDetail")
    public Result<?> examDetail(@RequestParam(name = "id") Long id) {
        try {
            // 通过考试ID获取考试详情
            ExamVO examVO = service.examDetail(id);
            return Result.ok(examVO);
        } catch (Exception e) {
            log.error("获取考试详情异常", e);
            // 捕获异常并返回错误信息
            return Result.error(e.getMessage());
        }
    }


    /**
     * 创建考试
     *
     * @param paperForReceive 包含试卷信息的请求体，用于接收客户端发送的试卷相关参数
     * @return Result<ExamVO> 包含考试详情的结果对象，如果创建成功，则考试详情为Result的对象；如果创建失败，则返回错误信息。
     */
    @PostMapping(value = "/create")
    public Result<ExamVO> create(@RequestBody Paper paperForReceive) {

        // 验证用户登录状态
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        // 解析客户端发送的试卷参数
        Long paperId = paperForReceive.getId();
        Long categoryId = paperForReceive.getCategoryId();

        // 根据试卷ID查询试卷信息
        Paper paper = paperService.getById(paperId);
        Long userPaperId;
        LambdaQueryWrapper<UserPaper> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserPaper::getPaperId, paperId)
                .eq(UserPaper::getUserId, loginUser.getId())
                .eq(UserPaper::getDelFlag, 0);

        // 绑定用户和试卷
        // 判定是否存在用户试卷关系
        UserPaper userPaper = userPaperService.getOne(lambdaQueryWrapper.in(UserPaper::getStatus, Arrays.asList(0, 1)));
        if (userPaper == null) {
            UserPaperVO userPaperVO = new UserPaperVO();
            userPaperVO.setPaperId(paperId);
            userPaperVO.setUserIds(loginUser.getId());
            userPaperVO.setPaperName(paper.getName());
            userPaperVO.setDepRoute(paper.getDepRoute());
            userPaperVO.setTotalTimes(paper.getNumExam());
            userPaperVO.setRetryTimes(paper.getNumRetries());
            userPaperVO.setType(paper.getAutoPush() == 1 ? 0 : 1);
            userPaperService.add(userPaperVO);
            userPaperId = userPaperService.getOne(lambdaQueryWrapper.eq(UserPaper::getStatus, 0)).getId();
        } else {
            userPaperId = userPaper.getId();
        }

        // 尝试创建考试
        try {
            if (paperId == null || paperId.equals(0L)) {
                paperId = null;
            }
            // 调用服务层创建考试并获取考试ID
            Long id = service.createExam(categoryId, userPaperId, paperId);
            // 根据考试ID查询考试详情
            ExamVO ExamVO = service.examDetail(id);

            // 返回成功结果，包含考试详情
            return Result.ok(ExamVO);
        } catch (JeecgBootException e) {
            log.error("创建考试异常", e);
            // 捕获自定义异常，返回带有错误信息的结果
            return Result.error(CommonConstant.SC_OK_200, e.getMessage());

        }
    }

    //提交考卷
    @PutMapping(value = "/submit")
    public Result<ExamVO> submit(@RequestBody ExamVO vo) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        try {
            service.submit(vo, false);
            ExamVO existExam = service.examDetail(vo.getId());

            Trainee sysUser = traineeService.getById(loginUser.getId());
            userPhaseHandleService.checkTrainee(sysUser, 1, existExam.getPaperId());

            return Result.ok(vo);
        } catch (JeecgBootException ex) {
            log.error("提交考试异常", ex);
            return Result.error("提交失败");
        } catch (Exception e) {
            log.error("提交考试异常", e);
            return Result.error("服务器忙，请稍后再试");
        }

    }


    @PostMapping("uploadVideos")
    public Result uploadVideos(@RequestParam String id, MultipartFile[] files) throws IOException {
        Exam exam = service.getById(id);
        String path = null;
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        String yyyyMM = format.format(new Date());
        log.info("文件数量：{}", files.length);
        for (MultipartFile file : files) {
            UUID uuid = UUID.randomUUID();
            String absPath = parentPath + yyyyMM + File.separator + uuid + ".mp4";
            File file1 = new File(absPath);
            File parentFile = file1.getParentFile();
            if (!parentFile.isDirectory()) {
                parentFile.mkdir();
            }
            file.transferTo(file1);
            path = yyyyMM + File.separator + uuid + ".mp4";
        }
        exam.setVideoPaths((exam.getVideoPaths() == null ? "" : exam.getVideoPaths()) + path + ",");

        service.updateById(exam);

        return Result.ok("视频上传成功");
    }

    @GetMapping("uploadComplete")
    public Result uploadComplete(@RequestParam String id) {
        Exam exam = service.getById(id);
        //提交人像处理
        faciallHandlerService.beginHandleFacial(exam);
        return Result.ok("视频上传完毕确认成功");
    }
}
