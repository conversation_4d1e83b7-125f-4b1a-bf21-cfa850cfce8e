package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 用户阶段（自动推送+手动赋值）
 *
 * @TableName user_phase
 */
@TableName(value = "user_phase")
public class UserPhase {

    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    private String userId;

    /**
     *
     */
    private Long phaseId;

    private Integer level;
    /**
     * 0 未到达，1 当前阶段，2 已完成
     */
    private Integer status;
    /**
     *
     */
    private Integer todo;
    /**
     * 是否是当前阶段 0 否，1 是
     */
    private Integer isCurrent;
    /**
     *
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *
     */
    private String createBy;

    /**
     *
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     *
     */
    private String updateBy;

    /**
     *
     */
    private String depRoute;

    @TableField(exist = false)
    private String icon;

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    /**
     *
     */
    public Long getId() {
        return id;
    }

    /**
     *
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     *
     */
    public Long getPhaseId() {
        return phaseId;
    }

    /**
     *
     */
    public void setPhaseId(Long phaseId) {
        this.phaseId = phaseId;
    }

    /**
     *
     */
    public Integer getStatus() {
        return status;
    }

    /**
     *
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     *
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     *
     */
    public String getCreateBy() {
        return createBy;
    }

    /**
     *
     */
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     *
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     *
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getTodo() {
        return todo;
    }

    public void setTodo(Integer todo) {
        this.todo = todo;
    }

    private transient String name;
    private transient Integer autoPush;

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getLevel() {
        return level;
    }

    public Integer getAutoPush() {
        return autoPush;
    }

    public void setAutoPush(Integer autoPush) {
        this.autoPush = autoPush;
    }

    public String getDepRoute() {
        return depRoute;
    }

    public void setDepRoute(String depRoute) {
        this.depRoute = depRoute;
    }

    public Integer getIsCurrent() {
        return isCurrent;
    }

    public void setIsCurrent(Integer isCurrent) {
        this.isCurrent = isCurrent;
    }
}