package org.jeecg.modules.training.async.report.service.impl;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.io.File;
import java.io.IOException;
import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.Collections;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.jeecg.modules.training.async.report.model.DetailBaseVO;
import org.jeecg.modules.training.async.report.model.DetailExamScl90VO;
import org.apache.commons.lang3.reflect.FieldUtils;

import com.aspose.words.Bookmark;
import com.aspose.words.BookmarkCollection;
import com.aspose.words.Document;
import com.aspose.words.PdfSaveOptions;
import org.jeecg.modules.training.async.report.service.IBookmarkHandler;
import org.jeecg.modules.training.async.report.service.IReportGenerator;
import org.jeecg.modules.training.async.report.service.IFieldValueFormatter;
import org.jeecg.modules.training.mapper.ExamQuestionMapper;
import org.jeecg.modules.training.util.AsposeWordsInitializer;
import org.jeecg.modules.training.vo.ExamReportFlattenedVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jeecg.modules.training.async.report.model.*;
import org.jeecg.modules.training.util.SegmentedBarChartImageGenerator;

/**
 * SCL90心理测试报告生成器
 * 使用Aspose.Words生成包含文本、表格、图表和图片的心理测试报告
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service("scl90ExamReportGenerator") // 指定 Bean 名称为 "scl90ExamReportGenerator"
public class Scl90Generator implements IReportGenerator {

    // 在类的开头定义常量
    private static final int EXPECTED_QUESTION_COUNT = 90; // SCL-90量表应该包含90个题目
    private static final int EXPECTED_FACTOR_COUNT = 10;   // SCL-90量表应该包含10个因子
    private static final boolean SAVE_WORD_FOR_DEBUG = false;  // 调试开关：是否同时保存Word格式

    // 定义并注入通用的字段格式化器
    @Autowired
    @Qualifier("defaultFormatter") // 注入名为 "defaultFormatter" 的 IFieldValueFormatter 实现
    private IFieldValueFormatter FIELD_VALUE_FORMATTER;

    // BOOKMARK_HANDLERS 映射, 根据书签名称绑定不同的处理器
    private final Map<String, IBookmarkHandler> bookmarkHandlers = new HashMap<>();

    // 评语配置文件的文件名
    private static final String COMMENT_DEFINITIONS_FILE_NAME = "scl90_comment_definitions.json";

    @Autowired
    public Scl90Generator(@Qualifier("defaultFormatter") IFieldValueFormatter fieldValueFormatter) {
        // 初始化 Aspose.Words
        AsposeWordsInitializer.getInstance().initialize();

        // 文本书签 (同名文本书签可省略在此处定义)
        bookmarkHandlers.put("candidateName2", new TextBookmarkHandler(
            detail -> ((DetailExamScl90VO)detail).getCandidateName(),
            fieldValueFormatter
        ));
        bookmarkHandlers.put("totalAverageScore2", new TextBookmarkHandler(
                detail -> ((DetailExamScl90VO)detail).getTotalAverageScore(),
                fieldValueFormatter
        ));
        bookmarkHandlers.put("totalAverageScore3", new TextBookmarkHandler(
                detail -> ((DetailExamScl90VO)detail).getTotalAverageScore(),
                fieldValueFormatter
        ));
        bookmarkHandlers.put("overallStatus2", new TextBookmarkHandler(
                detail -> ((DetailExamScl90VO)detail).getOverallStatus(),
                fieldValueFormatter
        ));

        // 总体评估指标相关书签
        bookmarkHandlers.put("totalAverageScore", new TextBookmarkHandler(
            detail -> ((DetailExamScl90VO)detail).getTotalAverageScore(),
            fieldValueFormatter
        ));
        bookmarkHandlers.put("abnormalFactorsCnt", new TextBookmarkHandler(
            detail -> ((DetailExamScl90VO)detail).getAbnormalFactorsCnt(),
            fieldValueFormatter
        ));
        bookmarkHandlers.put("overallStatus", new TextBookmarkHandler(
            detail -> ((DetailExamScl90VO)detail).getOverallStatus(),
            fieldValueFormatter
        ));
        bookmarkHandlers.put("overallEvaluation", new TextBookmarkHandler(
            detail -> ((DetailExamScl90VO)detail).getOverallEvaluation(),
            fieldValueFormatter
        ));

        // 各因子相关书签（A-J）
        String[] factors = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J"};
        for (String factor : factors) {
            // 因子水平分级条形图
            bookmarkHandlers.put("horizontalRankingBarChart_" + factor, new ImageBookmarkHandler(
                detail -> getFieldValue((DetailExamScl90VO)detail, "horizontalRankingBarChart_" + factor)
            ));
        }

        // 企业公章图片书签
        bookmarkHandlers.put("companySeal", new ImageBookmarkHandler(
                detail -> ((DetailExamScl90VO)detail).getCompanySeal()
        ));

        // 图表书签
        bookmarkHandlers.put("radarChart", new ChartBookmarkHandler(
            detail -> ((DetailExamScl90VO)detail).getRadarChartData()
        ));

        bookmarkHandlers.put("lineChart", new ChartBookmarkHandler(
            detail -> ((DetailExamScl90VO)detail).getLineChartData()
        ));
    }

    /**
     * 将double类型数值向下取整到1位小数
     * @param value 需要处理的数值
     * @return 处理后的数值
     */
    private double floorToOneDecimal(double value) {
        return Math.floor(value * 10.0) / 10.0;
    }

    /**
     * 加载评语定义配置文件
     * @param templateName 模板名称
     * @return Scl90CommentDefinitions 评语定义对象
     */
    private Scl90CommentDefinitions loadCommentDefinitions(String templateName) {
        ObjectMapper objectMapper = new ObjectMapper();
        // 使用 FileUtils.getFile() 自动处理跨平台路径分隔符
        // 注意:getFile()不会检查文件是否存在,仅构造路径字符串
        File configFile = FileUtils.getFile(dataRoot, TEMPLATE_BASE_DIR, templateName, COMMENT_DEFINITIONS_FILE_NAME);
        
        if (!configFile.exists()) {
            throw new RuntimeException("SCL-90评语定义文件不存在: " + configFile.getPath());
        }
        
        try {
            return objectMapper.readValue(configFile, Scl90CommentDefinitions.class);
        } catch (IOException e) {
            throw new RuntimeException("加载SCL-90评语定义文件失败: " + configFile.getPath(), e);
        }
    }

    @Value("${jeecg.path.dataRoot}")
    private String dataRoot;

    @Autowired
    private ExamQuestionMapper examQuestionMapper;

    @Override
    public DetailBaseVO getDetail(String templateName, String idStr) {
        Long examId = Long.parseLong(idStr);

        // 从数据库中查询考试成绩
        List<ExamReportFlattenedVO> results = examQuestionMapper.selectFlattenedReportData(examId);
        if (results == null || results.isEmpty()) {
            throw new RuntimeException("考试记录为空, examID=" + examId);
        }

        DetailExamScl90VO detail = new DetailExamScl90VO();

        // 数据对象ID (用于报错追溯)
        detail.setDataId(idStr);
        // 考试ID
        detail.setExamId(examId);
        // 企业公章图片
        File companySealFile = FileUtils.getFile(dataRoot, TEMPLATE_BASE_DIR, templateName, COMPANY_SEAL_FILE_NAME);
        detail.setCompanySeal(readImage(companySealFile));

        // 加载评语定义，每次请求都重新加载，保证无状态性
        // 且评语定义文件发现错误可随时修改，无需重启服务
        Scl90CommentDefinitions commentDefinitions = loadCommentDefinitions(templateName);
        
        // 设置各因子的中国人平均分
        for (FactorCommentDefinition factorDef : commentDefinitions.getFactorComments()) {
            switch (factorDef.getFactor()) {
                case "A":
                    detail.setChineseAverageScore_A(factorDef.getChineseAverageScore());
                    break;
                case "B":
                    detail.setChineseAverageScore_B(factorDef.getChineseAverageScore());
                    break;
                case "C":
                    detail.setChineseAverageScore_C(factorDef.getChineseAverageScore());
                    break;
                case "D":
                    detail.setChineseAverageScore_D(factorDef.getChineseAverageScore());
                    break;
                case "E":
                    detail.setChineseAverageScore_E(factorDef.getChineseAverageScore());
                    break;
                case "F":
                    detail.setChineseAverageScore_F(factorDef.getChineseAverageScore());
                    break;
                case "G":
                    detail.setChineseAverageScore_G(factorDef.getChineseAverageScore());
                    break;
                case "H":
                    detail.setChineseAverageScore_H(factorDef.getChineseAverageScore());
                    break;
                case "I":
                    detail.setChineseAverageScore_I(factorDef.getChineseAverageScore());
                    break;
                case "J":
                    detail.setChineseAverageScore_J(factorDef.getChineseAverageScore());
                    break;
                default:
                    throw new RuntimeException("未知的因子标识: " + factorDef.getFactor());
            }
        }

        // 填充基本信息
        ExamReportFlattenedVO firstRow = results.get(0);
        detail.setCandidateName(firstRow.getCandidateName());   // 考生姓名
        detail.setIdType("身 份 证");                            // 证件类型(目前仅支持身份证)
        detail.setIdNumber(firstRow.getIdNumber());             // 证件编号
        detail.setExamDate(firstRow.getExamDate());             // 考试日期
        detail.setUserScore(firstRow.getUserScore().doubleValue()); // 考试成绩
        detail.setExamTitle(firstRow.getExamTitle());           // 考试标题
        detail.setTotalPossibleScore(firstRow.getTotalPossibleScore().doubleValue()); // 卷面总分(满分)

        // 处理因子数据
        processFactorScores(results, detail, commentDefinitions);

        return detail;
    }

    /**
     * 处理因子得分和评语
     */
    private void processFactorScores(List<ExamReportFlattenedVO> results, 
                                   DetailExamScl90VO detail,
                                   Scl90CommentDefinitions commentDefinitions) {
        // 因子得分统计
        Map<String, Integer> factorTotalScores = new HashMap<>();
        Map<String, Integer> factorQuestionCounts = new HashMap<>();
        
        // 第一次遍历：累计各因子的总分和题目数
        int totalQuestionCount = 0;    // 用于统计总题目数
        int totalScore = 0;            // 用于计算总平均分
        
        for (ExamReportFlattenedVO row : results) {
            String factorName = row.getFactorName();
            Integer questionScore = row.getQuestionScore();
            
            if (factorName != null && questionScore != null) {
                totalQuestionCount++;   // 累计总题目数
                totalScore += questionScore;  // 累计总分
                // 累加因子总分：如果因子不存在则创建，存在则累加分数
                factorTotalScores.merge(factorName, questionScore, Integer::sum);
                // 累加因子题目数：如果因子不存在则创建并设为1，存在则计数加1
                factorQuestionCounts.merge(factorName, 1, Integer::sum);
            }
        }

        // 检查题目总数
        if (totalQuestionCount != EXPECTED_QUESTION_COUNT) {
            throw new RuntimeException(String.format(
                "试题数量异常：期望%d题，实际%d题",
                EXPECTED_QUESTION_COUNT,
                totalQuestionCount
            ));
        }
        
        // 计算总平均分（向下取整到1位小数）
        double totalAverageScore = floorToOneDecimal(totalScore * 1.0 / totalQuestionCount);
        detail.setTotalAverageScore(totalAverageScore);
        
        // 计算各因子平均分并设置评语
        int factorCount = 0;          // 有效因子数量
        int abnormalFactorsCnt = 0;   // 阳性因子数量（平均分>=3的因子数）
        // 用于存储阳性因子的字母标识
        List<String> abnormalFactorLetters = new ArrayList<>();
        
        // 处理每个因子的得分
        for (Map.Entry<String, Integer> entry : factorTotalScores.entrySet()) {
            String factorName = entry.getKey();
            Integer factorTotalScore = entry.getValue();
            Integer questionCount = factorQuestionCounts.get(factorName);
            
            if (questionCount != null && questionCount > 0) {
                // 计算因子平均分（向下取整到1位小数）
                double factorAvg = floorToOneDecimal(factorTotalScore.doubleValue() / questionCount);
                
                factorCount++;
                
                // 统计阳性因子数（平均分>=3的因子）
                if (factorAvg >= 3.0) {
                    abnormalFactorsCnt++;
                    // 获取因子的字母标识
                    String factorLetter = commentDefinitions.getFactorLetter(factorName);
                    abnormalFactorLetters.add(factorLetter);
                }
                
                // 获取因子的评语定义
                FactorCommentDefinition factorDef = getFactorCommentDefinition(factorName, commentDefinitions);
                if (factorDef == null) {
                    throw new RuntimeException("找不到因子 " + factorName + " 的评语定义");
                }
                
                // 设置因子评分和评语
                setFactorScoreAndComment(detail, factorName, factorTotalScore.doubleValue(), 
                                       factorAvg, factorDef);
            }
        }

        // 检查因子数量
        if (factorCount != EXPECTED_FACTOR_COUNT) {
            throw new RuntimeException(String.format(
                "因子数量异常：期望%d个因子，实际%d个因子",
                EXPECTED_FACTOR_COUNT,
                factorCount
            ));
        }
        
        // 设置阳性因子数
        detail.setAbnormalFactorsCnt(abnormalFactorsCnt);

        // 对阳性因子字母标识进行排序
        Collections.sort(abnormalFactorLetters);

        // 根据排序后的字母标识构建最终的阳性因子名称列表
        List<String> abnormalFactorNames = abnormalFactorLetters.stream()
            .map(letter -> {
                String factorName = commentDefinitions.getFactorNameByLetter(letter);
                return String.format("%s(%s)", factorName, letter);
            })
            .collect(Collectors.toList());

        // 将排序后的因子名称列表用顿号连接
        detail.setAbnormalFactorNames(
            abnormalFactorNames.isEmpty() ? "无" : String.join("、", abnormalFactorNames)
        );
        
        // 设置总体评价
        CommentRangeDefinition overallRange = findCommentRange(
            totalAverageScore, 
            commentDefinitions.getOverallComments().getRanges()
        );
        
        if (overallRange != null) {
            detail.setOverallStatus(overallRange.getStatus());
            detail.setOverallEvaluation(overallRange.getEvaluation());

            // 生成因子得分折线对比图和雷达图
            generateFactorComparisonChart(detail, commentDefinitions);
        } else {
            throw new RuntimeException("找不到总体评价范围");
        }
    }

    /**
     * 生成因子得分折线对比图和雷达图
     */
    private void generateFactorComparisonChart(DetailExamScl90VO detail, Scl90CommentDefinitions commentDefinitions) {
        List<String[]> chartData = new ArrayList<>();
        
        // 按顺序添加各个因子的数据
        // 数据格式：[因子名称, 考生平均分, 中国人平均分]
        chartData.add(new String[]{"躯体化(A)", 
            String.valueOf(detail.getFactorAverageScore_A()),
            String.valueOf(getChineseAverageScore("躯体化", commentDefinitions))});
            
        chartData.add(new String[]{"强迫症(B)", 
            String.valueOf(detail.getFactorAverageScore_B()),
            String.valueOf(getChineseAverageScore("强迫症", commentDefinitions))});
            
        chartData.add(new String[]{"人际关系敏感(C)", 
            String.valueOf(detail.getFactorAverageScore_C()),
            String.valueOf(getChineseAverageScore("人际关系敏感", commentDefinitions))});
            
        chartData.add(new String[]{"抑郁性(D)", 
            String.valueOf(detail.getFactorAverageScore_D()),
            String.valueOf(getChineseAverageScore("抑郁性", commentDefinitions))});
            
        chartData.add(new String[]{"焦虑性(E)", 
            String.valueOf(detail.getFactorAverageScore_E()),
            String.valueOf(getChineseAverageScore("焦虑性", commentDefinitions))});
            
        chartData.add(new String[]{"敌对性(F)", 
            String.valueOf(detail.getFactorAverageScore_F()),
            String.valueOf(getChineseAverageScore("敌对性", commentDefinitions))});
            
        chartData.add(new String[]{"恐怖性(G)", 
            String.valueOf(detail.getFactorAverageScore_G()),
            String.valueOf(getChineseAverageScore("恐怖性", commentDefinitions))});
            
        chartData.add(new String[]{"偏执性(H)", 
            String.valueOf(detail.getFactorAverageScore_H()),
            String.valueOf(getChineseAverageScore("偏执性", commentDefinitions))});
            
        chartData.add(new String[]{"精神病性(I)", 
            String.valueOf(detail.getFactorAverageScore_I()),
            String.valueOf(getChineseAverageScore("精神病性", commentDefinitions))});
            
        chartData.add(new String[]{"其他(J)", 
            String.valueOf(detail.getFactorAverageScore_J()),
            String.valueOf(getChineseAverageScore("其他", commentDefinitions))});

        // 设置图表数据到detail对象
        detail.setRadarChartData(chartData);
        detail.setLineChartData(chartData);
    }

    /**
     * 获取指定因子的中国人平均分
     */
    private double getChineseAverageScore(String factorName, Scl90CommentDefinitions commentDefinitions) {
        FactorCommentDefinition factorDef = getFactorCommentDefinition(factorName, commentDefinitions);
        if (factorDef == null) {
            throw new RuntimeException("找不到因子 " + factorName + " 的评语定义");
        }
        return factorDef.getChineseAverageScore();
    }

    /**
     * 设置单个因子的得分和评语
     */
    private void setFactorScoreAndComment(DetailExamScl90VO detail, 
                                        String factorName,
                                        Double totalScore,
                                        Double averageScore,
                                        FactorCommentDefinition factorDef) {
        CommentRangeDefinition range = findCommentRange(averageScore, factorDef.getRanges());
        if (range == null) {
            throw new RuntimeException("因子 " + factorName + " 的平均分 " + averageScore + " 超出评语范围");
        }

        // 生成分段条形图
        BufferedImage barChart;
        try {
            barChart = SegmentedBarChartImageGenerator.generateChart(averageScore);
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("生成" + factorName + "因子分段条形图失败: " + e.getMessage());
        }

        switch (factorName) {
            case "躯体化":
                detail.setFactorScore_A(totalScore);
                detail.setFactorAverageScore_A(averageScore);
                detail.setFactorStatus_A(range.getLevel());
                detail.setFactorEvaluation_A(range.getComment());
                detail.setHorizontalRankingBarChart_A(barChart);
                break;
            case "强迫症":
                detail.setFactorScore_B(totalScore);
                detail.setFactorAverageScore_B(averageScore);
                detail.setFactorStatus_B(range.getLevel());
                detail.setFactorEvaluation_B(range.getComment());
                detail.setHorizontalRankingBarChart_B(barChart);
                break;
            case "人际关系敏感":
                detail.setFactorScore_C(totalScore);
                detail.setFactorAverageScore_C(averageScore);
                detail.setFactorStatus_C(range.getLevel());
                detail.setFactorEvaluation_C(range.getComment());
                detail.setHorizontalRankingBarChart_C(barChart);
                break;
            case "抑郁性":
                detail.setFactorScore_D(totalScore);
                detail.setFactorAverageScore_D(averageScore);
                detail.setFactorStatus_D(range.getLevel());
                detail.setFactorEvaluation_D(range.getComment());
                detail.setHorizontalRankingBarChart_D(barChart);
                break;
            case "焦虑性":
                detail.setFactorScore_E(totalScore);
                detail.setFactorAverageScore_E(averageScore);
                detail.setFactorStatus_E(range.getLevel());
                detail.setFactorEvaluation_E(range.getComment());
                detail.setHorizontalRankingBarChart_E(barChart);
                break;
            case "敌对性":
                detail.setFactorScore_F(totalScore);
                detail.setFactorAverageScore_F(averageScore);
                detail.setFactorStatus_F(range.getLevel());
                detail.setFactorEvaluation_F(range.getComment());
                detail.setHorizontalRankingBarChart_F(barChart);
                break;
            case "恐怖性":
                detail.setFactorScore_G(totalScore);
                detail.setFactorAverageScore_G(averageScore);
                detail.setFactorStatus_G(range.getLevel());
                detail.setFactorEvaluation_G(range.getComment());
                detail.setHorizontalRankingBarChart_G(barChart);
                break;
            case "偏执性":
                detail.setFactorScore_H(totalScore);
                detail.setFactorAverageScore_H(averageScore);
                detail.setFactorStatus_H(range.getLevel());
                detail.setFactorEvaluation_H(range.getComment());
                detail.setHorizontalRankingBarChart_H(barChart);
                break;
            case "精神病性":
                detail.setFactorScore_I(totalScore);
                detail.setFactorAverageScore_I(averageScore);
                detail.setFactorStatus_I(range.getLevel());
                detail.setFactorEvaluation_I(range.getComment());
                detail.setHorizontalRankingBarChart_I(barChart);
                break;
            case "其他":
                detail.setFactorScore_J(totalScore);
                detail.setFactorAverageScore_J(averageScore);
                detail.setFactorStatus_J(range.getLevel());
                detail.setFactorEvaluation_J(range.getComment());
                detail.setHorizontalRankingBarChart_J(barChart);
                break;
            default:
                throw new RuntimeException("未知的因子名称: " + factorName);
        }
    }

    /**
     * 根据因子名称获取因子评语定义
     * @param factorName 因子名称 (例如 "躯体化")
     * @return FactorCommentDefinition 或 null
     */
    private FactorCommentDefinition getFactorCommentDefinition(String factorName, Scl90CommentDefinitions definitions) {
        if (definitions == null || definitions.getFactorComments() == null) {
            return null;
        }
        return definitions.getFactorComments().stream()
                .filter(def -> def.getFactorName().equals(factorName))
                .findFirst()
                .orElse(null);
    }

    /**
     * 在评语范围列表中查找匹配分数的范围定义
     * @param score  分数
     * @param ranges 评语范围列表
     * @return CommentRangeDefinition 或 null
     */
    private CommentRangeDefinition findCommentRange(Double score, List<CommentRangeDefinition> ranges) {
        if (score == null || ranges == null) {
            return null;
        }
        
        // 定义一个很小的误差范围
        final double EPSILON = 0.000001;
        
        return ranges.stream()
                .filter(range -> 
                    // 使用误差范围比较
                    (score >= range.getMin() - EPSILON) && 
                    (score <= range.getMax() + EPSILON))
                .findFirst()
                .orElse(null);
    }

    /**
     * 通过反射获取对象字段值
     * @param detail 对象
     * @param fieldName 字段名
     * @return 字段值
     */
    private Object getFieldValue(DetailExamScl90VO detail, String fieldName) {
        try {
            Field field = FieldUtils.getField(DetailExamScl90VO.class, fieldName, true);
            if (field == null) {
                throw new IllegalArgumentException("字段 " + fieldName + " 不存在");
            }
            return field.get(detail);
        } catch (IllegalAccessException e) {
            throw new RuntimeException("获取字段 " + fieldName + " 的值失败", e);
        }
    }

    @Override
    public void generateReport(String templateName, DetailBaseVO detailBaseVO, String outputPath) {
        try {
            // 参数验证
            if (detailBaseVO == null) {
                throw new IllegalArgumentException("考试详情不能为空");
            }
            if (!(detailBaseVO instanceof DetailExamScl90VO)) {
                throw new IllegalArgumentException("考试详情类型不匹配");
            }
            if (outputPath == null || outputPath.trim().isEmpty()) {
                throw new IllegalArgumentException("输出路径不能为空");
            }
            
            DetailExamScl90VO scl90ExamDetail = (DetailExamScl90VO) detailBaseVO;
            
            // 读入模板文件
            File templateFile = FileUtils.getFile(dataRoot, TEMPLATE_BASE_DIR, templateName, TEMPLATE_FILE_NAME);
            if (!templateFile.exists()) {
                throw new IllegalStateException("模板文件不存在: " + templateFile.getPath());
            }
            Document doc = new Document(templateFile.getPath());

            // 获取所有书签
            BookmarkCollection bookmarks = doc.getRange().getBookmarks();
            Set<String> bookmarkNames = new HashSet<>();
            for (Bookmark bookmark : bookmarks) {
                bookmarkNames.add(bookmark.getName());
            }
            
            // 获取所有字段（包括父类）
            List<Field> fields = FieldUtils.getAllFieldsList(DetailExamScl90VO.class);
            Set<String> fieldNames = new HashSet<>();
            for (Field field : fields) {
                fieldNames.add(field.getName());
            }
            
            // 验证：对于未配置特殊处理器的书签，确保模板中的书签名称与 DetailExamScl90VO 的字段名称相符
            for (String bookmarkName : bookmarkNames) {
                if (!bookmarkHandlers.containsKey(bookmarkName) && !fieldNames.contains(bookmarkName)) {
                    throw new IllegalStateException("模板中存在未知书签: " + bookmarkName);
                }
            }
            
            // 根据不同书签调用相应的处理器
            for (Bookmark bookmark : bookmarks) {
                IBookmarkHandler handler = bookmarkHandlers.get(bookmark.getName());
                if (handler != null) {
                    handler.process(bookmark, scl90ExamDetail, doc);
                } else {
                    // 默认处理方式：使用书签名称作为字段名获取值、按文本方式处理
                    new TextBookmarkHandler(
                        detail -> getFieldValue((DetailExamScl90VO)detail, bookmark.getName()),
                        FIELD_VALUE_FORMATTER
                    ).process(bookmark, scl90ExamDetail, doc);
                }
            }
            
            // 保存文档
            // 1. 如果输出路径以.pdf结尾，则保存为PDF格式
            // 2. 否则保存为默认的Word格式(.docx)
            if (outputPath.toLowerCase().endsWith(".pdf")) {
                // 如果开启了调试模式，同时保存Word格式
                if (SAVE_WORD_FOR_DEBUG) {
                    String wordPath = outputPath.substring(0, outputPath.length() - 4) + ".docx";
                    doc.save(wordPath);
                    log.info("已同时保存Word格式文件: {}", wordPath);
                }
                
                // 保存PDF格式
                PdfSaveOptions pdfOptions = new PdfSaveOptions();
                // 设置完整的字体嵌入
                pdfOptions.setEmbedFullFonts(true);
                // 禁用核心字体替换
                pdfOptions.setUseCoreFonts(false);

                doc.save(outputPath, pdfOptions);
            } else {
                doc.save(outputPath);
            }
            
        } catch (IllegalArgumentException | IllegalStateException e) {
            // 直接抛出参数验证和状态相关的异常
            throw e;
        } catch (Exception e) {
            // 其他异常包装为RuntimeException
            throw new RuntimeException("生成报告失败: " + e.getMessage(), e);
        }
    }
}
