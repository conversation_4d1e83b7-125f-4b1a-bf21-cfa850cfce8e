package org.jeecg.modules.training.entity;


import lombok.Data;

import java.util.Date;

/**
 * 每个学习资料船舶每日时长表(StudyTimePerDepartment)表实体类
 *
 * <AUTHOR>
 * @since 2023-12-28 15:39:49
 */
@Data
public class StudyTimePerDepartment {

    private Long id;
    //部门ID
    private String departmentId;
    //学习资料ID
    private String studyId;
    //日期
    private Date date;
    //学习时长
    private Double studyTime;
    //船队名称
    private String fleetName;
    //统计的周数
    private Integer week;
}

