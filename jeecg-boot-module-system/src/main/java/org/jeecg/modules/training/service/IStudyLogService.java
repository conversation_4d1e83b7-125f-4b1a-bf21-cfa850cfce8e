package org.jeecg.modules.training.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.Category;
import org.jeecg.modules.training.entity.StudyLog;
import org.jeecg.modules.training.vo.StudyLogOneMonth;
import org.jeecg.modules.training.vo.StudyLogVO;

import java.util.List;

/**
 * @Description: 学习日志表
 * @Author: hzk
 * @Date: 2022-09-02
 * @Version: V1.0
 */
public interface IStudyLogService extends IService<StudyLog> {

    /**
     * 分页查询
     *
     * @param page
     * @param queryWrapper
     * @return
     */
    IPage<StudyLogVO> getPageList(Page<StudyLogVO> page, QueryWrapper<StudyLogVO> queryWrapper);

    /**
     * 根据用户id和分类id分组后分页查询
     *
     * @param page
     * @param queryWrapper
     * @return
     */
    IPage<StudyLogVO> getStudyList(Page<StudyLogVO> page, QueryWrapper<StudyLogVO> queryWrapper);

    /**
     * 保存数据
     *
     * @param studyLog
     */
    StudyLog saveInfo(StudyLog studyLog);

    /**
     * 获取用户已读的数量
     *
     * @param userId
     * @param boardTime
     * @param post
     * @param groupInfo
     * @param category
     * @return
     */
    Long getAlreadyReadNumber(String userId, String boardTime, String post, String groupInfo, Category category);

    /**
     * 获取用户需要关注并且已经关注的数量
     *
     * @param userId
     * @param boardTime
     * @param post
     * @param groupInfo
     * @param category
     * @return
     */
    Long getAlreadyAttentionNumber(String userId, String boardTime, String post, String groupInfo, Category category);

    /**
     * 获取所有符合条件的学习记录
     *
     * @param queryWrapper
     * @return
     */
    List<StudyLogVO> getList(QueryWrapper<StudyLogVO> queryWrapper);


    List<StudyLogVO> getStudyList(QueryWrapper<StudyLogVO> queryWrapper);

    List<StudyLogOneMonth> getOneMonthRecordList(QueryWrapper<StudyLogOneMonth> queryWrapper, String depRoute);

    IPage<StudyLogOneMonth> getOneMonthRecordLisPage(Page<StudyLogOneMonth> page, QueryWrapper<StudyLogOneMonth> queryWrapper, @Param("depRoute") String depRoute);
}
