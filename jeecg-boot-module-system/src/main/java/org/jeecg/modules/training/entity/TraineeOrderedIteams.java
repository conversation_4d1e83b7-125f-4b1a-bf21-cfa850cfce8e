package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class TraineeOrderedIteams {

    @TableId(type = IdType.AUTO)
    private Integer id;
    
    // 船员ID
    private String traineeId;

    // iteam类型, 2:paper, 3:study
    private Short categorieType;
    
    // 资料ID(暂时使用share_id)
    private Long iteamId;
}