package org.jeecg.modules.training.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.training.entity.Category;
import org.jeecg.modules.training.mapper.CategoryMapper;
import org.jeecg.modules.training.service.ICategoryService;
import org.jeecg.modules.training.util.FindsCategoryChildrenUtil;
import org.jeecg.modules.training.vo.CategoryTreeVO;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 分类
 * @Author: huazhengkan
 * @Date: 2022-05-04
 * @Version: V1.0
 */
@Slf4j
@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category> implements ICategoryService {
    @Resource
    private ISyncStateService syncStateService;

    @Autowired
    private ISysDepartService sysDepartService;

    /**
     * 获取分类树信息根据关键字
     *
     * @param keyWord
     * @return
     */
    @Override
    public List<CategoryTreeVO> queryTreeByKeyWord(String types, String keyWord) {
        LambdaQueryWrapper<Category> query = new LambdaQueryWrapper<Category>();

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        query.and(q -> q.eq(Category::getDepRoute, loginUser.getDepRoute()));

        //分类树只展示父类;
        //query.isNull(Category::getParentId);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(types)) {
            Object[] tps = Arrays.stream(types.split(",")).map(Integer::parseInt).toArray();
            query.in(Category::getType, tps);
        }
        query.orderByAsc(Category::getType);
        query.orderByAsc(Category::getCreateTime);
        List<Category> list = this.list(query);
        //生成树状结构
        List<CategoryTreeVO> listResult = FindsCategoryChildrenUtil.wrapTreeDataToTreeList(list);
        //listResult.stream().forEach(categoryTreeVO -> {
        //    categoryTreeVO.setDisabled(false);
        //});
        List<CategoryTreeVO> treelist = new ArrayList<>();
        if (StringUtils.isNotBlank(keyWord)) {
            this.getTreeByKeyWord(keyWord, listResult, treelist);
        } else {
            return listResult;
        }
        return treelist;
    }

    /**
     * 逻辑删除
     *
     * @param id
     * @return
     */
    @Override
    public Result<Category> deleteById(String id) {
        Result<Category> result = new Result<Category>();
        Category category = this.getById(id);
        if (category == null) {
            result.error500("未找到对应实体");
        } else {
            category.setDelFlag(CommonConstant.DEL_FLAG_1);
            boolean ok = this.updateById(category);
            if (ok) {
                result.success("删除成功!");
            }
            syncStateService.resetSyncState(CommonConstant.CATEGORY, id);
        }
        return result;
    }

    /**
     * 逻辑批量删除
     *
     * @param ids
     * @return
     */
    @Override
    public Result<Category> deleteBatch(List<String> ids) {
        List<Category> list = new ArrayList<>();
        for (String id : ids) {
            Category category = this.getById(id);
            category.setDelFlag(CommonConstant.DEL_FLAG_1);
            list.add(category);
            syncStateService.resetSyncState(CommonConstant.CATEGORY, id);
        }
        if (!CollectionUtils.isEmpty(list)) {
            this.updateBatchById(list);
        }
        return Result.OK("批量删除成功！");
    }

    /**
     * 根据关键字筛选部门信息
     *
     * @param keyWord
     * @return
     */
    public void getTreeByKeyWord(String keyWord, List<CategoryTreeVO> allResult, List<CategoryTreeVO> newResult) {
        for (CategoryTreeVO model : allResult) {
            if (model.getName().contains(keyWord)) {
                newResult.add(model);
            } else if (model.getChildren() != null) {
                getTreeByKeyWord(keyWord, model.getChildren(), newResult);
            }
        }
    }


    public Long shareCategory(Long categoryId, String route, boolean force) {
        List<Category> list = list(new LambdaQueryWrapper<Category>().eq(Category::getDepRoute, route).eq(Category::getShareId, categoryId).eq(Category::getDelFlag, CommonConstant.DEL_FLAG_0));

        if (!list.isEmpty()) {
            return updateShareCategory(categoryId, list.get(0).getId(), route, force);
        }

        Category category = baseMapper.getById(categoryId);
        if (category == null) {
            return categoryId;
        }

        if (category.getParentId() != null) {
            category.setParentId(shareCategory(category.getParentId(), route, force));
        }
        category.setDepRoute(route);
        category.setShareMode("0");
        category.setShareId(categoryId);
        category.setId(null);
        baseMapper.insert(category);

        if (category.getParentId() != null) {
            Category p = baseMapper.getById(category.getParentId());
            category.setRoute(p.getRoute() + "/" + category.getId());
        } else {
            category.setRoute("/" + category.getId());
        }
        baseMapper.updateById(category);

        return category.getId();
    }

    private Long updateShareCategory(Long adminCategoryId, Long userCategoryId, String route, boolean force) {
        Category adminCategory = baseMapper.getById(adminCategoryId);
        Category userCategory = baseMapper.getById(userCategoryId);
        if (force || !Objects.equals(adminCategory.getUpdateTime(), userCategory.getUpdateTime())) {
            if (adminCategory.getParentId() != null) {
                userCategory.setParentId(shareCategory(adminCategory.getParentId(), route, force));
            } else {
                userCategory.setParentId(null);
            }
            if (userCategory.getParentId() != null) {
                Category p = baseMapper.getById(userCategory.getParentId());
                if (p != null)
                    userCategory.setRoute(p.getRoute() + "/" + userCategory.getId());
                else
                    log.error("not found parent category of : " + userCategory.getParentId() + "->" + userCategory.getId());
            } else {
                userCategory.setRoute("/" + userCategory.getId());
            }

            userCategory.setAppMenu(adminCategory.getAppMenu());
            userCategory.setDelFlag(adminCategory.getDelFlag());
            userCategory.setDescription(adminCategory.getDescription());
            userCategory.setDisabled(adminCategory.getDisabled());
            userCategory.setIcon(adminCategory.getIcon());
            userCategory.setIndustryIds(adminCategory.getIndustryIds());
            userCategory.setName(adminCategory.getName());
            userCategory.setPhaseLevel(adminCategory.getPhaseLevel());
            userCategory.setRoute(adminCategory.getRoute());
            userCategory.setShowOrder(adminCategory.getShowOrder());
            userCategory.setType(adminCategory.getType());
            userCategory.setUpdateTime(adminCategory.getUpdateTime());
            baseMapper.updateById(userCategory);
        }
        return userCategoryId;
    }
}
