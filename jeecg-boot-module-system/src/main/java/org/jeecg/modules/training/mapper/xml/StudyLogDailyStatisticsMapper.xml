<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.StudyLogDailyStatisticsMapper">

    <!--从study_log表获取指定阅读类型和日期的学习记录并返回其他类型-->
    <select id="getTimeAndTypeList" resultType="org.jeecg.modules.training.entity.StudyLogDailyStatistics">
        select sl.study_id, Date(sl.end_time) as study_date, sl.dep_route, count(*) as times
        from study_log sl
                 join study on sl.study_id = study.id
        where sl.del_flag = 0
          and study.del_flag = 0
          and study.dep_route is not null
          and sl.dep_route is not null
          and sl.type = #{type}
          and sl.end_time is not null
          and sl.end_time between #{startDate} and #{endDate}
        group by sl.study_id
    </select>

    <!-- 获取近一个月学习资料被学习次数 统计表专用 -->
    <select id="getList" resultType="org.jeecg.modules.training.vo.StudyLogDailyStatisticsVO">
        select study.name as study_name, sum(slds.times) as times
        from study_log_daily_statistics slds
                 join study on slds.study_id = study.id
        where study.del_flag = 0
          and slds.study_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
          and slds.dep_route like CONCAT(#{depRoute}, '%')
          and study.dep_route like CONCAT(#{depRoute}, '%')
        group by slds.study_id
        order by times desc
        limit 10;
    </select>

    <!-- 从study_log表获取至今为止指定阅读类型的学习记录并返回其他类型 -->
    <select id="getSoFarAndTypeList" resultType="org.jeecg.modules.training.entity.StudyLogDailyStatistics">
        select Date(sl.end_time) as study_date, sl.study_id, sl.dep_route, count(*) as times
        from study_log sl
                 join study on sl.study_id = study.id
        where sl.del_flag = 0
          and study.del_flag = 0
          and study.dep_route is not null
          and sl.dep_route is not null
          and sl.type = #{type}
          and sl.end_time is not null
        group by study_id, study_date
    </select>
</mapper>
