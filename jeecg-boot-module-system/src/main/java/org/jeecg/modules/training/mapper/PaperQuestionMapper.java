package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.PaperQuestion;
import org.jeecg.modules.training.vo.PaperQuestionVO;

import java.util.List;

/**
 * @Description: 试卷库题目
 * @Author: huazhengkan
 * @Date: 2022-06-15
 * @Version: V1.0
 */
public interface PaperQuestionMapper extends BaseMapper<PaperQuestion> {

    List<PaperQuestion> listQuestions(@Param("paperId") Long paperId, @Param("type") int type);

    IPage<PaperQuestionVO> listQuestionsPages(Page<PaperQuestionVO> page, @Param("ew") QueryWrapper<PaperQuestionVO> queryWrapper);

    List<PaperQuestionVO> listQuestionsVO(@Param("ew") QueryWrapper<PaperQuestionVO> queryWrapper);
}
