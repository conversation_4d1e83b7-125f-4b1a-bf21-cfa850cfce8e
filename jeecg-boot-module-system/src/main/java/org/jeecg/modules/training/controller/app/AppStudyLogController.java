package org.jeecg.modules.training.controller.app;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.training.async.phase.PushPhaseService;
import org.jeecg.modules.training.entity.Study;
import org.jeecg.modules.training.entity.StudyLog;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.*;

/**
 * @Description: 学习日志表
 * @Author: hzk
 * @Date: 2022-09-02
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "学习日志表")
@RestController
@RequestMapping("/app/studyLog")
public class AppStudyLogController extends JeecgController<StudyLog, IStudyLogService> {

    @Autowired
    private TraineeService traineeService;

    @Autowired
    private IStudyService studyService;

    @Autowired
    private IStudyLogNumService studyLogNumService;

    @Autowired
    private IStudyUserStateService studyUserStateService;

    @Resource
    private PushPhaseService userPhaseHandleService;

    @Override
    protected QueryWrapper<StudyLog> onCustomQuery(HttpServletRequest request, StudyLog object) {

        QueryWrapper<StudyLog> queryWrapper = super.onCustomQuery(request, object);

        try {
            if (request.getParameterMap().containsKey("studyTime_begin") || request.getParameterMap().containsKey("studyTime_end")) {
                QueryGenerator.doIntervalQuery(queryWrapper, request.getParameterMap(), "class java.util.Date", "studyTime", "start_time");
            }
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
        }

        queryWrapper.isNotNull("end_time");
        queryWrapper.eq("del_flag", CommonConstant.DEL_FLAG_0);

        /*
         * 获取部门路径
         */
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.lambda().and(q -> q.likeRight(StudyLog::getDepRoute, loginUser.getDepRoute()));

        return queryWrapper;
    }

    /**
     * 批量上传离线学习日志
     *
     * @param jsonObject
     * @return
     */
    @PostMapping("/uploadStudyLog")
    public Result<?> uploadStudyLog(@RequestBody JSONObject jsonObject) {

        Object study = jsonObject.get("study");
        ArrayList list = JSON.parseObject(study.toString(), ArrayList.class);
        Set<String> users = new HashSet<>();
        for (Object o : list) {
            String[] split = o.toString().split(",");
            String userId = split[0];
            String studyId = split[1];
            String start = split[2];
            String end = split[3];
            Date startTime = new Date(Long.parseLong(start));
            Date endTime = new Date(Long.parseLong(end));
            long useTime = (Long.parseLong(end) - Long.parseLong(start)) / 1000;

            Study study1 = studyService.getById(studyId);

            if (study1 == null || (study1.getDelFlag() != null && study1.getDelFlag().equals("1"))) {
                log.warn("Skip study log (1): " + userId + "," + studyId + "," + start + "," + end);
                continue;
            }

            if (study1.getTotalTime() != null && study1.getTotalTime() > useTime) {
                log.warn("Skip study log (2): " + userId + "," + studyId + "," + start + "," + end);
                continue;
            }

            //1.1根据用户id查询用户信息并封装到StudyLog里面
            StudyLog studyLog = new StudyLog();
            Trainee user = traineeService.getById(userId);
            studyLog.setType(0);
            studyLog.setDelFlag(0);
            studyLog.setUserId(userId);
            studyLog.setDepRoute(study1.getDepRoute());
            studyLog.setDepartment(user.getTeamId());
            //1.2封装学习资料id
            studyLog.setStudyId(Long.valueOf(studyId));
            //1.3封装开始结束时间以及总耗时
            studyLog.setStartTime(startTime);

            studyLog.setEndTime(endTime);
            studyLog.setUseTime(useTime);
            //1.4保存学习日志表
            service.save(studyLog);
            //2.保存学习日志数量关系表
            studyLogNumService.saveLogNum(userId, Long.valueOf(studyId));
            //3.保存用户学习状态表表
            studyUserStateService.saveStatue(userId, Long.valueOf(studyId), 1);
            users.add(userId);
        }

        if (!users.isEmpty()) {
            List<Trainee> userList = traineeService.listByIds(users);
            userPhaseHandleService.checkTrainee(userList);
        }

        return Result.ok("保存离线学习成功");
    }
}
