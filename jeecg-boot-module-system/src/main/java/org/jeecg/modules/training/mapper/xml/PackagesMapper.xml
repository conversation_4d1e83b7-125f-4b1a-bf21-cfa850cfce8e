<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.PackagesMapper">

    <!--    分页列表查询-->
    <select id="listPapersPages" resultType="org.jeecg.modules.training.vo.PackagesVO">
        select *
        from (select s.id,
                     s.member_num,
                     s.surplus_num,
                     s.expire_time,
                     s.status,
                     s.create_time,
                     s.sys_depart_id,
                     s.name,
                     sd.depart_name
              from packages s
                       join sys_depart sd on sd.id = s.sys_depart_id
              where s.del_flag = 0
                and s.type = 1
                and sd.del_flag = 0) as packages
            ${ew.customsqlsegment}
    </select>

    <!-- 查询企业会员状态 -->
    <select id="getDepartMemberLevel" resultType="java.lang.Short">
        SELECT
            -- 找不到`packages`记录，则表示无有效套餐
            COALESCE(p.version_type, 0) AS level
        FROM trainee t
            LEFT JOIN sys_trainee_depart std ON (t.id = std.trainee_id)
            -- 企业付费制企业内每个船员必定只有一个套餐
            LEFT JOIN packages p ON (p.id = std.packages_id AND p.del_flag = 0 AND p.status = 1)
        WHERE std.dep_id = #{sysDepartId}
          AND t.id = #{traineeId}
    </select>

    <!-- 查询船员会员状态 -->
    <select id="getTraineeMemberLevel" resultType="java.lang.Short">
        SELECT
            -- 找不到`packages`记录，则表示无有效套餐
            COALESCE(p.version_type, 0) AS level
        FROM trainee t
            LEFT JOIN packages p ON (p.id = t.current_packages_id AND p.del_flag = 0 AND p.status = 1)
        WHERE t.id = #{traineeId}
    </select>
</mapper>
