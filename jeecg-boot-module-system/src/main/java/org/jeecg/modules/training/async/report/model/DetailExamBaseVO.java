package org.jeecg.modules.training.async.report.model;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 考试详情抽象基类
 * 定义所有类型考试共有的基本信息
 * 不同类型的考试可以通过继承此类进行扩展
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class DetailExamBaseVO extends DetailBaseVO {
    /**
     * 考试唯一标识
     */
    protected Long examId;

    /**
     * 考生姓名
     */
    protected String candidateName;
    
    /**
     * 证件类型
     */
    protected String idType;
    
    /**
     * 证件编号
     */
    protected String idNumber;
    
    /**
     * 考试日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    protected LocalDate examDate;
    
    /**
     * 考试得分
     */
    protected Double userScore;

    /**
     * 考试标题
     * 例如：海员职业标准考试
     */
    protected String examTitle;
}