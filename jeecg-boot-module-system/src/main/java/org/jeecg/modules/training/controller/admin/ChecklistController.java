package org.jeecg.modules.training.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.training.entity.Checklist;
import org.jeecg.modules.training.entity.ChecklistTask;
import org.jeecg.modules.training.service.IChecklistItemService;
import org.jeecg.modules.training.service.IChecklistService;
import org.jeecg.modules.training.service.IChecklistTaskService;
import org.jeecg.modules.training.vo.ChecklistVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Description: 许可证和检查配置主表
 * @Author: jeecg-boot
 * @Date: 2022-10-23
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "许可证和检查配置主表")
@RestController
@RequestMapping("/adm/checklist")
public class ChecklistController extends JeecgController<Checklist, IChecklistService> {

    @Value(value = "${jeecg.path.dataRoot}")
    private String dataRoot;

    @Autowired
    private IChecklistService checklistService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private IChecklistItemService checklistItemService;

    @Autowired
    private IChecklistTaskService checklistTaskService;


    /**
     * 分页列表查询
     *
     * @param checklist
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "许可证和检查配置-分页列表查询")
    @ApiOperation(value = "许可证和检查配置-分页列表查询", notes = "许可证和检查配置-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ChecklistVO checklist,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<ChecklistVO> queryWrapper = QueryGenerator.initQueryWrapper(checklist, req.getParameterMap());
        Page<ChecklistVO> page = new Page<ChecklistVO>(pageNo, pageSize);
        IPage<ChecklistVO> pageList = checklistService.listPages(page, queryWrapper);
        for (ChecklistVO checklistVO : pageList.getRecords()) {
            if (checklistVO.getPostFactor() != null) {
                while (checklistVO.getPostFactor().startsWith(","))
                    checklistVO.setPostFactor(checklistVO.getPostFactor().substring(1));
                while (checklistVO.getPostFactor().endsWith(","))
                    checklistVO.setPostFactor(checklistVO.getPostFactor().substring(0, checklistVO.getPostFactor().length() - 1));
            }
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(checklistVO.getWorkflowId()).singleResult();
            if (processDefinition != null) {
                checklistVO.setWorkflowName(processDefinition.getName());
            }
        }
        return Result.OK(pageList);
    }

    /**
     * 启用&禁用
     */
    @RequestMapping(value = "/frozenBatch", method = RequestMethod.PUT)
    public Result<Checklist> frozenBatch(@RequestBody JSONObject jsonObject) {
        Result<Checklist> result = new Result<>();
        try {
            String ids = jsonObject.getString("ids");
            String status = jsonObject.getString("disabled");
            Boolean disabled = Boolean.parseBoolean(status);
            String[] idArr = ids.split(",");
            for (String id : idArr) {
                if (oConvertUtils.isNotEmpty(id)) {
                    this.service.update(new Checklist().setDisabled(disabled),
                            new UpdateWrapper<Checklist>().lambda().eq(Checklist::getId, id));
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败" + e.getMessage());
        }
        result.success("操作成功！");
        return result;
    }

    /**
     * 添加
     *
     * @param checklist
     * @return
     */
    @AutoLog(value = "许可证和检查配置主表-添加")
    @ApiOperation(value = "许可证和检查配置主表-添加", notes = "许可证和检查配置主表-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody Checklist checklist) {
        checklist.setDisabled(Boolean.TRUE);
        checklist.setDelFlag(CommonConstant.DEL_FLAG_0);
        fixFactors(checklist);
        checklistService.save(checklist);
        return Result.OK("添加成功！");
    }

    private void fixFactors(Checklist checklist) {
        if (checklist.getPostFactor() != null) {
            checklist.setPostFactor("," + checklist.getPostFactor().trim() + ",");
            if (checklist.getPostFactor().equals(",,")) {
                checklist.setPostFactor("");
            }
        }
    }

    /**
     * 文件上传方法
     *
     * @param request
     * @param response
     * @return
     */
    @PostMapping(value = "/upload")
    public Result<?> upload(HttpServletRequest request, HttpServletResponse response) {
        Result<String> result = new Result<>();
        String savePath = "";
        StringBuffer bizPath = new StringBuffer();
        bizPath.append("checklist/templates");
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multipartRequest.getFile("file");
        bizPath.append(File.separator);
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        String curdateStr = format.format(new Date());
        bizPath.append(curdateStr);
        savePath = CommonUtils.uploadLocal(file, bizPath.toString(), dataRoot);
        if (oConvertUtils.isNotEmpty(savePath)) {
            result.setResult(savePath);
            result.setCode(200);
            result.setMessage("上传成功！");
        } else {
            result.setMessage("上传失败！");
            result.setSuccess(false);
        }
        return result;
    }

    /**
     * 发布了新的流程图后，替换流程图
     *
     * @param oldProcDefId
     * @param newProcDefId
     * @return
     */
    @RequestMapping(value = "/replaceWorkflow", method = RequestMethod.POST)
    public Result<?> replaceWorkflow(String oldProcDefId, String newProcDefId) {
        if (StringUtils.isNotEmpty(oldProcDefId) && !oldProcDefId.equals("0")) {
            LambdaUpdateWrapper<Checklist> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(Checklist::getWorkflowId, newProcDefId).eq(Checklist::getWorkflowId, oldProcDefId);
            service.update(updateWrapper);
        }
        return Result.OK();
    }

    /**
     * 编辑
     *
     * @param checklist
     * @return
     */
    @AutoLog(value = "许可证和检查配置主表-编辑")
    @ApiOperation(value = "许可证和检查配置主表-编辑", notes = "许可证和检查配置主表-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody Checklist checklist) {
        checklist.setDelFlag(CommonConstant.DEL_FLAG_0);
        checklistService.updateById(checklist);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "许可证和检查配置主表-通过id删除")
    @ApiOperation(value = "许可证和检查配置主表-通过id删除", notes = "许可证和检查配置主表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        List<ChecklistTask> existTasks = checklistTaskService.getByChecklistId(id);
        if (!CollectionUtils.isEmpty(existTasks)) {
            throw new JeecgBootException("此配置已经被实例使用了，不能删除！");
        }
        checklistService.removeById(id);
        checklistItemService.deleteByMainIds(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "许可证和检查配置主表-批量删除")
    @ApiOperation(value = "许可证和检查配置主表-批量删除", notes = "许可证和检查配置主表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        List<ChecklistTask> existTasks = checklistTaskService.getByChecklistId(ids.split(","));
        if (!CollectionUtils.isEmpty(existTasks)) {
            throw new JeecgBootException("此配置已经被实例使用了，不能删除！");
        }
        this.checklistService.removeByIds(Arrays.asList(ids.split(",")));
        checklistItemService.deleteByMainIds(ids.split(","));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "许可证和检查配置主表-通过id查询")
    @ApiOperation(value = "许可证和检查配置主表-通过id查询", notes = "许可证和检查配置主表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        Checklist checklist = checklistService.getById(id);
        return Result.OK(checklist);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param checklist
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Checklist checklist) {
        return super.exportXls(request, checklist, Checklist.class, "许可证和检查配置主表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Checklist.class);
    }

    /**
     * APP根据分类获取分页列表
     *
     * @param
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    /*@AutoLog(value = "APP根据分类获取分页列表")
    @ApiOperation(value = "APP根据分类获取分页列表", notes = "APP根据分类获取分页列表")
    @GetMapping(value = "/getChecklists")
    public Result<?> getChecklists(@RequestParam(name = "categoryId") Long categoryId,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }
        SysUser sysUser = sysUserService.getUserByName(loginUser.getUsername());
        String post = sysUser.getPost();
        Page<ChecklistVO> page = new Page<ChecklistVO>(pageNo, pageSize);
        IPage<ChecklistVO> pageList = checklistService.findChecklists(page, categoryId, post);
        return Result.OK(pageList);
    }*/

}
