<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.InvoiceApplyMapper">

    <select id="pageQuery" resultType="org.jeecg.modules.training.vo.InvoiceApplyVO">
        select *
        from (select id,name,order_num,status,payer_name,payer_register_no,invoice_amount,email,create_time,update_time,pay_type,
                     CASE pay_type
                         WHEN 0 THEN '支付宝'
                         WHEN 1 THEN '微信'
                         ELSE '其他'
                         END AS pay_type_text
              from invoice_apply) as apply
            ${ew.customSqlSegment};
    </select>
</mapper>
