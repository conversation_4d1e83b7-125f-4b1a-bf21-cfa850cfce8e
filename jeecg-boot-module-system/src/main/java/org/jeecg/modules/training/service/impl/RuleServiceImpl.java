package org.jeecg.modules.training.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.modules.training.entity.Rule;
import org.jeecg.modules.training.mapper.RuleMapper;
import org.jeecg.modules.training.service.IRuleService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @Description: 规则表
 * @Author: huazhengkan
 * @Date: 2022-05-04
 * @Version: V1.0
 */
@Service
public class RuleServiceImpl extends ServiceImpl<RuleMapper, Rule> implements IRuleService {

    /**
     * 逻辑删除
     *
     * @param id
     * @return
     */
    @Override
    public Result<Rule> deleteById(String id) {
        Result<Rule> result = new Result<Rule>();
        Rule rule = this.getById(id);
        if (rule == null) {
            result.error500("未找到对应实体");
        } else {
            rule.setDelFlag(CommonConstant.DEL_FLAG_1);
            boolean ok = this.updateById(rule);
            if (ok) {
                result.success("删除成功!");
            }
        }
        return result;
    }

    /**
     * 逻辑批量删除
     *
     * @param ids
     * @return
     */
    @Override
    public Result<Rule> deleteBatch(List<String> ids) {
        List<Rule> list = new ArrayList<>();
        for (String id : ids) {
            Rule rule = this.getById(id);
            rule.setDelFlag(CommonConstant.DEL_FLAG_1);
            list.add(rule);
        }
        if (!CollectionUtils.isEmpty(list)) {
            this.updateBatchById(list);
        }
        return Result.OK("批量删除成功！");
    }

    /**
     * 根据 ruleIds查询，查询规则名称
     *
     * @param ruleIds
     * @return
     */
    @Override
    public Map<Long, String> getNamesByRuleIds(List<Long> ruleIds) {
        List<Rule> list = this.baseMapper.selectBatchIds(ruleIds);
        Map<Long, String> res = new HashMap(5);
        list.forEach(item -> {
            res.put(item.getId(), item.getName());
        });
        return res;
    }

    /**
     * 查询list
     *
     * @param page
     * @param params
     * @return
     */
    @Override
    public IPage<Rule> getList(Page<Rule> page, Map<String, Object> params) {
        return this.baseMapper.getAll(params, page);
    }

    @Override
    public Long shareRule(Long ruleId, String route, boolean force) {
        List<Rule> list = list(new LambdaQueryWrapper<Rule>().eq(Rule::getDepRoute, route).eq(Rule::getShareId, ruleId).eq(Rule::getDelFlag, CommonConstant.DEL_FLAG_0));
        if (!list.isEmpty()) {
            return updateShareRule(ruleId, list.get(0).getId(), route, force);
        }

        Rule rule = baseMapper.getById(ruleId);
        if (rule == null) {
            return ruleId;
        }

        //必须改成对象，否则复制结果错误
        rule.setDepRoute(route);
        rule.setShareId(ruleId.toString());
        rule.setShareMode("0");
        rule.setId(null);
        baseMapper.insert(rule);
        return rule.getId();
    }

    private Long updateShareRule(Long adminRuleId, Long userRuleId, String route, boolean force) {
        Rule adminRule = baseMapper.getById(adminRuleId);
        Rule userRule = baseMapper.getById(userRuleId);
        if (force || !Objects.equals(adminRule.getUpdateTime(), userRule.getUpdateTime())) {
            userRule.setContent(adminRule.getContent());
            userRule.setName(adminRule.getName());
            userRule.setType(adminRule.getType());
            userRule.setIndustryIds(adminRule.getIndustryIds());
            userRule.setDelFlag(adminRule.getDelFlag());
            userRule.setUpdateTime(adminRule.getUpdateTime());

            baseMapper.updateById(userRule);
        }
        return userRuleId;
    }
}
