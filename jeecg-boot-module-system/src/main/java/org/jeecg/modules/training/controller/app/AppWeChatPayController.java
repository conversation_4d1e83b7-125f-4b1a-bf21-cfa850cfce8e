package org.jeecg.modules.training.controller.app;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.wechat.pay.java.core.exception.HttpException;
import com.wechat.pay.java.core.exception.MalformedMessageException;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.exception.ValidationException;
import com.wechat.pay.java.core.http.HttpRequest;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RSAPublicKeyNotificationConfig;
import com.wechat.pay.java.service.payments.h5.H5Service;
import com.wechat.pay.java.service.payments.h5.model.*;
import com.wechat.pay.java.service.payments.model.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBoot401Exception;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.system.configuration.WechatPayProperties;
import org.jeecg.modules.training.entity.Orders;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.service.IOrdersService;
import org.jeecg.modules.training.service.IPackagePriceCalculationService;
import org.jeecg.modules.training.service.TraineeService;
import org.jeecg.modules.training.vo.PriceInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/app/wechatPay")
public class AppWeChatPayController {

    @Autowired
    private IPackagePriceCalculationService packagePriceCalculationService;

    @Autowired
    private IOrdersService ordersService;

    @Autowired(required = false)
    private H5Service h5Service;

    @Autowired(required = false)
    private WechatPayProperties properties;

    // 获取调起支付的H5链接(h5_url)
    @PostMapping("/createOrder")
    public Result<?> createOrder(
            HttpServletRequest request,
            @RequestParam Integer productCategorie,
            @RequestParam Long productDetail) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (oConvertUtils.isEmpty(loginUser)) {
            // 直接抛出401异常，会被全局异常处理器处理
            throw new JeecgBoot401Exception(CommonConstant.TOKEN_IS_INVALID_MSG);
        }

        return Result.OK(getH5Url(loginUser, request, productCategorie, productDetail));
    }

    // 获取调起支付的H5链接(h5_url)
    public Result<?> getH5Url(LoginUser loginUser, HttpServletRequest request, Integer productCategorie, Long productDetail) {

        JSONObject returnData = new JSONObject();
        try {
            // 获取船员ID
            String traineeId = loginUser.getId();
            // 获取部门ID
            String sysDepartId = StrUtil.subAfter(loginUser.getDepRoute(), "/", true);
            // 获取商品信息(价格和说明)
            PriceInfoVO priceInfoVO = packagePriceCalculationService.getPrice(traineeId, sysDepartId, productCategorie, productDetail);

            // 构建请求体 以下都是必需参数
            PrepayRequest prepayRequest = new PrepayRequest();
            prepayRequest.setAppid(properties.getAppid());// APPID
            prepayRequest.setMchid(properties.getMchid());// 商户号
            // 商品描述，不能超过127个字符
            String productDescription = priceInfoVO.getDescription();
            prepayRequest.setDescription(productDescription);

            // 生成商户订单号 要求6-32个字符内，只能是数字、大小写字母_-|* 且在同一个商户号下唯一。
            String outTradeNo = System.currentTimeMillis() + RandomUtil.randomNumbers(4);
            prepayRequest.setOutTradeNo(outTradeNo);
            // 商户回调地址
            prepayRequest.setNotifyUrl(properties.getNotifyUrl());
            // 订单金额
            BigDecimal amount = priceInfoVO.getPrice();
            Amount WechatPayamount = new Amount();
            // 单位为分，须填整数 (右移小数点两位后取整)
            WechatPayamount.setTotal(amount.movePointRight(2).intValue());
            prepayRequest.setAmount(WechatPayamount);
            // 场景信息
            SceneInfo sceneInfo = new SceneInfo();
            // 获取用户的客户端IP，支持IPv4和IPv6两种格式的IP地址。
            sceneInfo.setPayerClientIp(request.getRemoteAddr());
            // H5场景信息
            H5Info h5Info = new H5Info();
            // 支付场景 使用H5支付的场景：Wap、iOS、Android
            h5Info.setType("Wap");
            sceneInfo.setH5Info(h5Info);
            prepayRequest.setSceneInfo(sceneInfo);

            // 发送请求
            PrepayResponse prepayResponse = h5Service.prepay(prepayRequest);

            if (prepayResponse == null) {
                log.error("微信支付返回的请求prepayResponse为空");
                return Result.error("微信支付官方返回的请求对象为空！请联系管理人员！");
            }

            String h5Url = prepayResponse.getH5Url();

            if (h5Url != null) { // 获取支付链接成功
                log.info("获取微信支付H5链接成功！");
                returnData.put("h5Url", h5Url);
                Orders orders = ordersService.initOrders(loginUser, outTradeNo, amount, 1, productCategorie, productDetail, productDescription);
                log.info("创建Orders对象成功！");
                returnData.put("orderId", orders.getId());
                returnData.put("redirectUrl", properties.getRedirectUrl());
            } else { // 获取支付链接失败
                log.error("微信支付官方返回的H5支付链接为空！");
                return Result.error("微信支付官方返回的H5支付链接为空！请联系管理人员！");
            }
        } catch (HttpException e) { // 发送HTTP请求失败
            HttpRequest httpRequest = e.getHttpRequest();
            log.error("向微信官方发送下单HTTP请求失败！详细信息为：{}", httpRequest.getBody());
            return Result.error("向微信官方发送下单HTTP请求失败！请联系管理人员！");
        } catch (ServiceException e) { // 服务返回状态小于200或大于等于300，例如500
            log.error("请求发送成功，服务返回异常！详细信息为：{}", e.getResponseBody());
            return Result.error("请求发送成功，服务返回异常！请联系管理人员！");
        } catch (MalformedMessageException e) { // 服务返回成功，返回体类型不合法，或者解析返回体失败
            log.error("服务返回成功，返回体类型不合法，或者解析返回体失败！报错信息为：{}", e.getMessage());
            return Result.error("微信官方返回的数据不合法！请联系管理人员！");
        } catch (ValidationException e) { // 发送HTTP请求成功，验证微信支付返回签名失败
            log.error("发送HTTP请求成功，验证微信支付返回签名失败！报错信息为：{}", e.getMessage());
            return Result.error("发送HTTP请求成功，验证微信支付返回签名失败！请联系管理人员！");
        } catch (Exception e) { // 其他异常
            log.error("获取调起支付的H5链接失败！", e);
            return Result.error("获取调起支付的H5链接失败！请联系管理人员！");
        }

        return Result.OK(returnData);
    }

    // 接收微信支付返回的支付信息，并开通会员
    @PostMapping("/wechatPayNotify")
    public ResponseEntity.BodyBuilder wechatPayNotify(HttpServletRequest request) {

        log.info("微信支付回调接口开始执行");

        // 从请求对象中获取指定参数

        // 获取原始请求体
        String requestBody;
        try {
            requestBody = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
        } catch (IOException e) {
            log.error("回调接口中，获取原始请求体失败！", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR);
        }

        // 应答的微信支付签名
        String paySignature = request.getHeader("Wechatpay-Signature");
        // 微信支付平台证书的序列号
        String paySerial = request.getHeader("Wechatpay-Serial");
        // 签名中的随机数
        String payNonce = request.getHeader("Wechatpay-Nonce");
        // 签名中的时间戳
        String payTimestamp = request.getHeader("Wechatpay-Timestamp");

        // 构造 RequestParam
        com.wechat.pay.java.core.notification.RequestParam requestParam = new com.wechat.pay.java.core.notification.RequestParam.Builder()
                .serialNumber(paySerial)
                .nonce(payNonce)
                .signature(paySignature)
                .timestamp(payTimestamp)
                .body(requestBody)
                .build();

        // 使用的是微信支付公私钥，则使用 RSAPublicKeyNotificationConfig
        NotificationConfig config = new RSAPublicKeyNotificationConfig.Builder()
                .publicKeyFromPath(properties.getPublicKeyPath())
                .publicKeyId(properties.getPublicKeyId())
                .apiV3Key(properties.getApiV3Key())
                .build();

        // 初始化 NotificationParser
        NotificationParser parser = new NotificationParser(config);

        try {
            // 以支付通知回调为例，验签、解密并转换成 Transaction
            Transaction transaction = parser.parse(requestParam, Transaction.class);

            if (transaction == null) {
                log.error("微信官方回调接口返回的结果为空！");
                // 向微信官方返回错误码
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR);
            }

            // 获取订单支付状态
            Transaction.TradeStateEnum tradeState = transaction.getTradeState();
            if (tradeState == null) {
                log.error("微信官方回调接口返回的订单支付状态为空");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR);
            }

            String outTradeNo = transaction.getOutTradeNo();
            String tradeStatus = tradeState.toString();

            // 获取订单号对应的未支付状态的订单
            Orders orders = ordersService.lambdaQuery()
                    .eq(Orders::getOrdersNum, outTradeNo)
                    .one();
            if (orders == null) {
                log.error("系统中找不到订单号outTradeNo：{}对应的订单", outTradeNo);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR);
            }

            if (orders.getStatus() != 0) {
                log.info("该订单对应的船员已开通会员，请勿重复开通（orderId：{}）", orders.getId());
                return ResponseEntity.status(HttpStatus.OK);
            }

            if (tradeState.equals(Transaction.TradeStateEnum.SUCCESS)) { // 支付成功
                // 开通会员
                ordersService.openMember(orders);
            } else {
                log.info("开通失败，微信支付返回的支付状态不是支付成功（订单id：{}, 微信支付支付状态：{}）",
                        orders.getId(), tradeStatus);
            }
        } catch (ValidationException e) {
            // 签名验证失败
            log.error("微信支付回调接口签名验证失败！");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR);
        }

        log.info("微信支付回调接口执行成功！");

        // 处理成功，返回 200 OK 状态码
        return ResponseEntity.status(HttpStatus.OK);
    }

    // 查询微信支付订单状态，并更新船员会员状态
    @GetMapping("/tradeQuery")
    public Result<?> tradeQuery(@RequestParam(name = "orderId") String orderId) {

        log.info("主动查询微信支付订单状态，并更新船员会员状态");

        Orders orders = ordersService.getById(orderId);
        if (orders == null) {
            log.error("id ={}的orders记录在数据库中不存在", orderId);
            return Result.error("查询失败！该订单不存在！");
        }
        if (orders.getOrdersNum() == null) {
            log.error("id ={}的orders记录的订单号不存在", orderId);
            return Result.error("查询失败！该订单的订单号不存在！");
        }
        if (orders.getStatus() != 0) {
            log.info("该订单对应的船员已开通会员，请勿重复开通（orderId：{}）", orders.getId());
            return Result.OK("该订单对应的船员已开通会员，请勿重复开通！");
        }

        // 构造查询必需参数
        QueryOrderByOutTradeNoRequest tradeNoRequest = new QueryOrderByOutTradeNoRequest();
        tradeNoRequest.setOutTradeNo(orders.getOrdersNum());
        tradeNoRequest.setMchid(properties.getMchid());

        try {
            // 获取查询结果
            Transaction transaction = h5Service.queryOrderByOutTradeNo(tradeNoRequest);
            if (transaction == null) {
                log.error("微信官方返回的查询结果为空，订单id：{}", orderId);
                return Result.error("查询失败！微信官方返回的查询结果为空");
            }

            // 获取订单支付状态
            Transaction.TradeStateEnum tradeState = transaction.getTradeState();
            if (tradeState == null) {
                log.error("微信官方返回的订单支付状态为空，订单id：{}", orderId);
                return Result.error("查询失败！微信官方返回的订单支付状态为空");
            }

            String tradeStatus = tradeState.toString();

            if (tradeState.equals(Transaction.TradeStateEnum.SUCCESS)) { // 支付成功
                // 开通会员
                ordersService.openMember(orders);
            } else {
                log.info("开通失败，微信支付返回的支付状态不是支付成功（订单id：{}, 微信支付支付状态：{}）",
                        orders.getId(), tradeStatus);
                return Result.error("开通失败，微信支付返回的支付状态不是支付成功");
            }
        } catch (HttpException e) { // 发送HTTP请求失败
            HttpRequest httpRequest = e.getHttpRequest();
            log.error("向微信官方发送下单HTTP请求失败！详细信息为：{}", httpRequest.getBody());
            return Result.error("向微信官方发送下单HTTP请求失败！请联系管理人员！");
        } catch (ServiceException e) { // 服务返回状态小于200或大于等于300，例如500
            log.error("请求发送成功，服务返回异常！详细信息为：{}", e.getResponseBody());
            return Result.error("请求发送成功，服务返回异常！请联系管理人员！");
        } catch (MalformedMessageException e) { // 服务返回成功，返回体类型不合法，或者解析返回体失败
            log.error("服务返回成功，返回体类型不合法，或者解析返回体失败！报错信息为：{}", e.getMessage());
            return Result.error("微信官方返回的数据不合法！请联系管理人员！");
        } catch (ValidationException e) { // 发送HTTP请求成功，验证微信支付返回签名失败
            log.error("发送HTTP请求成功，验证微信支付返回签名失败！报错信息为：{}", e.getMessage());
            return Result.error("发送HTTP请求成功，验证微信支付返回签名失败！请联系管理人员！");
        } catch (Exception e) { // 其他异常
            log.error("订单状态查询失败！", e);
            return Result.error("订单状态查询失败！请联系管理人员！");
        }

        return Result.OK("接口执行成功！");
    }

}
