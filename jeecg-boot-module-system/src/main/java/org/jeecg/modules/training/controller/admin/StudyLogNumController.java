package org.jeecg.modules.training.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.training.entity.StudyLogNum;
import org.jeecg.modules.training.service.IStudyLogNumService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: 学习日志数量关系表
 * @Author: hzk
 * @Date: 2022-09-02
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "学习日志数量关系表")
@RestController
@RequestMapping("/adm/studyLogNum")
public class StudyLogNumController extends JeecgController<StudyLogNum, IStudyLogNumService> {
    @Autowired
    private IStudyLogNumService studyLogNumService;

    /**
     * 分页列表查询
     *
     * @param studyLogNum
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "学习日志数量关系表-分页列表查询")
    @ApiOperation(value = "学习日志数量关系表-分页列表查询", notes = "学习日志数量关系表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(StudyLogNum studyLogNum,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<StudyLogNum> queryWrapper = QueryGenerator.initQueryWrapper(studyLogNum, req.getParameterMap());
        Page<StudyLogNum> page = new Page<StudyLogNum>(pageNo, pageSize);
        IPage<StudyLogNum> pageList = studyLogNumService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param studyLogNum
     * @return
     */
    @AutoLog(value = "学习日志数量关系表-添加")
    @ApiOperation(value = "学习日志数量关系表-添加", notes = "学习日志数量关系表-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody StudyLogNum studyLogNum) {
        studyLogNum.setDelFlag(CommonConstant.DEL_FLAG_0);
        studyLogNumService.save(studyLogNum);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param studyLogNum
     * @return
     */
    @AutoLog(value = "学习日志数量关系表-编辑")
    @ApiOperation(value = "学习日志数量关系表-编辑", notes = "学习日志数量关系表-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody StudyLogNum studyLogNum) {
        studyLogNum.setDelFlag(CommonConstant.DEL_FLAG_0);
        studyLogNumService.updateById(studyLogNum);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "学习日志数量关系表-通过id删除")
    @ApiOperation(value = "学习日志数量关系表-通过id删除", notes = "学习日志数量关系表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        studyLogNumService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "学习日志数量关系表-批量删除")
    @ApiOperation(value = "学习日志数量关系表-批量删除", notes = "学习日志数量关系表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        this.studyLogNumService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "学习日志数量关系表-通过id查询")
    @ApiOperation(value = "学习日志数量关系表-通过id查询", notes = "学习日志数量关系表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        StudyLogNum studyLogNum = studyLogNumService.getById(id);
        return Result.OK(studyLogNum);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param studyLogNum
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StudyLogNum studyLogNum) {
        return super.exportXls(request, studyLogNum, StudyLogNum.class, "学习日志数量关系表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StudyLogNum.class);
    }
}
