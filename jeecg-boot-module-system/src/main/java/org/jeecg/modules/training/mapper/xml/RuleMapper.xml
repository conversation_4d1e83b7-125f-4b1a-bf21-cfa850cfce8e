<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.RuleMapper">

  <sql id="where">
    <if test="mp.id != null and mp.id != ''">AND root.id=#{mp.id}</if>
    <if test="mp.name != null and mp.name != ''">AND root.name like concat("%", #{mp.name}, '%')</if>
    <if test="mp.type != null and mp.type != ''">AND root.type = #{mp.type}</if>
    <if test="mp.delFlag != null and mp.delFlag != ''">AND root.del_flag =#{mp.delFlag}
    </if>
  </sql>

  <select id="getAll" resultType="org.jeecg.modules.training.entity.Rule">
    SELECT root.* FROM rule root
    WHERE 1=1
    <include refid="where"/>
    ORDER BY root.create_time
  </select>
  <select id="getById" resultType="org.jeecg.modules.training.entity.Rule">
    SELECT *
    FROM rule
    WHERE id = #{id}
  </select>
</mapper>
