<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.TeamMapper">

  <select id="getTeamList" resultType="org.jeecg.modules.training.entity.Team">
    SELECT *
    FROM team ${ew.customSqlSegment}
  </select>

  <select id="pageAll" resultType="org.jeecg.modules.training.vo.TeamVO">
    SELECT *
    FROM (SELECT t.*, sd.depart_name
          FROM team t
               LEFT JOIN sys_depart sd ON sd.id = t.parent_id) AS t
      ${ew.customSqlSegment}
  </select>
</mapper>