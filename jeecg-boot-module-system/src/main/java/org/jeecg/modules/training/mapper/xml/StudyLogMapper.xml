<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.StudyLogMapper">

  <select id="getPageList" resultType="org.jeecg.modules.training.vo.StudyLogVO">
    SELECT *
    FROM (SELECT sl.*,
                 c.name  AS category_name,
                 s.name  AS study_name,
                 e.title AS exam_title,
                 t.realname,
                 t.identity_card,
                 t.phone,
                 te.team_name,
                 sd.depart_name,
                 t.post,
                 t.onboard_date,
                 sd.route,
                 t.work_no
          FROM study_log sl
               LEFT JOIN exam e ON sl.exam_id = e.id
               LEFT JOIN study s ON s.id = sl.study_id
               LEFT JOIN category c ON s.category_id = c.id
               LEFT JOIN trainee t ON t.id = sl.user_id
               LEFT JOIN team te ON te.id = sl.department
               LEFT JOIN sys_trainee_depart td ON td.trainee_id = t.id
               LEFT JOIN sys_depart sd ON td.dep_id = sd.id) study_log
      ${ew.customSqlSegment}
  </select>

  <select id="getStudyList" resultType="org.jeecg.modules.training.vo.StudyLogVO">
    SELECT *, SUM(use_time) AS use_time_total
    FROM (SELECT sl.*,
                 c.name AS category_name,
                 s.category_id,
                 t.realname,
                 t.identity_card,
                 t.phone,
                 te.team_name,
                 sd.depart_name,
                 t.post,
                 t.onboard_date,
                 sd.route,
                 t.work_no
          FROM study_log sl
               LEFT JOIN study s ON s.id = sl.study_id
               LEFT JOIN category c ON s.category_id = c.id
               LEFT JOIN trainee t ON t.id = sl.user_id
               LEFT JOIN team te ON te.id = t.team_id
               LEFT JOIN sys_trainee_depart td ON td.trainee_id = t.id
               LEFT JOIN sys_depart sd ON td.dep_id = sd.id
          WHERE t.id IS NOT NULL) study_log
      ${ew.customSqlSegment}
  </select>
  <select id="getAlreadyReadNumber" resultType="java.lang.Long">
    select count(1)
    from
    study_user_state sus LEFT JOIN study s ON s.id = sus.study_id
    where sus.user_id = #{userId}
    and sus.state = 1
    and s.del_flag = 0
    and s.disabled = false
    and s.category_id = #{categoryId}
    and (
    ((s.time_factor = 0 or s.time_factor is null or s.time_factor = ',任意,' or post_factor = ''
    or find_in_set(#{boardTime},s.time_factor))
    and
    (s.post_factor = 0 or post_factor is null or post_factor = '' or post_factor = '任意'
    or
    #{post} regexp concat(',' , replace(s.post_factor, ',', ',|,'),',')
    ))
    <if test="groupInfo!=null and groupInfo!= ''">
      or (s.group_factor is not null and s.group_factor &lt;&gt; '' and
      #{groupInfo} regexp concat(',' , replace(s.group_factor, ',', ',|,'), ',')
      )
    </if>
    )
    and s.need_read is not null
    and #{post} regexp concat(',' , replace(s.need_read, ',', ',|,'), ',')


  </select>
  <select id="getAlreadyAttentionNumber" resultType="java.lang.Long">
    select count(1)
    from
    study_user_state sus LEFT JOIN study s ON s.id = sus.study_id LEFT JOIN category c ON s.category_id = c.id
    where sus.user_id = #{userId}
    and (sus.state = 2 or sus.state = 3)
    and s.del_flag = 0
    AND c.del_flag = 0
    and s.disabled = false
    and c.route LIKE #{categoryRoute}
    and (
    (
    (s.time_factor = 0 or s.time_factor is null or s.time_factor = ',任意,' or post_factor = ''
    or find_in_set(#{boardTime},s.time_factor))
    and
    (s.post_factor = 0 or post_factor is null or post_factor = '' or post_factor = '任意'
    or #{post} regexp concat(',' , replace(s.post_factor, ',', ',|,'),','))
    )
    <if test="groupInfo!=null and groupInfo!= ''">
      or (s.group_factor is not null and s.group_factor &lt;&gt; '' and
      #{groupInfo} regexp concat(',' , replace(s.group_factor, ',', ',|,'), ',')
      )
    </if>
    )
    and s.need_attention is not null
    and #{post} regexp concat(',' , replace(s.need_attention, ',', ',|,'),',')
  </select>
  <select id="getOneMonthRecordList" resultType="org.jeecg.modules.training.vo.StudyLogOneMonth">
    SELECT *
    FROM (SELECT su.id, su.realname, su.identity_card, team.team_name, IF(ss.c IS NULL, 0, ss.c) AS `count`
          FROM trainee su
               LEFT JOIN sys_trainee_depart std ON su.id = std.trainee_id
               LEFT JOIN sys_depart sd ON std.dep_id = sd.id AND sd.route LIKE CONCAT(#{depRoute}, '%')
               LEFT JOIN team ON su.team_id = team.id
               LEFT JOIN
                         (SELECT user_id, dep_route, SUM(use_time) / 60 AS c
                          FROM study_log
                          WHERE dep_route LIKE CONCAT(#{depRoute}, '%')
                            AND DATE_SUB(CURDATE(), INTERVAL 1 MONTH) &lt;= DATE(create_time)
                          GROUP BY user_id, dep_route) ss ON su.id = ss.user_id
          WHERE identity_card IS NOT NULL
            AND su.del_flag = '0'
            AND su.status = 1
            AND LOWER(TRIM(realname)) NOT LIKE '%test%'
            AND LOWER(TRIM(realname)) NOT LIKE '%测试%'
          ORDER BY ss.c DESC) a ${ew.customSqlSegment}
  </select>
  <select id="getOneMonthRecordLisPage" resultType="org.jeecg.modules.training.vo.StudyLogOneMonth">
    SELECT *
    FROM (SELECT su.id, su.realname, su.identity_card, team.team_name, IF(ss.c IS NULL, 0, ss.c) AS `count`
          FROM trainee su
               LEFT JOIN team ON su.team_id = team.id
               LEFT JOIN sys_trainee_depart std ON su.id = std.trainee_id
               LEFT JOIN sys_depart sd ON std.dep_id = sd.id AND sd.route LIKE CONCAT(#{depRoute}, '%')
               LEFT JOIN
                         (SELECT user_id, dep_route, SUM(use_time) / 60 AS c
                          FROM study_log
                          WHERE dep_route LIKE CONCAT(#{depRoute}, '%')
                            AND DATE_SUB(CURDATE(), INTERVAL 1 MONTH) &lt;= DATE(create_time)
                          GROUP BY user_id, dep_route) ss ON su.id = ss.user_id
          WHERE identity_card IS NOT NULL
            AND su.del_flag = '0'
            AND su.status = 1
            AND LOWER(TRIM(realname)) NOT LIKE '%test%'
            AND LOWER(TRIM(realname)) NOT LIKE '%测试%'
          ORDER BY ss.c DESC) a ${ew.customSqlSegment}
  </select>
</mapper>
