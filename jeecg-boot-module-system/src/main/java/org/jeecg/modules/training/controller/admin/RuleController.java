package org.jeecg.modules.training.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.fields.FieldFilter;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.training.async.share.PushShareService;
import org.jeecg.modules.training.entity.Rule;
import org.jeecg.modules.training.service.IIndustryService;
import org.jeecg.modules.training.service.IRuleService;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 规则表
 * @Author: huazhengkna
 * @Date: 2022-05-04
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "规则表")
@RestController
@RequestMapping("/adm/rule")
public class RuleController extends JeecgController<Rule, IRuleService> {

    @Resource
    private ISyncStateService syncStateService;

    @Autowired
    private ISysDepartService sysDepartService;

    @Autowired
    private IIndustryService industryService;

    @Autowired
    private PushShareService pushShareService;


    /**
     * 分页列表查询
     *
     * @param rule
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "规则表-分页列表查询")
    @ApiOperation(value = "规则表-分页列表查询", notes = "规则表-分页列表查询")
    @GetMapping(value = "/list")
    @FieldFilter({"content", "createBy", "createTime", "delFlag", "depRoute", "id", "industryIds", "industryNames", "name", "shareMode", "updateBy", "updateTime"})
    public Result<?> queryPageList(Rule rule, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<Rule> queryWrapper = QueryGenerator.initQueryWrapper(rule, req.getParameterMap());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.and(q -> q.lambda().eq(Rule::getDepRoute, loginUser.getDepRoute()).or());

        Page<Rule> page = new Page<>(pageNo, pageSize);
        IPage<Rule> pageList = service.page(page, queryWrapper);

        // 设置返回的行业名称
        pageList.getRecords().forEach(item -> {
            item.setIndustryNames(industryService.getNamesByIndustryIds(item.getIndustryIds()));
        });
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param rule
     * @return
     */
    @AutoLog(value = "规则表-添加")
    @ApiOperation(value = "规则表-添加", notes = "规则表-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody Rule rule) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        rule.setDepRoute(loginUser.getDepRoute());

        rule.setDelFlag(CommonConstant.DEL_FLAG_0);
        rule.setIndustryIds(industryService.getIndustryIdsByNames(rule.getIndustryNames()));

        service.save(rule);

        //复制数据
        if ("admin".equals(loginUser.getUsername()) && StringUtils.isNotBlank(rule.getIndustryIds()) && "1".equals(rule.getShareMode())) {

            pushShareService.pushData("rule", rule.getId().toString());

        }
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param rule
     * @return
     */
    @AutoLog(value = "规则表-编辑")
    @ApiOperation(value = "规则表-编辑", notes = "规则表-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody Rule rule) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        rule.setDelFlag(CommonConstant.DEL_FLAG_0);

        //判断是否需要分享
        if (rule.getIndustryNames() != null && !rule.getIndustryNames().trim().isEmpty()) {
            rule.setIndustryIds(industryService.getIndustryIdsByNames(rule.getIndustryNames()));
        }

        service.updateById(rule);

        syncStateService.resetSyncState(CommonConstant.RULE, rule.getId().toString());

        //复制数据
        if ("admin".equals(loginUser.getUsername()) && "1".equals(rule.getShareMode())) {

            pushShareService.pushData("rule", rule.getId().toString());

        }
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "规则表-通过id删除")
    @ApiOperation(value = "规则表-通过id删除", notes = "规则表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        if (service.removeById(id)) {
            syncStateService.resetSyncState(CommonConstant.RULE, id);
            return Result.ok();
        }

        return Result.error("删除失败");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "规则表-批量删除")
    @ApiOperation(value = "规则表-批量删除", notes = "规则表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        if (ids == null || ids.trim().isEmpty()) {
            return Result.error("参数不识别！");
        }
        List<String> collect = Arrays.stream(ids.split(",")).distinct().collect(Collectors.toList());

        if (service.removeBatchByIds(collect)) {
            collect.forEach(id -> syncStateService.resetSyncState(CommonConstant.RULE, id));
            return Result.ok();
        }

        return Result.error("删除失败");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "规则表-通过id查询")
    @ApiOperation(value = "规则表-通过id查询", notes = "规则表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        Rule rule = service.getById(id);
        return Result.OK(rule);
    }

    /**
     * 获取所有规则下拉用
     *
     * @return
     */
    @FieldFilter({"content", "createBy", "createTime", "delFlag", "depRoute", "id", "industryIds", "name", "shareMode", "type", "updateBy", "updateTime"})
    @RequestMapping(value = "/queryall", method = RequestMethod.GET)
    public Result<Map<String, Object>> queryall() {
        Result<Map<String, Object>> result = new Result<>();
        Map<String, Object> map = new HashMap(5);
        LambdaQueryWrapper<Rule> wrapper = new LambdaQueryWrapper<>();

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        wrapper.and(q -> q.eq(Rule::getDepRoute, loginUser.getDepRoute()));


        wrapper.eq(Rule::getDelFlag, CommonConstant.DEL_FLAG_0);
        List<Rule> list = service.list(wrapper);
        if (list == null || list.size() <= 0) {
            result.error500("未找到规则");
        } else {
            map.put("list", list);
            result.setResult(map);
            result.setSuccess(true);
        }
        return result;
    }

    /**
     * 导出excel
     *
     * @param request
     * @param rule
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Rule rule) {
        return super.exportXls(request, rule, Rule.class, "规则表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Rule.class);
    }

    /**
     * 查询当前登录用户可获取的分享数据 shareModel = 2
     */
    @FieldFilter({"industryNames", "content", "createBy", "createTime", "delFlag", "depRoute", "id", "industryIds", "name", "shareMode"})
    @GetMapping("/getShareList")
    public Result<?> getSharePaperList(Rule rule, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<Rule> queryWrapper = QueryGenerator.initQueryWrapper(rule, req.getParameterMap());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        queryWrapper.in("share_mode", 1, 2);

        // 获取该部门所属行业的id集合
        String industryIds = sysDepartService.getDepartByOrgCode(loginUser.getOrgCode()).getIndustryIds();
        if (!industryIds.isEmpty()) {
            // 格式化行业id字符串
            industryIds = industryService.formatIndustryIdsExp(industryIds);
            queryWrapper.apply("industry_ids regexp {0}", industryIds);
        }

        Page<Rule> page = new Page<>(pageNo, pageSize);
        service.page(page, queryWrapper);

        return Result.ok(page);
    }


    /**
     * 设置用户选择的公共资料
     */
    @GetMapping("setShareRule")
    public Result<?> setShareRule(@RequestParam List<Long> ids) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 共享数据
        ids.forEach(item -> {
            service.shareRule(item, loginUser.getDepRoute(), true);
        });

        return Result.ok("获取成功");
    }

}
