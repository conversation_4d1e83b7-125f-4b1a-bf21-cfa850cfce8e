package org.jeecg.modules.training.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.training.entity.Invoice;
import org.jeecg.modules.training.entity.InvoiceApply;
import org.jeecg.modules.training.entity.Orders;
import org.jeecg.modules.training.vo.InvoiceApplyVO;

import java.util.List;


public interface IInvoiceApplyService extends IService<InvoiceApply> {


    Result<String> applyInvoice(InvoiceApply invoiceApply);

    Result<String> editInvoiceApply(InvoiceApply invoiceApply);

    Page<InvoiceApplyVO> pageQuery(Page<InvoiceApply> page, QueryWrapper<InvoiceApply> queryWrapper);

}
