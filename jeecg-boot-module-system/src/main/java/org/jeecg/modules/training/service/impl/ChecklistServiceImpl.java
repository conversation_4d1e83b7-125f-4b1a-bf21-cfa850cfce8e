package org.jeecg.modules.training.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.training.entity.Checklist;
import org.jeecg.modules.training.mapper.ChecklistMapper;
import org.jeecg.modules.training.service.IChecklistService;
import org.jeecg.modules.training.vo.ChecklistVO;
import org.springframework.stereotype.Service;

/**
 * @Description: 许可证和检查配置主表
 * @Author: jeecg-boot
 * @Date: 2022-10-23
 * @Version: V1.0
 */
@Service
public class ChecklistServiceImpl extends ServiceImpl<ChecklistMapper, Checklist> implements IChecklistService {

    @Override
    public IPage<ChecklistVO> listPages(Page<ChecklistVO> page, QueryWrapper<ChecklistVO> queryWrapper) {
        return baseMapper.listChecklists(page, queryWrapper);
    }

    @Override
    public IPage<ChecklistVO> findChecklists(Page<ChecklistVO> page, Long categoryId, String post) {
        return baseMapper.findChecklists(page, categoryId, post, null);
    }
}
