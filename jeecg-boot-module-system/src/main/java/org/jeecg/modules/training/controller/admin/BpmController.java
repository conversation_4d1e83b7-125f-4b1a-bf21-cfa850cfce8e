package org.jeecg.modules.training.controller.admin;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.impl.persistence.entity.DeploymentEntity;
import org.camunda.bpm.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.camunda.bpm.engine.impl.persistence.entity.TaskEntity;
import org.camunda.bpm.engine.repository.Deployment;
import org.camunda.bpm.engine.task.Comment;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.modules.camunda.mapper.ActHiTaskInstMapper;
import org.jeecg.modules.camunda.service.BpmService;
import org.jeecg.modules.camunda.vo.ProcessDefinitionInfo;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年11月05日 00:55
 */

@RestController
@RequestMapping("/adm/bpmn")
@Slf4j
public class BpmController {

    @Autowired
    private HistoryService historyService;

    @Autowired
    private ActHiTaskInstMapper actHiTaskInstMapper;

    @Autowired
    private TaskService taskService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private RepositoryService repositoryService;

    /**
     * 查询历史信息
     */
    @PostMapping("/queryHistory")
    public void queryHistory() {
        List<HistoricActivityInstance> list = historyService.createHistoricActivityInstanceQuery()
                .finished()
                .orderByHistoricActivityInstanceEndTime()
                .asc()
                .list();
        for (HistoricActivityInstance instance : list) {
            System.out.println(instance.getActivityId());
            System.out.println(instance.getProcessDefinitionKey());
            System.out.println(instance.getAssignee());
            System.out.println(instance.getStartTime());
            System.out.println(instance.getEndTime());
            System.out.println("=============================");
        }
    }

    /**
     * @Description: 流程定义部署
     */
    @PostMapping("/deployText")
    public Result<?> deploy(@RequestBody ProcessDefinitionInfo processDefinition) {
        //通过xmlText部署流程并添加创建用户id
        Deployment deployment = repositoryService.createDeployment()
                .name(processDefinition.getDefinitionName())
                .addString(processDefinition.getResourceName() + ".bpmn", processDefinition.getResource())
                .deploy();
        Map<Class<?>, List> deployedArtifacts = ((DeploymentEntity) deployment).getDeployedArtifacts();
        ProcessDefinitionEntity pde = (ProcessDefinitionEntity) deployedArtifacts.values().iterator().next().get(0);
        return Result.OK(pde.getId());
    }

    /**
     * @Description: 根据流程定义id部署
     */
    @PostMapping("/deploy/{id}")
    public Result<?> deploy(@PathVariable("id") String id) {
        return bpmService.deploy(id);
    }

    /**
     * 根据流程id获取审批记录
     *
     * @param processInstId
     */
    @AutoLog(value = "流程-根据流程id获取审批记录")
    @ApiOperation(value = "流程-根据流程id获取审批记录", notes = "根据流程id获取审批记录")
    @GetMapping("/approveHistory")
    public Result<List<Map<String, Object>>> approveHistory(@RequestParam("processInstId") String processInstId) {
        List<HistoricActivityInstance> list = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstId)
                .orderPartiallyByOccurrence()
                .asc().list();
        List<Map<String, Object>> result = new ArrayList<>(list.size());
        for (HistoricActivityInstance historicActivityInstance : list) {
            if (historicActivityInstance.getActivityId().endsWith("#multiInstanceBody") || "exclusiveGateway".equals(historicActivityInstance.getActivityType())
                    || "startEvent".equals(historicActivityInstance.getActivityId()) || "endEvent".equals(historicActivityInstance.getActivityId())) {
                continue;
            }
            Map<String, Object> map = new HashMap<>();
            String taskId = historicActivityInstance.getTaskId();

            //创建查询
            TaskEntity taskEntity = actHiTaskInstMapper.selectById(taskId);
            if (taskEntity != null && taskEntity.getDeleteReason() != null && taskEntity.getDeleteReason().equals("deleted")) {
                continue;
            }

            List<Comment> taskComments = taskService.getTaskComments(taskId);
            SysUser sysUser = sysUserService.getById(historicActivityInstance.getAssignee());
            if (sysUser != null) {
                map.put("assignee", sysUser.getRealname());
            } else {
                map.put("assignee", "");
            }
            map.put("state", taskEntity == null || taskEntity.getDeleteReason() == null ? "" : matchState(taskEntity.getDeleteReason()));
            map.put("startTime", DateFormatUtils.format(historicActivityInstance.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
            map.put("endTime", historicActivityInstance.getEndTime() != null ? DateFormatUtils.format(historicActivityInstance.getEndTime(), "yyyy-MM-dd HH:mm:ss") : "");
            map.put("activityName", historicActivityInstance.getActivityName());
            if (taskComments.size() > 0) {
                map.put("message", taskComments.get(0).getFullMessage());
            } else {
                map.put("message", "");
            }
            result.add(map);
        }
        return Result.OK(result);
    }

    private String matchState(String deleteReason) {
        String value = "";
        switch (deleteReason) {
            case TaskEntity.DELETE_REASON_COMPLETED:
                value = "通过";
                break;
            case TaskEntity.DELETE_REASON_DELETED:
                value = "取消";
                break;
            case CommonConstant.BPM_STATUS__CANCEL:
                value = "申请人取消";
                break;
            case CommonConstant.BPM_STATUS_REJECTED:
                value = "驳回";
                break;
            case CommonConstant.BPM_STATUS_REFUSE:
                value = "拒绝";
                break;
            case CommonConstant.BPM_STATUS_RETRIEVE:
                value = "撤回";
                break;
            case CommonConstant.BPM_STATUS_FOLLOWING:
                value = "进行中";
                break;
            case CommonConstant.BPM_STATUS_DRAFT:
                value = "草稿";
                break;
            default:
                value = "";
                break;
        }
        return value;
    }

    /**
     * @Description: 驳回
     */
    @GetMapping("/rollbackProcess")
    public Result<?> rollbackProcess(@RequestParam("rejectType") Integer rejectType,
                                     @RequestParam("processInstId") String processInstId,
                                     @RequestParam("taskId") String taskId, String reason) throws Exception {
        bpmService.rollbackProcess(rejectType, processInstId, taskId, reason, true);
        return Result.OK("驳回成功");
    }
}
