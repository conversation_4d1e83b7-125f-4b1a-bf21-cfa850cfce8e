package org.jeecg.modules.training.async.phase;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.config.init.SpringBeanUtil;
import org.jeecg.modules.training.async.phase.impl.CheckUserPhaseHandler;
import org.jeecg.modules.training.async.phase.impl.PushUserPhaseHandler;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.service.TraineeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年12月14日
 */
@Slf4j
@Component
public class PushPhaseService {

    @Autowired(required = false)
    @Lazy
    private TraineeService traineeService;

    private final ConcurrentLinkedQueue<PushPhaseHandler> pushHandlers = new ConcurrentLinkedQueue<>();

    private final Object syncPushLocker = new Object();

    private Thread handlerPushThread;

    private volatile Boolean running = false;

    private Date lastPushAllTime = new Date();

    public PushPhaseService() {
    }

    public void start() {

        running = true;

        handlerPushThread = new Thread("PushUserPhase Thread") {
            @Override
            public void run() {
                try {

                    while (running) {

                        try {

                            PushPhaseHandler handler;
                            while ((handler = pushHandlers.poll()) != null) {
                                try {
                                    handler.run();
                                } catch (Exception ex) {
                                    log.error("EXP", ex);
                                }
                            }
                        } catch (Exception ex) {
                            log.error("EXP", ex);
                        }

                        synchronized (syncPushLocker) {
                            syncPushLocker.wait();
                        }

                    }
                } catch (Exception ex) {
                    log.error("EXP", ex);
                }
                log.info("PushUserPhase Thread stoped");
            }
        };

        handlerPushThread.start();

        log.info("PushPaperService started");

        //启动便推送一次,界面上有触发按钮，改成手动
        //pushAll();

    }

    public void close() {

        running = false;

        synchronized (syncPushLocker) {
            syncPushLocker.notifyAll();
        }

        try {
            handlerPushThread.join();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        handlerPushThread = null;

        log.info("PushPaperService Thread stoped");
    }

    //[秒] [分] [小时] [日] [月] [周] [年]
    @Scheduled(cron = "0 30 0 * * ?")
    public void pushAll() {
        log.info("push all");
        pushAll(false);
    }

    public boolean pushAll(boolean checkRepeatTime) {

        if (checkRepeatTime && (new Date().getTime() - lastPushAllTime.getTime()) < 60 * 1000) {
            log.info("push all fail interval too small");
            return false;
        }

        List<Trainee> traineeList = traineeService.getAllExamUserForPhase(new QueryWrapper<>()).stream().map(t -> (Trainee) t).collect(Collectors.toList());
        return pushUsers(traineeList, checkRepeatTime);
    }

    public boolean pushData(Long dataId, boolean checkRepeatTime) {

        if (checkRepeatTime && (new Date().getTime() - lastPushAllTime.getTime()) < 60 * 1000) {
            return false;
        }

        List<Trainee> userList = traineeService.getAllExamUserForPhase(new QueryWrapper<>()).stream().map(t -> (Trainee) t).collect(Collectors.toList());
        return pushUsers(userList, dataId, checkRepeatTime);
    }
    public boolean pushUsers(List<Trainee> traineeList) {
        return pushUsers(traineeList, false);
    }

    public boolean pushUsers(List<Trainee> traineeList, boolean checkRepeatTime){
        return pushUsers(traineeList, null, checkRepeatTime);
    }    

	public boolean pushUsers(List<Trainee> traineeList,Long dataId, boolean checkRepeatTime) {
        if (checkRepeatTime && (new Date().getTime() - lastPushAllTime.getTime()) < 60 * 1000) {
            return false;
        }

        lastPushAllTime = new Date();

        PushUserPhaseHandler pushHandler = SpringBeanUtil.getBean(PushUserPhaseHandler.class);
        pushHandler.setTraineeList(traineeList);
        pushHandler.setDataId(dataId);

        CheckUserPhaseHandler checkHandler = SpringBeanUtil.getBean(CheckUserPhaseHandler.class);
        checkHandler.setTraineeList(traineeList);
        checkHandler.setDataId(dataId);

        synchronized (pushHandlers) {
            pushHandlers.add(pushHandler);
            pushHandlers.add(checkHandler);
        }

        notifyPush();

        return true;
    }

    //处理用户的待办事项
    public void checkAll() {
        log.info("check all");
        List<Trainee> traineeList = traineeService.getAllExamUserForPhase(new QueryWrapper<>())
                .stream().map(t -> (Trainee) t).collect(Collectors.toList());
        checkTrainee(traineeList, null, null);
    }

    //处理用户的待办事项
    public void checkTrainee(Trainee trainee) {
        checkTrainee(trainee, null, null);
    }
    
    public void checkTrainee(Trainee trainee, Integer dataType, Long dataId) {
        List<Trainee> traineeList = Collections.singletonList(trainee);
        checkTrainee(traineeList, dataType, dataId);
    }

    //处理用户的待办事项
    public void checkTrainee(List<Trainee> traineeList) {
        checkTrainee(traineeList, null, null);
    }

    //处理用户的待办事项
    /*
     * 这段代码的作用是创建一个处理用户阶段检查的处理器实例，并将其添加到处理器列表中，
     * 最后通知处理逻辑有新的处理器需要处理。
     *
     * */
    public void checkTrainee(List<Trainee> traineeList, Integer dataType, Long dataId) {
        CheckUserPhaseHandler handler = SpringBeanUtil.getBean(CheckUserPhaseHandler.class);
        handler.setTraineeList(traineeList);
        synchronized (pushHandlers) {
            pushHandlers.add(handler);
        }
        notifyPush();
    }

    private void notifyPush() {
        synchronized (syncPushLocker) {
            syncPushLocker.notifyAll();
        }
    }

    public boolean pushTrainee(List<Trainee> traineeList) {
        return pushTrainee(traineeList, false);
    }

    public boolean pushTrainee(List<Trainee> traineeList, boolean checkRepeatTime) {
        if (checkRepeatTime && (new Date().getTime() - lastPushAllTime.getTime()) < 60 * 1000) {
            return false;
        }

        lastPushAllTime = new Date();

        PushUserPhaseHandler pushHandler = SpringBeanUtil.getBean(PushUserPhaseHandler.class);
        pushHandler.setTraineeList(traineeList);

        CheckUserPhaseHandler checkHandler = SpringBeanUtil.getBean(CheckUserPhaseHandler.class);
        checkHandler.setTraineeList(traineeList);

        synchronized (pushHandlers) {
            pushHandlers.add(pushHandler);
            pushHandlers.add(checkHandler);
        }

        notifyPush();

        return true;
    }
	
}
