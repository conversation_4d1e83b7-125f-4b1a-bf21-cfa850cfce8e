package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.Date;

@Data
public class Packages {

    @TableId(type = IdType.AUTO)
    private Long id;

    // 套餐名称
    private String name;

    // 所属公司
    private String sysDepartId;

    // 过滤路径
    private String depRoute;

    // 会员数量
    private Integer memberNum;

    // 剩余数量
    private Integer surplusNum;

    // 到期时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private LocalDate expireDate;

    // 套餐状态，0：禁用；1：启用（默认值）
    private Integer status;

    // 创建时间
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    // 更新时间
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    // 删除标记 0 未删除（默认值），1 已删除
    @TableLogic
    private Integer delFlag;

    // 套餐类型 0：个人付费套餐（默认值），1：企业付费套餐
    private Integer type;

    // 船员id
    private String traineeId;

    // 订单Id
    private Long ordersId;

    // 套餐开始时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private LocalDate startTime;

    // 公司名称
    private String departName;

    // 套餐类型, 1:基础版, 2:标准版, 3:尊享版
    private Short versionType;

    /**
     * 判断套餐是否已过期
     * 当前日期大于结束日期时认为已过期
     *
     * @return true: 已过期, false: 未过期
     */
    public boolean isExpired() {
        if (expireDate == null) {
            return false;
        }
        return LocalDate.now().isAfter(expireDate);
    }

    /**
     * 判断套餐是否有效
     * 当前日期在起止时间范围内时认为有效
     *
     * @return true: 有效, false: 无效
     */
    public boolean isValid() {
        if (startTime == null || expireDate == null) {
            return false;
        }
        LocalDate today = LocalDate.now();
        return !today.isBefore(startTime) && !today.isAfter(expireDate);
    }
}
