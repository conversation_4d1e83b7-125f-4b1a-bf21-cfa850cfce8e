package org.jeecg.modules.training.async.facial;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.training.async.facial.impl.VideoFacialHandler;
import org.jeecg.modules.training.async.facial.impl.VideoTools;
import org.jeecg.modules.training.entity.Exam;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedDeque;

/**
 * 人脸识别对比服务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年09月22日 13:06
 */
@Slf4j
@Component
public class FacialHandlerService implements ApplicationContextAware {

    private final Object syncLocker = new Object();

    private ApplicationContext applicationContext;


    private Thread handlerThread;

    private volatile Boolean running = false;

    private final Queue<FacialHandler> facialHandlers = new ConcurrentLinkedDeque<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public FacialHandlerService() {
        VideoTools.checkTools();
    }

    public void start() {
        if (handlerThread != null) {
            return;
        }

        running = true;
        handlerThread = new Thread("FacialHandlerService Thread") {
            @Override
            public void run() {
                try {
                    while (running) {
                        try {
                            if (facialHandlers.isEmpty()) {
                                synchronized (syncLocker) {
                                    syncLocker.wait();
                                }
                            }

                            if (!running) {
                                break;
                            }

                            FacialHandler item;
                            while ((item = facialHandlers.poll()) != null) {
                                try {
                                    item.run();
                                } catch (Exception ex) {
                                    log.error("EXP", ex);
                                }
                                if (!running) {
                                    break;
                                }
                            }

                        } catch (InterruptedException e) {
                            break;
                        } catch (Exception ex) {
                            log.error("EXP", ex);
                        }
                    }
                } catch (Exception ex) {
                    log.error("EXP", ex);
                }
                log.info("FacialHandlerService Thread STOP");
            }
        };
        handlerThread.start();
        log.info("FacialHandlerService started");
    }

    public void close() {
        if (handlerThread == null) {
            return;
        }
        log.info("FacialHandlerService Thread STOP");
        running = false;
        synchronized (syncLocker) {
            syncLocker.notifyAll();
        }
        try {
            handlerThread.join();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        handlerThread = null;
        log.info("FacialHandlerService Thread STOP");
    }

    /*public boolean beginHandleFacial(Exam exam, List paths) {
        if (handlerThread == null || handlerThread.isInterrupted()) {
            log.error("FacialHandlerService is stopping/stopped, cannot handle");
            return false;
        }
        boolean handled = true;
        try {
            VideoFacialHandler bean = applicationContext.getBean(VideoFacialHandler.class);
            bean = bean.withExam(exam)
                    .withVideoPaths(paths);
            facialHandlers.add(bean);
        } catch (Exception ex) {
            log.error("EXP", ex);
            handled = false;
        }
        try {
            if (handled) {
                synchronized (syncLocker) {
                    syncLocker.notifyAll();
                }
            }
        } catch (Exception ex) {
            log.error("EXP", ex);
        }
        return handled;
    }*/

    public boolean beginHandleFacial(Exam exam) {
        if (handlerThread == null || handlerThread.isInterrupted()) {
            log.error("FacialHandlerService is stopping/stopped, cannot handle");
            return false;
        }
        boolean handled = true;
        try {
            VideoFacialHandler bean = applicationContext.getBean(VideoFacialHandler.class);
            bean = bean.withExam(exam);
            facialHandlers.add(bean);
        } catch (Exception ex) {
            log.error("EXP", ex);
            handled = false;
        }
        try {
            if (handled) {
                synchronized (syncLocker) {
                    syncLocker.notifyAll();
                }
            }
        } catch (Exception ex) {
            log.error("EXP", ex);
        }
        return handled;
    }
}
