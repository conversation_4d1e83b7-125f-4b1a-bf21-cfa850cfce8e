package org.jeecg.modules.training.async.report.service.impl;

import com.aspose.words.Bookmark;
import com.aspose.words.CompositeNode;
import com.aspose.words.Document;
import com.aspose.words.DocumentBuilder;
import com.aspose.words.Font;
import com.aspose.words.Node;
import com.aspose.words.NodeCollection;
import com.aspose.words.NodeType;
import com.aspose.words.Run;
import org.jeecg.modules.training.async.report.model.DetailExamBaseVO;
import org.jeecg.modules.training.async.report.service.IBookmarkHandler;
import org.jeecg.modules.training.async.report.service.IFieldValueFormatter;
import org.jeecg.modules.training.async.report.service.IValueProvider;

public class TextBookmarkHandler implements IBookmarkHandler {
    private final IValueProvider valueProvider;
    private final IFieldValueFormatter valueFormatter;
    
    public TextBookmarkHandler(IValueProvider valueProvider, IFieldValueFormatter valueFormatter) {
        this.valueProvider = valueProvider;
        this.valueFormatter = valueFormatter;
    }
    
    @Override
    public void process(Bookmark bookmark, DetailExamBaseVO detail, Document document) throws Exception {
        Object value = valueProvider.getValue(detail);
        String formattedValue = valueFormatter.format(bookmark.getName(), value);
        
        // 获取书签范围内的所有节点
        @SuppressWarnings("unchecked")
        NodeCollection<Node> nodes = bookmark.getBookmarkStart().getParentNode()
                .getChildNodes(NodeType.ANY, true);
        
        // 找到开始和结束位置
        int startIndex = nodes.indexOf(bookmark.getBookmarkStart());
        int endIndex = nodes.indexOf(bookmark.getBookmarkEnd());
        
        // 保存第一个Run的格式
        Run firstRun = null;
        Font originalFont = null;
        
        // 查找第一个Run并保存其格式
        for (int i = startIndex + 1; i < endIndex; i++) {
            Node node = nodes.get(i);
            if (node.getNodeType() == NodeType.RUN) {
                firstRun = (Run)node;
                originalFont = firstRun.getFont();
                break;
            }
        }
        
        if (firstRun != null) {
            // 清除书签范围内的所有Run
            for (int i = startIndex + 1; i < endIndex; i++) {
                Node node = nodes.get(i);
                if (node.getNodeType() == NodeType.RUN) {
                    ((Run)node).setText("");
                }
            }
            
            // 使用DocumentBuilder在正确的位置插入新文本
            DocumentBuilder builder = new DocumentBuilder(document);
            builder.moveTo(firstRun);
            
            // 应用原始格式
            Font builderFont = builder.getFont();
            builderFont.setName(originalFont.getName());
            builderFont.setSize(originalFont.getSize());
            builderFont.setBold(originalFont.getBold());
            builderFont.setItalic(originalFont.getItalic());
            
            // 插入新文本
            builder.write(formattedValue);
            
            // 删除空的Run
            firstRun.remove();
        } else {
            // 如果没有找到Run节点，创建新的Run
            @SuppressWarnings("rawtypes")
            CompositeNode parent = (CompositeNode)bookmark.getBookmarkStart().getParentNode();
            Run newRun = new Run(document);
            newRun.setText(formattedValue);
            parent.insertAfter(newRun, bookmark.getBookmarkStart());
        }
    }
} 