package org.jeecg.modules.training.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.training.entity.ChecklistTaskItem;
import org.jeecg.modules.training.vo.ChecklistTaskItemVO;

import java.util.List;

/**
 * @Description: 许可证和检查实例子表
 * @Author: jeecg-boot
 * @Date: 2022-10-23
 * @Version: V1.0
 */
public interface IChecklistTaskItemService extends IService<ChecklistTaskItem> {

    /**
     * 保存子表数据
     *
     * @param checklistItems
     */
    Boolean saveEntity(List<ChecklistTaskItemVO> checklistItems);
}
