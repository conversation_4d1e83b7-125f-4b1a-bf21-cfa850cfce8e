package org.jeecg.modules.training.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.training.entity.Rule;

import java.util.List;
import java.util.Map;

/**
 * @Description: 规则表
 * @Author: huazhengkna
 * @Date: 2022-05-04
 * @Version: V1.0
 */
public interface IRuleService extends IService<Rule> {

    /**
     * 逻辑删除
     *
     * @param id
     */
    Result<Rule> deleteById(String id);

    /**
     * 逻辑批量删除
     *
     * @param ids
     */
    Result<Rule> deleteBatch(List<String> ids);

    /**
     * 根据 ruleIds查询，查询规则名称（多个规则名称名逗号隔开）
     *
     * @param ruleIds
     * @return
     */
    Map<Long, String> getNamesByRuleIds(List<Long> ruleIds);

    /**
     * 查询list
     *
     * @param page
     * @param params
     * @return
     */
    IPage<Rule> getList(Page<Rule> page, Map<String, Object> params);

    Long shareRule(Long id, String route, boolean force);
}
