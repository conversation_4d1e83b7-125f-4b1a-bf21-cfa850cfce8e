package org.jeecg.modules.training.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.training.entity.StudyTimePerDepartment;
import org.jeecg.modules.training.vo.StudyTimePerDepartmentVO;

/**
 * 每个学习资料船舶每日时长表(StudyTimePerDepartment)表服务接口
 *
 * <AUTHOR>
 * @since 2023-12-28 15:39:49
 */
public interface StudyTimePerDepartmentService extends IService<StudyTimePerDepartment> {

    Page<StudyTimePerDepartmentVO> getPageList(Page<StudyTimePerDepartmentVO> page, String depRoute, QueryWrapper<StudyTimePerDepartmentVO> queryWrapper, String scope);

    Page<StudyTimePerDepartmentVO> completeRate(Page<StudyTimePerDepartmentVO> page, String depRoute, QueryWrapper<StudyTimePerDepartmentVO> queryWrapper, String scope);
}

