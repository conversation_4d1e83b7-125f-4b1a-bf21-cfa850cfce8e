package org.jeecg.modules.training.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.training.entity.Team;
import org.jeecg.modules.training.vo.TeamVO;

import java.util.List;

public interface TeamService extends IService<Team> {

    /**
     * 根据船名和部门路径查询
     *
     * @param departNam 船名
     * @param route     部门路径
     * @return
     */
    Team queryTeamByName(String teamName, String route);

    Team saveTeamData(Team team, String depRoute);

    /**
     * 查询船列表
     *
     * @param queryWrapper
     * @return
     */
    List<Team> getTeamList(QueryWrapper<Team> queryWrapper);

    /**
     * 获取我的部门下级所有部门
     *
     * @param parentId 父id
     * @param ids      多个部门id
     * @return
     */
    List<Team> queryTreeListByPid(String parentId, String ids, String searchKey);

    Page<TeamVO> pageAll(Page<TeamVO> page, QueryWrapper<TeamVO> queryWrapper);
}
