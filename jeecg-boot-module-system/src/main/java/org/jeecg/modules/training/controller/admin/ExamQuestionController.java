package org.jeecg.modules.training.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.fields.FieldFilter;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.training.entity.ExamQuestion;
import org.jeecg.modules.training.service.IExamQuestionService;
import org.jeecg.modules.training.vo.ExamQuestionVO;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * @Description: 考卷题目
 * @Author: jeecg-boot
 * @Date: 2022-05-04
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "考卷题目")
@RestController
@RequestMapping("/adm/examQuestion")
public class ExamQuestionController extends JeecgController<ExamQuestion, IExamQuestionService> {

    /**
     * 分页列表查询
     *
     * @param examQuestion
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "考卷题目-分页列表查询")
    @ApiOperation(value = "考卷题目-分页列表查询", notes = "考卷题目-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ExamQuestion examQuestion,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<ExamQuestion> queryWrapper = QueryGenerator.initQueryWrapper(examQuestion, req.getParameterMap());
        Page<ExamQuestion> page = new Page<ExamQuestion>(pageNo, pageSize);
        IPage<ExamQuestion> pageList = service.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param examQuestion
     * @return
     */
    @AutoLog(value = "考卷题目-添加")
    @ApiOperation(value = "考卷题目-添加", notes = "考卷题目-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ExamQuestion examQuestion) {
        service.save(examQuestion);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param examQuestion
     * @return
     */
    @AutoLog(value = "考卷题目-编辑")
    @ApiOperation(value = "考卷题目-编辑", notes = "考卷题目-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody ExamQuestion examQuestion) {
        service.updateById(examQuestion);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "考卷题目-通过id删除")
    @ApiOperation(value = "考卷题目-通过id删除", notes = "考卷题目-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        service.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "考卷题目-批量删除")
    @ApiOperation(value = "考卷题目-批量删除", notes = "考卷题目-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        this.service.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "考卷题目-通过id查询")
    @ApiOperation(value = "考卷题目-通过id查询", notes = "考卷题目-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        ExamQuestion examQuestion = service.getById(id);
        return Result.OK(examQuestion);
    }

    /**
     * 通过考卷id获取详情
     *
     * @param examQuestion
     * @return
     */
    @AutoLog(value = "通过考卷id获取详情")
    @ApiOperation(value = "通过考卷id获取详情", notes = "通过考卷id获取详情")
    @GetMapping(value = "/getAll")
    public Result<?> getAll(ExamQuestionVO examQuestion) {
        return service.getAll(examQuestion);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param examQuestion
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ExamQuestion examQuestion) {
        return super.exportXls(request, examQuestion, ExamQuestion.class, "考卷题目");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ExamQuestion.class);
    }

    @FieldFilter({"name", "value"})
    @ApiOperation("获取心理健康异常因子分布")
    @GetMapping("/getFactorRate")
    public Result<List<HashMap<String, Integer>>> getFactorRate() {
        List<HashMap<String, Integer>> factorRate = service.getFactorRate();
        return Result.ok(factorRate);
    }

}
