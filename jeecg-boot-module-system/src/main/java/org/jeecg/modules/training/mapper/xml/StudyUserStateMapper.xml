<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.StudyUserStateMapper">

  <select id="hasState" resultType="java.lang.Integer">
    SELECT count(*)
    FROM study_user_state
    WHERE user_id = #{userId}
      AND study_id = ${studyId}
      AND state = ${state}
  </select>

  <select id="getUserAttentionState" resultType="org.jeecg.modules.training.entity.StudyUserState">
    SELECT *
    FROM study_user_state
    WHERE user_id = #{userId}
      AND study_id = ${studyId}
      AND (state = 2 OR state = 3)
    LIMIT 1
  </select>

  <select id="getUserReadState" resultType="org.jeecg.modules.training.entity.StudyUserState">
    SELECT *
    FROM study_user_state
    WHERE user_id = #{userId}
      AND study_id = ${studyId}
      AND state = 1
    LIMIT 1
  </select>

</mapper>
