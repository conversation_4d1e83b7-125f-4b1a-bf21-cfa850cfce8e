package org.jeecg.modules.training.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.modules.training.entity.Packages;
import org.jeecg.modules.training.mapper.PackagesMapper;
import org.jeecg.modules.training.service.IPackagesService;
import org.jeecg.modules.training.vo.PackagesVO;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
public class PackagesServiceImpl extends ServiceImpl<PackagesMapper, Packages> implements IPackagesService {

    @Autowired
    private ISyncStateService syncStateService;

    // 获取指定公司的所有可用企业套餐，最先过期的排在前面
    @Override
    public List<Packages> getAllAvailable(String sysDepartId) {
        return lambdaQuery()
                .eq(Packages::getSysDepartId, sysDepartId)
                .eq(Packages::getStatus, 1)
                .eq(Packages::getType, 1)
                .orderByAsc(Packages::getExpireDate)
                .list();
    }

    // 套餐剩余数量扣除 扣除返回套餐id，否则返回null
    @Override
    public Long deductSurplusNum(List<Packages> packagesList) {

        return packagesList.stream()
                .filter(pkg -> pkg.getSurplusNum() > 0)
                .findFirst()
                .map(pkg -> { // 套餐有余量，扣除
                    lambdaUpdate()
                            .setSql("surplus_num = surplus_num - 1")
                            .eq(Packages::getId, pkg.getId())
                            .update();
                    // 修改同步状态
                    syncStateService.resetSyncState(CommonConstant.PACKAGES, String.valueOf(pkg.getId()));
                    return pkg.getId();
                })
                .orElse(null);
    }

    // 分页列表查询
    @Override
    public IPage<PackagesVO> listPages(Page<PackagesVO> page, QueryWrapper<PackagesVO> queryWrapper) {
        return baseMapper.listPapersPages(page, queryWrapper);
    }
}
