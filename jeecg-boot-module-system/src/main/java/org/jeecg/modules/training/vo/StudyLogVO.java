package org.jeecg.modules.training.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.modules.training.entity.StudyLog;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 用于前后台交互的数据
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年09月10日 07:02
 */
@Data
public class StudyLogVO extends StudyLog {

    /**
     * 员工工号
     */
    @Excel(name = "编号", width = 15)
    private String workNo;

    /**
     * 用户姓名
     */
    @Excel(name = "用户姓名", width = 15)
    private String realname;


    /**
     * 职位
     */
    private String post;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上船时间", width = 20, format = "yyyy-MM-dd")
    Date onboardDate;

    /**
     * 学习名称
     */
    @Excel(name = "学习名称", width = 15)
    private String studyName;

    /**
     * 船名
     */
    @Excel(name = "船名", width = 15)
    private String teamName;

    /**
     * 分类名称
     */
    @Excel(name = "分类", width = 15)
    private String categoryName;

    /**
     * 阅读类型：0：已阅读，1：关注, 2：无关
     */
    @Excel(name = "结果", width = 15)
    private String result;


    /**
     * 考试标题
     */
    //@Excel(name = "考卷名称", width = 15)
    private String examTitle;


    /**
     * 部门路径
     */
    private String route;

    /**
     * 各分类总时间
     */
    @Excel(name = "学习时间", width = 15)
    private Long useTimeTotal;

    /**
     * 关联行业名称集合
     */
    private String industryNames;

    // 学习资料总学习时间
    private Long totalTime;
}
