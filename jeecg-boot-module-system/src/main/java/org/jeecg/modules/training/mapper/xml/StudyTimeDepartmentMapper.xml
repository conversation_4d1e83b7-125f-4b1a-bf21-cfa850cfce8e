<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.StudyTimeDepartmentMapper">

  <select id="getPageList" resultType="org.jeecg.modules.training.vo.StudyTimeDepartmentVO">
      select * from (
      select ifnull(sum(std.study_time) /
      (select count(*) from trainee te where te.team_id = sd.id),0) as study_time,
      sd.team_name as depart_name
      from team sd
      left join study_time_department std on std.department_id = sd.id
      <if test="scope == 'week'">
          and WEEK(std.date, 1) = WEEK(now(), 1)
          and YEAR(std.date) = YEAR(CURDATE())
      </if>
      <if test="scope == 'month'">
          and MONTH(std.date) = MONTH(NOW())
          and YEAR(std.date) = YEAR(CURDATE())
      </if>
      where
      sd.route like concat(#{depRoute},'%')
      group by sd.id
      ) as t
      ${ew.customSqlSegment}

  </select>

</mapper>

