package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 用户导入历史表
 * @Author: hzk
 * @Date: 2022-06-10
 * @Version: V1.0
 */
@Data
@TableName("sys_user_log")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "sys_user_log对象", description = "用户导入历史表")
public class SysUserLog {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键id")
    private Long id;
    /**
     * 用户表id
     */
    @Excel(name = "用户表id", width = 15)
    @ApiModelProperty(value = "用户表id")
    private String userId;
    /**
     * 登录账号
     */
    @Excel(name = "登录账号", width = 15)
    @ApiModelProperty(value = "登录账号")
    private String username;
    /**
     * 真实姓名
     */
    @Excel(name = "真实姓名", width = 15)
    @ApiModelProperty(value = "真实姓名")
    private String realname;
    /**
     * 密码
     */
    @Excel(name = "密码", width = 15)
    @ApiModelProperty(value = "密码")
    private String password;
    /**
     * 身份证
     */
    @Excel(name = "身份证", width = 15)
    @ApiModelProperty(value = "身份证")
    private String identityCard;
    /**
     * md5密码盐
     */
    @Excel(name = "md5密码盐", width = 15)
    @ApiModelProperty(value = "md5密码盐")
    private String salt;
    /**
     * 上船日期
     */
    @Excel(name = "上船日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "上船日期")
    private Date onboardDate;
    /**
     * 头像
     */
    @Excel(name = "头像", width = 15)
    @ApiModelProperty(value = "头像")
    private String avatar;
    /**
     * 生日
     */
    @Excel(name = "生日", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生日")
    private Date birthday;
    /**
     * 性别(0-默认未知,1-男,2-女)
     */
    @Excel(name = "性别", width = 15)
    @ApiModelProperty(value = "性别")
    private Integer sex;
    /**
     * 电子邮件
     */
    @Excel(name = "电子邮件", width = 15)
    @ApiModelProperty(value = "电子邮件")
    private String email;
    /**
     * 电话
     */
    @Excel(name = "电话", width = 15)
    @ApiModelProperty(value = "电话")
    private String phone;
    /**
     * 机构编码
     */
    @Excel(name = "机构编码", width = 15)
    @ApiModelProperty(value = "机构编码")
    private String orgCode;
    /**
     * 状态(1-正常,2-冻结)
     */
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private Integer status;
    /**
     * 删除状态(0-正常,1-已删除)
     */
    @Excel(name = "删除状态", width = 15)
    @ApiModelProperty(value = "删除状态")
    private Integer delFlag;
    /**
     * 第三方登录的唯一标识
     */
    @Excel(name = "第三方登录的唯一标识", width = 15)
    @ApiModelProperty(value = "第三方登录的唯一标识")
    private String thirdId;
    /**
     * 第三方类型
     */
    @Excel(name = "第三方类型", width = 15)
    @ApiModelProperty(value = "第三方类型")
    private String thirdType;
    /**
     * 工号，唯一键
     */
    @Excel(name = "工号，唯一键", width = 15)
    @ApiModelProperty(value = "工号，唯一键")
    private String workNo;
    /**
     * 职务，关联职务表
     */
    @Excel(name = "职务，关联职务表", width = 15)
    @ApiModelProperty(value = "职务，关联职务表")
    private String post;
    /**
     * 座机号
     */
    @Excel(name = "座机号", width = 15)
    @ApiModelProperty(value = "座机号")
    private String telephone;
    /**
     * 导入时间
     */
    @Excel(name = "导入时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "导入时间")
    private Date importTime;
    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @Excel(name = "更新人", width = 15)
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 身份（1普通成员 2上级）
     */
    @Excel(name = "身份（1普通成员 2上级）", width = 15)
    @ApiModelProperty(value = "身份（1普通成员 2上级）")
    private Integer userIdentity;
    /**
     * 负责部门
     */
    @Excel(name = "负责部门", width = 15)
    @ApiModelProperty(value = "负责部门")
    private Object departIds;
    /**
     * 多租户标识
     */
    @Excel(name = "多租户标识", width = 15)
    @ApiModelProperty(value = "多租户标识")
    private String relTenantIds;
    /**
     * 设备ID
     */
    @Excel(name = "设备ID", width = 15)
    @ApiModelProperty(value = "设备ID")
    private String clientId;
}
