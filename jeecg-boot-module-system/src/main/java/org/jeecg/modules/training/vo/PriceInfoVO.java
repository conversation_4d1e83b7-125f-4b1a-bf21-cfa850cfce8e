package org.jeecg.modules.training.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 价格信息
 */
@Data
@NoArgsConstructor
public class PriceInfoVO {

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 套餐起始日期
     */
    private Date startDate;

    /**
     * 套餐终止日期
     */
    private Date endDate;

    /**
     * 构造函数 - 包含价格、描述、起始日期和终止日期
     */
    public PriceInfoVO(BigDecimal price, String description, Date startDate, Date endDate) {
        this.price = price;
        this.description = description;
        this.startDate = startDate;
        this.endDate = endDate;
    }
}
