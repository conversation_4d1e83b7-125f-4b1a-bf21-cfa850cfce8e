package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.PackagesLog;
import org.jeecg.modules.training.vo.PackagesLogVO;

@Mapper
public interface PackagesLogMapper extends BaseMapper<PackagesLog> {

    // 分页列表查询
    IPage<PackagesLogVO> listPages(Page<PackagesLogVO> page, @Param("ew") QueryWrapper<PackagesLogVO> queryWrapper);
}
