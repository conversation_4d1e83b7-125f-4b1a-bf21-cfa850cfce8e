package org.jeecg.modules.training.async.download;

import org.jeecg.modules.training.entity.Trainee;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021-06-16 16:47
 **/

@Service
public class AsyncDownloadService {

    private static final Logger LOG = LoggerFactory.getLogger(AsyncDownloadService.class);

    private Thread thread;
    private final Object syncTimer = new Object();
    private volatile boolean running = false;

    //分成两个列表，一个列表存储刚刚添加的新任务，一个列表存储正在运行的任务，
    //主要目的是防止正在运行的任务因为对列表加锁卡住了调用添加新任务的线程，导致降低系统反应速度
    //任务的传输，可能会因为连接异常，导致卡住，等待，
    //running tasks
    private final List<AsyncDownloadTask> runningTasks = new ArrayList<>();
    //new tasks
    private final List<AsyncDownloadTask> newTasks = new ArrayList<>();

    //userId <-> downloading tasks
    private final Map<String, List<AsyncDownloadTask>> userTasks = new HashMap<>();

    public AsyncDownloadService() {
    }

    public void start() {

        if (thread != null)
            return;

        running = true;

        thread = new Thread(() -> {
            try {

                while (running) {

                    try {

                        synchronized (syncTimer) {
                            syncTimer.wait();
                        }

                        if (!running)
                            break;

                        AsyncDownloadTask task;
                        byte[] buffer = new byte[2048];

                        synchronized (newTasks) {
                            runningTasks.addAll(newTasks);
                            newTasks.clear();
                        }

                        boolean hasEnd = runningTasks.isEmpty();
                        while (!hasEnd && running) {

                            long totalSize = 0;
                            long startTime = System.currentTimeMillis();
                            boolean isEnd = !running;
                            int i = 0;
                            while (!isEnd) {

                                task = null;

                                try {

                                    //获取任务
                                    task = runningTasks.get(i);

                                    //判断能否传输
                                    if (!task.canTransfer()) {

                                        //下一个
                                        i++;
                                        isEnd = (i >= runningTasks.size()) || !running;

                                        continue;
                                    }

                                    //传输
                                    int size = task.transferOneFrame(buffer);
                                    if (size < 0) {
                                        //小于0，则传输完成
                                        runningTasks.remove(i--);
                                    }

                                    //累计
                                    totalSize += size;

                                } catch (Exception ex) {
                                    runningTasks.remove(i--);
                                    if (task != null)
                                        task.end();
                                    LOG.error("EXP", ex);
                                }

                                //下一个
                                i++;
                                isEnd = (i >= runningTasks.size()) || !running;
                            }

                            long totalTime = (System.currentTimeMillis() - startTime) / 1000;

                            //如果本次没有任何一个可以传输，则需要等待一点时间再循环，防止演变为无停顿死循环模式导致CPU过高
                            if (totalSize == 0) {
                                Thread.sleep(30);
                            } else if (totalTime > 10) {
                                //每过10秒，让出CPU，给其他线程机会
                                //yield=让步，让出CPU
                                // TODO 让其他线程有执行机会 ？？？ 待验证，可能会导致：控速不准(变化范围较大)、线程切换消耗CPU等问题。
                                // 主要是给 向handlers添加记录 操作更多执行机会，因为线程和那个操作都会对handlers加锁
                                Thread.yield();
                            }

                            synchronized (newTasks) {
                                if (!newTasks.isEmpty()) {
                                    runningTasks.addAll(newTasks);
                                    newTasks.clear();
                                }
                            }

                            hasEnd = runningTasks.isEmpty();
                        }

                    } catch (InterruptedException ex) {
                        LOG.error("EXP", ex);
                        break;
                    } catch (Throwable ex) {
                        LOG.error("EXP", ex);
                    }
                }

            } finally {
                running = false;
                try {
                    for (AsyncDownloadTask task : runningTasks) {
                        task.end();
                    }
                } catch (Exception ex) {
                }
                try {
                    for (AsyncDownloadTask task : newTasks) {
                        task.end();
                    }
                } catch (Exception ex) {
                }
                LOG.info("AsyncDownloadService Thread STOP");
            }

        }, "AsyncDownloadServiceThread");

        thread.start();

        LOG.info("AsyncDownloadService started");

//        initConsoleCommands();
    }

    public void close() {

        if (thread == null)
            return;

        LOG.info("AsyncDownloadService stopping");

        running = false;

        synchronized (syncTimer) {
            syncTimer.notify();
        }
        try {
            thread.join();
        } catch (Exception ex) {
        }

        LOG.info("AsyncDownloadService stopped");

    }

    public AsyncDownloadTask add(Trainee sysUser, HttpServletRequest request, HttpServletResponse response, File file, String filename) {

        if (!running) {
            LOG.error("AsyncDownloadService is stopping/stopped, cannot push new download:" + filename);
            return null;
        }

        AsyncDownloadTask task = new AsyncDownloadTask(sysUser, file, filename, request, response);

        if (addTask(task)) {
            return task;
        }

        return null;
    }

    private boolean addTask(AsyncDownloadTask task) {

        if (task.start()) {

            final List<AsyncDownloadTask> tasks = userTasks.computeIfAbsent(task.getUserId(), key -> new ArrayList<>());

            task.setOnEnd(() -> {
                synchronized (tasks) {
                    tasks.remove(task);
                }
            });

            synchronized (newTasks) {
                newTasks.add(task);
                if (newTasks.size() > 200) {
                    LOG.warn("Download new task too much:" + newTasks.size());
                }
            }

            synchronized (tasks) {
                tasks.add(task);
            }

            synchronized (syncTimer) {
                syncTimer.notify();
            }

            return true;
        }

        return false;
    }

    public void changeUserLevel(Long userId, int level) {

        final List<AsyncDownloadTask> tasks = userTasks.get(userId);
        if (tasks == null)
            return;

        synchronized (tasks) {
            for (AsyncDownloadTask task : tasks) {
                if (task.getSpeedLevel() != level) {
                    task.setSpeedLevel(level);
                }
            }
        }
    }

}
