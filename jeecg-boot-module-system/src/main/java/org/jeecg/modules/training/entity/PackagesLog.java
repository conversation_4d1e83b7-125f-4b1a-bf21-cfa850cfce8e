package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

@Data
public class PackagesLog implements Serializable {

    //日志id
    @TableId(type = IdType.AUTO)
    private Long id;
    //被修改的套餐id
    private Long packageId;
    //操作人id
    private String operatorId;
    //操作类型（0：新增、1：修改、2：删除）
    private Integer actionType;
    //修改前的剩余数量
    private Integer oldSurplusNum;
    //修改后的剩余数量
    private Integer newSurplusNum;
    //修改前的到期时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private LocalDate oldExpireTime;
    //修改后的到期时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private LocalDate newExpireTime;
    //创建时间
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    //备注
    private String remark;

}

