package org.jeecg.modules.training.async.materials;

import lombok.Data;

/**
 * Created by xiguozhou on 15/12/5.
 */
@Data
public class VideoInfo {

    //宽度 像素
    public int width = 0;

    //高度 像素
    public int height = 0;

    //时间长度,秒
    public float time = 0f;

    //帧率
    public Float fps = 24f;

    //文件格式
    public String format = "";

    //视频编码
    public String videoCoder = "";
    //视频编码标记
    public String videoCoderTag = "";
    //视频比特率
    public long videoBitRate = 0;

    //音频编码
    public String audioCoder = "";
    //音频比特率
    public long audioBitRate = 0;
    //音频采样频率
    public long audioSampleRate = 0;

}
