package org.jeecg.modules.training.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import org.jeecg.modules.training.entity.UserPaper;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年12月17日 15:18
 */
@Data
public class UserPaperVO extends UserPaper {

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 试卷类型
     */
    private Integer paperType;

    /**
     * 人员ids
     */
    private String userIds;

    /**
     * 人员
     */
    private String userName;

    private String teamName;

    private String teamNameOld;

    private String phone;

    /**
     * 界面推送时传递的值
     */
    private String startTimeVal;

    /**
     * 界面推送时传递的值
     */
    private String endTimeVal;

    /**
     * 界面推送时传递的值
     */
    private String warnTimeVal;

    /**
     * 强制推送
     * 0 有可考试的就不推送
     * 1 强制推送新的考试
     */
    private Integer forcePush;

    /**
     * 用户所属公司（同属多个公司时，用户信息会有重复）
     */
    private String userDepRoute;

    // 用户职务
    @TableField("user_post")
    private String userPost;

}
