package org.jeecg.modules.training.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.NumberUtil;
import org.jeecg.modules.training.entity.Paper;
import org.jeecg.modules.training.entity.PaperQuestion;
import org.jeecg.modules.training.service.IPaperQuestionService;
import org.jeecg.modules.training.service.IPaperService;
import org.jeecg.modules.training.service.IQuestionsService;
import org.jeecg.modules.training.vo.PaperQuestionVO;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @Description: 试卷库题目
 * @Author: hzk
 * @Date: 2022-06-15
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "试卷库题目")
@RestController
@RequestMapping("/adm/paper_question/paperQuestion")
public class PaperQuestionController extends JeecgController<PaperQuestion, IPaperQuestionService> {
    @Autowired
    private IPaperQuestionService paperQuestionService;
    @Autowired
    private IPaperService paperService;
    @Autowired
    private IQuestionsService questionsService;
    @Autowired
    private ISyncStateService syncStateService;
    //
    ///**
    // * 分页列表查询
    // *
    // * @param paperQuestion
    // * @param pageNo
    // * @param pageSize
    // * @param req
    // * @return
    // */
    //@AutoLog(value = "试卷库题目-分页列表查询")
    //@ApiOperation(value="试卷库题目-分页列表查询", notes="试卷库题目-分页列表查询")
    //@GetMapping(value = "/list")
    //public Result<?> queryPageList(PaperQuestion paperQuestion,
    //							   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
    //							   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
    //							   HttpServletRequest req) {
    //	QueryWrapper<PaperQuestion> queryWrapper = QueryGenerator.initQueryWrapper(paperQuestion, req.getParameterMap());
    //	Page<PaperQuestion> page = new Page<PaperQuestion>(pageNo, pageSize);
    //	IPage<PaperQuestion> pageList = paperQuestionService.page(page, queryWrapper);
    //	return Result.OK(pageList);
    //}


    /**
     * 分页列表查询
     *
     * @param paperQuestion
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "试卷库题目-分页列表查询")
    @ApiOperation(value = "试卷库题目-分页列表查询", notes = "试卷库题目-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<PaperQuestionVO>> queryPageList(PaperQuestionVO paperQuestion,
                                                        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                        HttpServletRequest req) {
        QueryWrapper<PaperQuestionVO> queryWrapper = QueryGenerator.initQueryWrapper(paperQuestion, req.getParameterMap());
        queryWrapper.and(q -> q.lambda().eq(PaperQuestionVO::getDelFlag, 0));

        Page<PaperQuestionVO> page = new Page<>(pageNo, pageSize);
        IPage<PaperQuestionVO> pageList = paperQuestionService.listQuestions(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param paperQuestion
     * @return
     */
    @AutoLog(value = "试卷库题目-添加")
    @ApiOperation(value = "试卷库题目-添加", notes = "试卷库题目-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody PaperQuestion paperQuestion) {
        paperQuestionService.save(paperQuestion);
        return Result.OK("添加成功！");
    }

    /**
     * 添加
     *
     * @param map
     * @return
     */
    @AutoLog(value = "题库-添加")
    @ApiOperation(value = "题库-添加", notes = "题库-添加")
    @PostMapping(value = "/addQuestToPaper")
    public Result<?> addQuestToPaper(@RequestBody HashMap<String, String> map) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        String paperId = map.get("paperId");
        String questIds = map.get("questIds");

        Long pid = Long.parseLong(paperId);
        Paper paper = paperService.getById(pid);
        List<PaperQuestion> paperQuestions = new ArrayList<>();
        String[] qids = questIds.split(",");
        for (String questId : qids) {
            Long qid = Long.parseLong(questId);
            //不要判断重复，因为可能同一个题目在多套试卷中
            //LambdaQueryWrapper<PaperQuestion> wrapper = new LambdaQueryWrapper();
            //wrapper.eq(PaperQuestion::getQuestionId, questId);
            //wrapper.eq(PaperQuestion::getPaperId, pid);
            //boolean exists = paperQuestionMapper.exists(wrapper);
            //if (exists){
            //    throw new JeecgBootException("此题目已经存在无需再次添加，请选择其他题目添加！");
            //}
            PaperQuestion paperQuestion = new PaperQuestion();
            paperQuestion.setPaperId(pid);
            paperQuestion.setQuestionId(qid);
            if (NumberUtil.equalsValue(1, paper.getIsQuesSort())) {
                paperQuestion.setSort(questionsService.getById(qid).getSort());
            }
            paperQuestions.add(paperQuestion);
        }

        paperQuestionService.saveBatch(paperQuestions);
        return Result.OK("添加成功！");
    }


    /**
     * 编辑
     *
     * @param paperQuestion
     * @return
     */
    @AutoLog(value = "试卷库题目-编辑")
    @ApiOperation(value = "试卷库题目-编辑", notes = "试卷库题目-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody PaperQuestion paperQuestion) {
        paperQuestionService.updateById(paperQuestion);
        syncStateService.resetSyncState(CommonConstant.PAPER_QUESTION, paperQuestion.getId().toString());
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "试卷库题目-通过id删除")
    @ApiOperation(value = "试卷库题目-通过id删除", notes = "试卷库题目-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        paperQuestionService.removeById(id);
        syncStateService.resetSyncState(CommonConstant.PAPER_QUESTION, id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "试卷库题目-批量删除")
    @ApiOperation(value = "试卷库题目-批量删除", notes = "试卷库题目-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        this.paperQuestionService.removeByIds(idList);
        idList.forEach(id -> syncStateService.resetSyncState(CommonConstant.PAPER_QUESTION, id));
        return Result.OK("批量删除成功！");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "试卷库题目-批量设置题号")
    @ApiOperation(value = "试卷库题目-设置题号", notes = "试卷库题目-设置题号")
    @RequestMapping(value = "/setQuestionSort", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> setQuestionSort(@RequestParam(name = "ids") String ids,
                                     @RequestParam(name = "start") Integer start,
                                     @RequestParam(name = "end") Integer end) {
        String[] idArray = ids.split(",");
        List<String> idList = Arrays.asList(idArray);
        this.paperQuestionService.setQuestionSort(idArray, start, end);
        idList.forEach(id -> syncStateService.resetSyncState(CommonConstant.PAPER_QUESTION, id));
        return Result.OK("批量设置成功！");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "试卷库题目-设置卷套")
    @ApiOperation(value = "试卷库题目-设置卷套", notes = "试卷库题目-设置卷套")
    @RequestMapping(value = "/setQuestionGroup", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> setQuestionGroup(@RequestParam(name = "ids") String ids,
                                      @RequestParam(name = "group") String group) {
        String[] idArray = ids.split(",");
        List<String> idList = Arrays.asList(idArray);
        this.paperQuestionService.setQuestionGroup(idArray, group);
        idList.forEach(id -> syncStateService.resetSyncState(CommonConstant.PAPER_QUESTION, id));
        return Result.OK("批量设置成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "试卷库题目-通过id查询")
    @ApiOperation(value = "试卷库题目-通过id查询", notes = "试卷库题目-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        PaperQuestion paperQuestion = paperQuestionService.getById(id);
        return Result.OK(paperQuestion);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param paperQuestion
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PaperQuestion paperQuestion) {
        return super.exportXls(request, paperQuestion, PaperQuestion.class, "试卷库题目");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PaperQuestion.class);
    }
}
