package org.jeecg.modules.training.async.paper;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.config.init.SpringBeanUtil;
import org.jeecg.modules.training.async.paper.impl.PushUserPaperAllHandler;
import org.jeecg.modules.training.async.paper.impl.PushUserPaperByPaperHandler;
import org.jeecg.modules.training.async.paper.impl.PushUserPaperByUserHandler;
import org.jeecg.modules.training.service.IUserPaperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年12月14日
 */
@Slf4j
@Component
public class PushPaperService {

    final static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    final long ONE_DAY_MS = 24 * 3600 * 1000L;

    private final Object syncCheckLocker = new Object();
    private final Object syncPushLocker = new Object();

    private Thread handlerCheckThread;
    private Thread handlerPushThread;

    private volatile Boolean running = false;
    private volatile Boolean pushAll = true;

    private final ConcurrentLinkedQueue<PushPaperHandler> handlers = new ConcurrentLinkedQueue<>();

    @Autowired
    private IUserPaperService userPaperService;

    @Autowired
    private PushUserPaperAllHandler pushUserPaperAllHandler;

    public PushPaperService() {
    }

    public void start() {
        if (handlerCheckThread != null) {
            return;
        }

        running = true;

        handlerCheckThread = new Thread("CheckUserPaper Thread") {
            @Override
            public void run() {
                try {

                    boolean isPushAll = pushAll;

                    while (running) {

                        Calendar calNow = Calendar.getInstance();

                        //00：00：00
                        Calendar calZero = Calendar.getInstance();
                        calZero.set(
                                calNow.get(Calendar.YEAR),
                                calNow.get(Calendar.MONTH),
                                calNow.get(Calendar.DATE),
                                0, 0, 0
                        );

                        if (isPushAll) {

                            try {
                                pushAll();
                            } catch (Exception ex) {
                                log.error("EXP", ex);
                            }

                        } else {

                            //当日00:00时间
                            String now = dateFormat.format(calZero.getTime());

                            log.info("Run checker start");

                            try {
                                //处理告警
                                userPaperService.processWarnStatus(now);
                            } catch (Exception ex) {
                                log.error("EXP", ex);
                            }

                            try {
                                //处理过期
                                userPaperService.processExpiredStatus(now);
                            } catch (Exception ex) {
                                log.error("EXP", ex);
                            }

                            log.info("Run checker end");

                        }

                        pushAll = true;

                        //次日00：00
                        long nextDateMS = calZero.getTimeInMillis() + ONE_DAY_MS;
                        synchronized (syncCheckLocker) {
                            //等待 从现在开始 到 次日00:01 时长
                            syncCheckLocker.wait(nextDateMS - calNow.getTimeInMillis() + 1000);
                            isPushAll = pushAll;
                        }

                    }
                } catch (Exception ex) {
                    log.error("EXP", ex);
                }
                log.info("CheckUserPaper Thread stoped");
            }
        };
        handlerCheckThread.start();

        handlerPushThread = new Thread("PushUserPaper Thread") {
            @Override
            public void run() {
                try {

                    while (running) {

                        try {

                            PushPaperHandler handler;
                            while ((handler = handlers.poll()) != null) {
                                try {
                                    handler.run();
                                } catch (Exception ex) {
                                    log.error("EXP", ex);
                                }
                            }

                            //检查一下是否有过期或者告警的
                            notifyChecker();

                        } catch (Exception ex) {
                            log.error("EXP", ex);
                        }

                        synchronized (syncPushLocker) {
                            //等待
                            syncPushLocker.wait();
                        }

                    }
                } catch (Exception ex) {
                    log.error("EXP", ex);
                }
                log.info("PushUserPaper Thread stoped");
            }
        };
        handlerPushThread.start();

        log.info("PushPaperService started");
    }

    public void close() {
        if (handlerCheckThread == null) {
            return;
        }

        running = false;
        synchronized (syncCheckLocker) {
            syncCheckLocker.notifyAll();
        }

        synchronized (syncPushLocker) {
            syncPushLocker.notifyAll();
        }

        try {
            handlerCheckThread.join();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        handlerCheckThread = null;

        try {
            handlerPushThread.join();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        handlerPushThread = null;

        log.info("PushPaperService Thread stoped");
    }

    /**
     * 立即运行一次定时处理
     */
    public void pushPaper(Long paperId) {

        synchronized (handlers) {
            if (paperId != null && paperId > 0) {
                PushUserPaperByPaperHandler handler = SpringBeanUtil.getBean(PushUserPaperByPaperHandler.class);
                handler.setPaperId(paperId);
                handlers.add(handler);
            }
        }

        synchronized (syncPushLocker) {
            syncPushLocker.notifyAll();
        }

    }

    /**
     * 立即运行一次定时处理
     */
    public void pushUsers(List<String> userIds) {

        synchronized (handlers) {
            if (userIds != null && !userIds.isEmpty()) {
                PushUserPaperByUserHandler handler = SpringBeanUtil.getBean(PushUserPaperByUserHandler.class);
                handler.setUserIds(userIds);
                handlers.add(handler);
            }
        }

        synchronized (syncPushLocker) {
            syncPushLocker.notifyAll();
        }

    }

    /**
     * 立即运行一次定时处理
     */
    public void pushAll() {

        synchronized (handlers) {
            handlers.add(pushUserPaperAllHandler);
        }

        synchronized (syncPushLocker) {
            syncPushLocker.notifyAll();
        }
    }

    public void notifyChecker() {
        //触发一次状态处理，以便能及时判断过期等状态
        synchronized (syncCheckLocker) {
            //阻止检查全部，否则会导致死循环
            pushAll = false;
            syncCheckLocker.notifyAll();
        }
    }
}
