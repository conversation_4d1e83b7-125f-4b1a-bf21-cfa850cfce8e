package org.jeecg.modules.training.vo;

import lombok.Data;
import org.jeecg.modules.training.entity.Exam;

import java.util.ArrayList;
import java.util.List;

/**
 * 考卷视图模型
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年05月07日 18:54
 */
@Data
public class ExamVO extends Exam {
    /**
     * 题目与答案
     */
    List<ExamQuestionVO> questions = new ArrayList<>();
    /**
     * 分类名称
     */
    String categoryName;
    /**
     * 用户名称
     */
    String username;

    String realname;

    //临时接受用户id
    String loginId;
}
