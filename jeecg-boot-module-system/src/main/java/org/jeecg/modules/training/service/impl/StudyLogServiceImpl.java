package org.jeecg.modules.training.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.training.entity.Category;
import org.jeecg.modules.training.entity.StudyLog;
import org.jeecg.modules.training.mapper.StudyLogMapper;
import org.jeecg.modules.training.mapper.TraineeMapper;
import org.jeecg.modules.training.service.IStudyLogService;
import org.jeecg.modules.training.service.IStudyService;
import org.jeecg.modules.training.vo.StudyLogOneMonth;
import org.jeecg.modules.training.vo.StudyLogVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 学习日志表
 * @Author: hzk
 * @Date: 2022-09-02
 * @Version: V1.0
 */
@Service
public class StudyLogServiceImpl extends ServiceImpl<StudyLogMapper, StudyLog> implements IStudyLogService {

    @Autowired
    private TraineeMapper traineeMapper;

    @Resource
    private ISysDepartService sysDepartService;

    @Autowired
    private IStudyService studyService;

    @Override
    public IPage<StudyLogVO> getPageList(Page<StudyLogVO> page, QueryWrapper<StudyLogVO> queryWrapper) {
        return baseMapper.getPageList(page, queryWrapper);
    }

    @Override
    public IPage<StudyLogVO> getStudyList(Page<StudyLogVO> page, QueryWrapper<StudyLogVO> queryWrapper) {
        return baseMapper.getStudyList(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StudyLog saveInfo(StudyLog studyLog) {
        studyLog.setDelFlag(CommonConstant.DEL_FLAG_0);
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆。");
        }
        studyLog.setDepartment(loginUser.getTeamId());
        studyLog.setUserId(loginUser.getId());
        // 设置行业id的值
        studyLog.setIndustryIds(studyService.getById(studyLog.getStudyId()).getIndustryIds());
        this.save(studyLog);
        return studyLog;
    }

    /**
     * 获取用户已读的数量
     *
     * @param userId
     * @param boardTime
     * @param post
     * @param groupInfo
     * @param category
     * @param type
     * @return
     */
    @Override
    public Long getAlreadyReadNumber(String userId, String boardTime, String post, String groupInfo, Category category) {
        Long categoryId = category.getId();
        //用户有组信息查询特定组
        //List<String> groupInfoList = StringUtils.isEmpty(groupInfo) ? null : Arrays.asList(groupInfo.split(","));
        return baseMapper.getAlreadyReadNumber(userId, categoryId, boardTime, post, groupInfo);
    }

    /**
     * 获取用户需要关注并且已经关注的数量
     *
     * @param userId
     * @param boardTime
     * @param post
     * @param groupInfo
     * @param category
     * @return
     */
    @Override
    public Long getAlreadyAttentionNumber(String userId, String boardTime, String post, String groupInfo, Category category) {
        //用户有组信息查询特定组
        return baseMapper.getAlreadyAttentionNumber(userId, category.getRoute() + "%", boardTime, post, groupInfo);
    }

    /**
     * 获取所有符合条件的
     *
     * @param queryWrapper
     * @return
     */
    @Override
    public List<StudyLogVO> getList(QueryWrapper<StudyLogVO> queryWrapper) {
        return baseMapper.getPageList(queryWrapper);
    }

    @Override
    public List<StudyLogVO> getStudyList(QueryWrapper<StudyLogVO> queryWrapper) {
        return baseMapper.getStudyList(queryWrapper);
    }

    @Override
    public List<StudyLogOneMonth> getOneMonthRecordList(QueryWrapper<StudyLogOneMonth> queryWrapper, String depRoute) {
        return baseMapper.getOneMonthRecordList(queryWrapper, depRoute);
    }

    @Override
    public IPage<StudyLogOneMonth> getOneMonthRecordLisPage(Page<StudyLogOneMonth> page, QueryWrapper<StudyLogOneMonth> queryWrapper, String depRoute) {
        return baseMapper.getOneMonthRecordLisPage(page, queryWrapper, depRoute);
    }
}
