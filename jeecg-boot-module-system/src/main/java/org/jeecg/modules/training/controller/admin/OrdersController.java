package org.jeecg.modules.training.controller.admin;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.training.entity.Orders;
import org.jeecg.modules.training.service.IOrdersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

@Slf4j
@RestController
@RequestMapping("/adm/orders")
public class OrdersController {

    @Autowired
    private IOrdersService ordersService;

    // 分页查询
    @GetMapping(value = "/pageList")
    public Result<?> queryPageList(Orders orders,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<Orders> queryWrapper = QueryGenerator.initQueryWrapper(orders, req.getParameterMap());

        Page<Orders> page = new Page<>(pageNo, pageSize);
        Page<Orders> pageList = ordersService.page(page, queryWrapper);

        return Result.OK(pageList);
    }

    // 删除
    @DeleteMapping("/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        ordersService.removeById(id);
        return Result.ok("删除成功！");
    }

    // 批量删除
    @DeleteMapping("/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        ordersService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    // 修改
    @PutMapping("/edit")
    public Result<?> edit(@RequestBody Orders orders) {
        ordersService.updateById(orders);
        return Result.ok("修改成功！");
    }


}

