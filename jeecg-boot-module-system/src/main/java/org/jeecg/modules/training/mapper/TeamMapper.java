package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.training.entity.Team;
import org.jeecg.modules.training.vo.TeamVO;

import java.util.List;

public interface TeamMapper extends BaseMapper<Team> {

    List<Team> getTeamList(@Param("ew") QueryWrapper<Team> queryWrapper);

    /**
     * 根据id下级部门数量
     *
     * @param parentId
     * @return
     */
    @Select("SELECT COUNT(*) FROM team WHERE del_flag = 0 AND parent_id = #{parentId,jdbcType=VARCHAR}")
    Integer queryCountByPid(@Param("parentId") String parentId);

    Page<TeamVO> pageAll(Page<TeamVO> page, @Param("ew") QueryWrapper<TeamVO> queryWrapper);

}
