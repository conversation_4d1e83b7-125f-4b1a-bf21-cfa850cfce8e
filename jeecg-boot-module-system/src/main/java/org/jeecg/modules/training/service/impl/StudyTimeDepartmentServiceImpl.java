package org.jeecg.modules.training.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.training.entity.StudyTimeDepartment;
import org.jeecg.modules.training.mapper.StudyTimeDepartmentMapper;
import org.jeecg.modules.training.service.StudyTimeDepartmentService;
import org.jeecg.modules.training.vo.StudyTimeDepartmentVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 船舶每日学习时长表(StudyTimeDepartment)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-22 10:47:41
 */
@Service("studyTimeDepartmentService")
public class StudyTimeDepartmentServiceImpl extends ServiceImpl<StudyTimeDepartmentMapper, StudyTimeDepartment> implements StudyTimeDepartmentService {

    @Resource
    private StudyTimeDepartmentMapper studyTimeDepartmentMapper;

    @Override
    public Page<StudyTimeDepartmentVO> getPageList(Page<StudyTimeDepartmentVO> page,  String depRoute, QueryWrapper<StudyTimeDepartmentVO> queryWrapper, String scope) {
        return studyTimeDepartmentMapper.getPageList(page,depRoute, queryWrapper, scope);
    }
}

