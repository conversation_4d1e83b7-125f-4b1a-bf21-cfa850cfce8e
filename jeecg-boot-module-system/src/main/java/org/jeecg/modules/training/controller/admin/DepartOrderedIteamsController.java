package org.jeecg.modules.training.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.training.entity.DepartOrderedIteams;
import org.jeecg.modules.training.service.IDepartOrderedIteamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/adm/departOrderedIteams")
public class DepartOrderedIteamsController {

    @Autowired
    private IDepartOrderedIteamsService departOrderedIteamsService;

    /**
     * 分页查询
     */
    @GetMapping(value = "/pageList")
    public Result<?> queryPageList(DepartOrderedIteams departOrderedIteams,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<DepartOrderedIteams> queryWrapper = QueryGenerator.initQueryWrapper(departOrderedIteams, req.getParameterMap());
        
        Page<DepartOrderedIteams> page = new Page<>(pageNo, pageSize);
        Page<DepartOrderedIteams> pageList = departOrderedIteamsService.page(page, queryWrapper);
        
        return Result.OK(pageList);
    }
    
    /**
     * 添加
     */
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody DepartOrderedIteams departOrderedIteams) {
        departOrderedIteamsService.save(departOrderedIteams);
        return Result.OK("添加成功！");
    }
    
    /**
     * 修改
     */
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody DepartOrderedIteams departOrderedIteams) {
        departOrderedIteamsService.updateById(departOrderedIteams);
        return Result.OK("编辑成功！");
    }
    
    /**
     * 通过id删除
     */
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        departOrderedIteamsService.removeById(id);
        return Result.OK("删除成功！");
    }
    
}