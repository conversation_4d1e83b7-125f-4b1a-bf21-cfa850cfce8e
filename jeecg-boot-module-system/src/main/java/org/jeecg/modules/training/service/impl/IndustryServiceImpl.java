package org.jeecg.modules.training.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.training.entity.Industry;
import org.jeecg.modules.training.mapper.IndustryMapper;
import org.jeecg.modules.training.service.IIndustryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class IndustryServiceImpl extends ServiceImpl<IndustryMapper, Industry> implements IIndustryService {

    @Autowired
    private IndustryMapper industryMapper;

    // 根据行业名称集合获取行业id集合
    @Override
    public List<Long> getIdListByNames(String industryNames) {
        return this.listObjs(new LambdaQueryWrapper<Industry>()
                        .select(Industry::getId)
                        .in(Industry::getIndustryName, Arrays.asList(industryNames.split(","))),
                c -> Long.parseLong(c.toString()));
    }

    // 根据行业id拼接字符串获取行业名称集合拼接字符串
    @Override
    public String getNamesByIndustryIds(String industryIds) {
        if (industryIds == null) {
            return "";
        }
        // 格式化行业ids
        industryIds = industryIds.trim().replaceAll("^,|,$", "");
        // 获取行业id集合
        List<Long> industryIdList = Arrays.stream(industryIds.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        // 获取行业id对应的行业名称集合
        return industryMapper.selectBatchIds(industryIdList).stream()
                .map(Industry::getIndustryName)
                .collect(Collectors.joining(","));
    }

    // 格式化行业id集合 在每个元素之间插入","，在前后添加","
    @Override
    public String formatIndustryIds(List<Long> industryIdList) {
        return industryIdList.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(",", ",", ","));
    }

    // 根据行业名称字符串获取保存格式的行业id字符串
    @Override
    public String getIndustryIdsByNames(String industryNames) {
        // 根据行业名称获取行业id
        List<Long> industryIdList = this.getIdListByNames(industryNames);
        // 设置industryIdList的格式，在每个元素之间插入","，在前后添加","
        return this.formatIndustryIds(industryIdList);
    }

    @Override
    public String formatIndustryIdsExp(String industryIds) {

        // 去掉开头和结尾的逗号
        if (industryIds.startsWith(","))
            industryIds = industryIds.substring(1, industryIds.length() - 1);

        // 逗号改成MySQL正则表达式
        industryIds = "," + industryIds.replaceAll(",", ",|,") + ",";
        return industryIds;
    }
}
