package org.jeecg.modules.training.async.report.factory;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.training.async.report.service.IReportGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * 根据报告类型创建对应的数据提供者和报告生成器实例
 * 使用工厂模式实现服务创建的统一管理
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class GeneratorFactory {

    @Autowired
    @Qualifier("standardExamReportGenerator")
    private IReportGenerator standardExamReportGenerator;
    @Autowired
    @Qualifier("scl90ExamReportGenerator")
    private IReportGenerator scl90ExamReportGenerator;

    /**
     * 创建报告生成器
     * ---
     * 模板版本号管理规则：
     * 1. 版本号格式：Vx.y，其中x为大版本号，y为小版本号
     * 2. 小版本号变更(y+1) 例如：V1.0 -> V1.1
     *    - 仅修改模板样式、排版、文字描述等
     *    - 删除模板中的替换字段（书签）
     * 3. 大版本号变更(x+1) 例如：V1.1 -> V2.0
     *    - 增加模板中的替换字段（书签）
     *    - 修改替换字段（书签）的名称
     *    - 改变数据结构（如评分项目数量变更）
     * 4. 新的大版本发布时，小版本号重置为0
     * ---
     * 因此,根据上述版本号管理规则:
     *    - 模板小版本升级,可以复用原有的 ReportGenerator
     *    - 模板大版本升级,需要新增 ReportGenerator
     *    - 如果某个模板需要读取大量磁盘上的资源文件，导致生成速度变慢，
     *      可以考虑对此模板专门建一个 ReportGenerator，
     *      将资源文件读取放入构造函数中，避免重复读取。
     *      并使用 @Lazy 注解修饰此 ReportGenerator，延迟加载。
     *
     * @param templateName 模板名称
     * @return 对应的报告生成器实例
     * @throws IllegalArgumentException 当考试类型不支持时抛出
     */
    public IReportGenerator createReportGenerator(String templateName) {
        switch (templateName) {
            case "Standard-V1-0":
//          case "Standard-V1-1":
                return standardExamReportGenerator;
//          case "Standard-V2-0":
//              return {新增的 ReportGenerator};
            case "SCL90-V1-0":
                return scl90ExamReportGenerator;
            default:
                log.error("Unsupported exam template type: {}", templateName);
                throw new IllegalArgumentException("Unsupported exam type: " + templateName);
        }
    }
} 