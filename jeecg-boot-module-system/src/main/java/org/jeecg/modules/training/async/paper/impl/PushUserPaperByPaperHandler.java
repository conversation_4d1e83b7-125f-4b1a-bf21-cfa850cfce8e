package org.jeecg.modules.training.async.paper.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.dto.message.MessageDTO;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.modules.training.async.paper.PushPaperHandler;
import org.jeecg.modules.training.async.paper.UserInfo;
import org.jeecg.modules.training.entity.Paper;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.entity.UserPaper;
import org.jeecg.modules.training.util.ExamUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-12-16 01:23
 **/
@Slf4j
@Component
@Scope("prototype")
public class PushUserPaperByPaperHandler extends PushPaperHandler {

    final static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    private Long paperId;

    public void setPaperId(Long paperId) {
        this.paperId = paperId;
    }

    @Override
    public void run() {

        Date start = new Date();

        Paper paper = paperService.getById(paperId);
        //已经删除的考试会自动不可见
        if (paper == null || paper.getDelFlag() != null && paper.getDelFlag().equals(CommonConstant.DEL_FLAG_1))
            return;

        try {
            sysBaseAPI.sendSysAnnouncement(new MessageDTO("system", "admin", "考试推送", "考试推送开始:" + paper.getName() + "," + dateFormat.format(start)));

            Map<Long, Paper> paperMap = new HashMap<>();
            /*
             * 处理自动推送
             */
            final Calendar calNow = Calendar.getInstance();

            //00：00：00
            final Calendar calZero = Calendar.getInstance();
            calZero.set(
                    calNow.get(Calendar.YEAR),
                    calNow.get(Calendar.MONTH),
                    calNow.get(Calendar.DATE),
                    0, 0, 0
            );

            //当日00:00时间
            Date nowDate = calZero.getTime();

            //同步已推送的，未完成的考试推送信息，并自动变更状态
            fixPaperPush(paper, calNow);

        long time = System.currentTimeMillis();
            //查找应该能考试，但没有推送的。
            List<UserPaper> paperPush = userPaperService.findPaperPush(paperId);
            if (paperPush.isEmpty()) {
                log.info("Run push paper " + paperId + " break for empty");
                return;
            }

        log.info("Query Paper Push:" + paper.getId() + ":" + paper.getName() + ":" + (System.currentTimeMillis() - time));

            paperMap.put(paperId, paper);

            //所有尚未推送的用户(！！！注意！！！数据可能会很大，比如新增一个所有人都可以进行的考试后)
            Map<String, UserInfo> userMap = getUserInfoMap(calNow, paperPush);

            System.gc();

            if (!paperMap.isEmpty() && !userMap.isEmpty()){
	            time = System.currentTimeMillis();
	            doFixPush(nowDate, paperPush, paperMap, userMap);
	            log.info("Fix Paper Push:" + paper.getId() + ":" + paper.getName() + ":" + (System.currentTimeMillis() - time));
	        }
        } catch (Exception ex) {
            log.error("EXP", ex);
        } finally {
            Date end = new Date();
            String msg = "考试推送完成:" + paper.getName() + "," + dateFormat.format(start) + "~" + dateFormat.format(end) + "，耗时：" + (end.getTime() - start.getTime()) / 1000 + "秒";
            sysBaseAPI.sendSysAnnouncement(new MessageDTO("system", "admin", "考试推送", msg));
        }

    }

    @NotNull
    private Map<String, UserInfo> getUserInfoMap(Calendar calNow, List<UserPaper> paperPush) {
        Map<String, UserInfo> userMap;

        List<String> ids = paperPush.stream().map(UserPaper::getUserId).distinct().collect(Collectors.toList());
        List<Trainee> list = traineeService.listByIds(ids);
        userMap = list.stream().collect(Collectors.toMap(Trainee::getId, u -> {
            UserInfo userInfo = new UserInfo();
            userInfo.setTeamId(u.getTeamId());
            userInfo.setPost(u.getPost());
            userInfo.setOnboardDate(u.getOnboardDate());
            userInfo.setBoardTime(ExamUtils.getUserBoardTime(u, calNow.getTime()));
            userInfo.setTimeFactor(ExamUtils.getUserBoardTimeName(u, calNow.getTime()));
            return userInfo;
        }));
        list.clear();

        return userMap;
    }

    /**
     * 修正已推送的，未完成的考试推送信息，并自动变更状态
     *
     * @param paper
     */
    private void fixPaperPush(Paper paper, Calendar calNow) {

        long time = System.currentTimeMillis();

        LambdaQueryWrapper<UserPaper> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper
                .eq(UserPaper::getPaperId, paper.getId())
                .lt(UserPaper::getStatus, 2)
                .eq(UserPaper::getDelFlag, CommonConstant.DEL_FLAG_0)
                .orderByAsc(UserPaper::getId);

        try {

            int page = 1;
            while (true) {

                //每1000个处理一次
                Page<UserPaper> p = new Page<>(page, 1000);
                p = userPaperService.page(p, updateWrapper);
                List<UserPaper> list = p.getRecords();
                if (list == null || list.isEmpty()) {
                    break;
                }

                Map<String, UserInfo> userInfoMap = getUserInfoMap(calNow, list);

                for (UserPaper userPaper : list) {
                    makeEditUserPaper(userPaper, paper, userInfoMap.get(userPaper.getUserId()), calNow.getTime());
                }

                userPaperService.updateBatchById(list);

                page++;

            }

            log.info("Update Paper Push:" + paper.getId() + ":" + paper.getName() + ":" + (System.currentTimeMillis() - time));
        } catch (Exception e) {
            //
        }
        time = System.currentTimeMillis();
        userPaperService.fixPaperPushStatus(paperId);
        log.info("Update Paper Push State:" + paper.getId() + ":" + paper.getName() + ":" + (System.currentTimeMillis() - time));

    }
}
