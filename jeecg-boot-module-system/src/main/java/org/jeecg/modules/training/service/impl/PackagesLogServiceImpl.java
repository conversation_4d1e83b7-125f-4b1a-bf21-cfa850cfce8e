package org.jeecg.modules.training.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.training.entity.Packages;
import org.jeecg.modules.training.entity.PackagesLog;
import org.jeecg.modules.training.mapper.PackagesLogMapper;
import org.jeecg.modules.training.service.IPackagesLogService;
import org.jeecg.modules.training.vo.PackagesLogVO;
import org.jeecg.modules.training.vo.PackagesVO;
import org.springframework.stereotype.Service;


@Service
public class PackagesLogServiceImpl extends ServiceImpl<PackagesLogMapper, PackagesLog> implements IPackagesLogService {

    // 新增套餐新增或删除日志
    @Override
    public void createAddOrDeleteLog(Long packageId, String operatorId, Integer actionType) {
        PackagesLog packagesLog = new PackagesLog();

        packagesLog.setPackageId(packageId);
        packagesLog.setOperatorId(operatorId);
        packagesLog.setActionType(actionType);

        save(packagesLog);
    }

    // 分页列表查询
    @Override
    public IPage<PackagesLogVO> listPages(Page<PackagesLogVO> page, QueryWrapper<PackagesLogVO> queryWrapper) {
        return baseMapper.listPages(page, queryWrapper);
    }

    // 修改套餐修改日志
    @Override
    public void createRenewLog(PackagesVO newPackages, Packages oldPackages, String operatorId) {
        PackagesLog packagesLog = new PackagesLog();

        packagesLog.setPackageId(oldPackages.getId());
        packagesLog.setOperatorId(operatorId);
        packagesLog.setActionType(1);
        packagesLog.setOldSurplusNum(oldPackages.getSurplusNum());
        packagesLog.setNewSurplusNum(newPackages.getSurplusNum());
        packagesLog.setOldExpireTime(oldPackages.getExpireTime());
        packagesLog.setNewExpireTime(newPackages.getExpireTime());
        packagesLog.setRemark(newPackages.getRemark());

        save(packagesLog);
    }
}
