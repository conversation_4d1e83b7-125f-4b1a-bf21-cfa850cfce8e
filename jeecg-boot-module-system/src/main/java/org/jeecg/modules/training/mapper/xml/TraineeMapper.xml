<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.TraineeMapper">

  <!-- 根据用户名查询 -->
  <select id="getTraineeByName" resultType="org.jeecg.modules.training.entity.Trainee">
    SELECT *
    FROM trainee
    WHERE username = #{username}
      AND del_flag = 0
  </select>

  <!-- 根据身份证号查询 -->
  <select id="getTraineeByIDCard" resultType="org.jeecg.modules.training.vo.TraineeVO">
      SELECT t.*,team.team_name teamName
      FROM trainee t left join team on t.team_id = team.id
      WHERE identity_card = #{identityCard}
  </select>

  <select id="getTraineeList" resultType="org.jeecg.modules.training.vo.SysTraineeVO">
      select *
      from (select trainees.*,
                   tm.team_name,
                   p.id                                                                       as packages_id,
                   p.expire_time,
                      case
                      when p.status is null then 3
                      when (p.status = 1 and datediff(p.expire_time, now()) &lt; 30) then 2
                      else p.status
                      end as member_status,
                   p.start_time                                                               as member_start,
                   p.expire_time                                                              as member_expired
            from (select te.*
                  from trainee te
                  where id in (select std.trainee_id
                               from sys_trainee_depart std
                               where std.dep_id in (select d.id
                                                    from sys_user_depart sy
                                                             left join sys_depart d on sy.dep_id = d.id
                                                    where route like concat(#{loginRoute}, '%')))) trainees
                     left join team tm on trainees.team_id = tm.id
                      <if test="memberType == 1">
                          left join sys_trainee_depart std on (std.dep_id = #{sysDepartId}
                                                                   and trainees.id = std.trainee_id)
                      </if>
                     left join packages p on (p.del_flag = 0
                      <choose>
                          <when test="memberType == 0">
                              and trainees.current_packages_id = p.id
                          </when>
                          <when test="memberType == 1">
                              and std.packages_id = p.id
                          </when>
                      </choose> )
            where trainees.del_flag = 0
      ) trainee
          ${ew.customSqlSegment}
  </select>

  <!-- 更新被逻辑删除的用户 -->
  <update id="revertLogicDeleted">
    UPDATE
      trainee
    SET del_flag    = 0,
        update_by   = #{entity.updateBy},
        update_time = #{entity.updateTime}
    WHERE del_flag = 1
      AND id IN (${userIds})
  </update>

  <!--  修改用户部门code -->
  <update id="updateTraineeDepart">
    UPDATE trainee
    SET org_code = #{orgCode}
    WHERE username = #{username}
  </update>

  <!-- 根据用户id无状态查询 -->
  <select id="getUserByIdIgnoreDelFag" resultType="org.jeecg.modules.training.entity.Trainee">
    SELECT *
    FROM trainee
    WHERE id = #{id}
  </select>

  <!-- 根据手机号查询 -->
  <select id="getUserByPhone" resultType="org.jeecg.modules.training.entity.Trainee">
    SELECT *
    FROM trainee
    WHERE phone = #{phone}
      AND del_flag = 0
  </select>
  <select id="queryDepartTraineePageList" resultType="org.jeecg.modules.training.entity.Trainee">
    select a.*, c.depart_name as org_code_txt from trainee a
    join sys_trainee_depart b on b.trainee_id = a.id
    join sys_depart c on b.dep_id = c.id
    <bind name="bindOrgCode" value="orgCode+'%'"/>
    where a.del_flag = 0 and a.status = 1 and c.org_code like #{bindOrgCode}
    <if test="username!=null and username!=''">
      <bind name="bindUsername" value="'%'+username+'%'"/>
      and a.username like #{bindUsername}
    </if>
    <if test="realname!=null and realname!=''">
      <bind name="bindRealname" value="'%'+realname+'%'"/>
      and a.realname like #{bindRealname}
    </if>
  </select>

  <select id="getDepNamesByTraineeIds" resultType="org.jeecg.modules.training.vo.TraineeVO">
    select d.depart_name,std.trainee_id as id from sys_trainee_depart std,sys_depart d where d.id = std.dep_id and std.trainee_id in
    <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>

  <select id="getExamUserForPhase" resultType="org.jeecg.modules.training.vo.TraineeVO">
    SELECT *
    FROM (SELECT su.*,
                 sd.route AS route,
                 p.name   AS phase_name,
                 t.team_name
          FROM trainee su
                   LEFT JOIN user_phase up ON su.id = up.user_id AND up.is_current = 1
               LEFT JOIN phase p ON p.id = up.phase_id
               LEFT JOIN sys_trainee_depart std ON su.id = std.trainee_id
               LEFT JOIN sys_depart sd ON sd.id = std.dep_id
               LEFT JOIN team t ON t.id = su.team_id
          WHERE su.del_flag = 0) trainee
      ${ew.customSqlSegment}

  </select>

  <select id="getAllExamUserForPhase" resultType="org.jeecg.modules.training.vo.TraineeVO">
    SELECT *
    FROM (SELECT su.*
          FROM trainee su
          WHERE su.del_flag = 0) trainee
      ${ew.customSqlSegment}
  </select>

  <select id="getExamUserForPhaseItem" resultType="org.jeecg.modules.training.vo.TraineeVO">
    SELECT userPhaseItem.*
    FROM (SELECT su.*,
                 sd.route       AS route,
                 sd.depart_name AS depart_name,
                 t.team_name    AS team_name,
                 p.name         AS phase_name,
                 pi.type        AS phase_item_type,
                 pi.name        AS phase_item_name,
                 upi.status     AS user_phase_item_status,
                 upi.id         AS user_phase_item_id
          FROM user_phase_item upi
               INNER JOIN trainee su ON (upi.user_id = su.id AND su.del_flag = 0)
               INNER JOIN phase_item pi ON (upi.phase_item_id = pi.id AND pi.mode = 1)
               LEFT JOIN phase p ON p.id = pi.phase_id
               LEFT JOIN team t ON t.id = su.team_id
               LEFT JOIN sys_trainee_depart std ON su.id = std.trainee_id
               LEFT JOIN sys_depart sd ON sd.id = std.dep_id
          ) userPhaseItem
      ${ew.customSqlSegment}
  </select>

  <select id="getUserByOpenId" resultType="org.jeecg.modules.training.entity.Trainee">
    SELECT *
    FROM trainee
    WHERE open_id = #{openId}
      AND del_flag = 0;
  </select>
    <!--    获取会员即将过期的人数-->
    <select id="getWillExpireCount" resultType="java.lang.Long">
        select count(*)
                from (select te.*
                        from trainee te
                        where id in (select std.trainee_id
                                        from sys_trainee_depart std
                                        where std.dep_id in (select d.id
                                                                from sys_user_depart sy
                                                                left join sys_depart d on sy.dep_id = d.id
                                                                where route like concat(#{loginRoute}, '%'))
                                        )) trainees
                <if test="memberType == 1">
                    join sys_trainee_depart std on (std.dep_id = #{sysDepartId}
                                                        and trainees.id = std.trainee_id)
                </if>
                join packages p on (p.del_flag = 0 and p.status = 1 and datediff(p.expire_time, now()) &lt; 30
                <choose>
                    <when test="memberType == 0">
                        and p.id = trainees.current_packages_id
                    </when>
                    <when test="memberType == 1">
                        and p.id = std.packages_id
                    </when>
                </choose> )
                where trainees.del_flag = 0
    </select>

    <!--    获取会员已过期的人数-->
    <select id="getExpireCount" resultType="java.lang.Long">
        select count(*)
                from (select te.*
                        from trainee te
                        where id in (select std.trainee_id
                                        from sys_trainee_depart std
                                        where std.dep_id in (select d.id
                                                                from sys_user_depart sy
                                                                left join sys_depart d on sy.dep_id = d.id
                                                                where route like concat(#{loginRoute}, '%'))
                                        )) trainees
                <if test="memberType == 1">
                    join sys_trainee_depart std on (std.dep_id = #{sysDepartId}
                                                        and trainees.id = std.trainee_id)
                </if>
                join packages p on (p.del_flag = 0 and p.status = 0
                <choose>
                    <when test="memberType == 0">
                        and p.id = trainees.current_packages_id
                    </when>
                    <when test="memberType == 1">
                        and p.id = std.packages_id
                    </when>
                </choose> )
                where trainees.del_flag = 0
    </select>

    <!--    获取船员在指定公司的未过期企业套餐数量-->
    <select id="getNotExpireCount" resultType="java.lang.Long">
        select count(*)
        from sys_trainee_depart std
                 join packages on std.packages_id = packages.id
        where std.trainee_id = #{traineeId}
          and std.dep_id = #{sysDepartId}
          and packages.del_flag = 0
          and packages.type = 1
          and packages.status = 1;
    </select>

</mapper>