package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 套餐定价
 */
@Data
@TableName("package_price")
public class PackagePrice implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 平台套餐价格ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 基础版定价
     */
    private BigDecimal lev1Price;
    
    /**
     * 标准版定价
     */
    private BigDecimal lev2Price;
    
    /**
     * 尊享版定价
     */
    private BigDecimal lev3Price;
}
