package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 知识点
 * @Author: jeecg-boot
 * @Date: 2022-05-04
 * @Version: V1.0
 */
@Data
@TableName("knowledge")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "knowledge对象", description = "知识点")
public class Knowledge {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键id")
    private Long id;
    /**
     * 分类id
     */
    @Excel(name = "分类id", width = 15)
    @ApiModelProperty(value = "分类id")
    private Long categoryId;
    /**
     * 知识点名称
     */
    @Excel(name = "知识点名称", width = 15)
    @ApiModelProperty(value = "知识点名称")
    private String title;
    /**
     * 知识点详情描述
     */
    @Excel(name = "知识点详情描述", width = 15)
    @ApiModelProperty(value = "知识点详情描述")
    private String content;
    /**
     * 删除状态（0，正常，1已删除）
     */
    @Excel(name = "删除状态", width = 15, dicCode = "del_flag")
    @ApiModelProperty(value = "删除状态（0，正常，1已删除）")
    @TableLogic
	private Integer delFlag;
    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @Excel(name = "更新人", width = 15)
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    private String depRoute;

    /**
     * 关联行业id集合
     */
    private String industryIds;

    /**
     * 关联行业名称集合
     */
    @TableField(exist = false)
    private String industryNames;


    private String shareMode;

    private Long shareId;
}
