package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.Questions;
import org.jeecg.modules.training.vo.QuestionsVO;

import java.util.List;
import java.util.Map;

/**
 * @Description: 题库
 * @Author: jeecg-boot
 * @Date: 2022-05-04
 * @Version: V1.0
 */
public interface QuestionsMapper extends BaseMapper<Questions> {

    /**
     * 关联查询所有数据
     *
     * @return
     */
    List<QuestionsVO> getList(@Param("mp") Map<String, Object> param);

    /**
     * 关联查询所有数据d带分页
     *
     * @return
     */
    IPage<QuestionsVO> getPageList(Page<QuestionsVO> page, @Param("ew") QueryWrapper<QuestionsVO> queryWrapper);

    Questions getById(@Param("id") Long id);
}
