package org.jeecg.modules.training.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.training.async.phase.PushPhaseService;
import org.jeecg.modules.training.entity.*;
import org.jeecg.modules.training.service.*;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 培训阶段数据
 * @Author: hzk
 * @Date: 2022-06-15
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "培训阶段数据")
@RestController
@RequestMapping("/adm/phaseItem")
public class PhaseItemController extends JeecgController<PhaseItem, IPhaseItemService> {

    @Autowired
    private IUserPhaseItemService userPhaseItemService;

    @Autowired
    private ISyncStateService syncStateService;

    @Autowired
    private PushPhaseService userPhaseHandleService;

    @Autowired
    private ICategoryService categoryService;

    @Autowired
    private IPaperService paperService;

    @Autowired
    private IStudyService studyService;

    @Autowired
    private IPhaseService phaseService;
    @Autowired
    private ISysDepartService sysDepartService;

    /**
     * 列表查询
     *
     * @return
     */
    @GetMapping(value = "/list")
    public Result<?> list(@RequestParam("phaseId") Long phaseId, @RequestParam("parentId") Long parentId, @RequestParam(value = "showType", required = false, defaultValue = "0") Integer showType) {
        if (parentId == null) {
            parentId = 0L;
        }

        LambdaQueryWrapper<PhaseItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PhaseItem::getPhaseId, phaseId);
        queryWrapper.eq(PhaseItem::getParentId, parentId);

        if (showType != null) {
            if (showType == 1) {
                queryWrapper.eq(PhaseItem::getMode, 0);
            } else if (showType == 2) {
                queryWrapper.and(q -> q.eq(PhaseItem::getMode, 1).or().eq(PhaseItem::getType, 0));
            }
        }

        queryWrapper.orderByAsc(PhaseItem::getSeq);

        return Result.OK(service.list(queryWrapper));
    }

    /**
     * 添加
     *
     * @param phaseItem
     * @return
     */
    @AutoLog(value = "培训阶段数据-添加")
    @ApiOperation(value = "培训阶段数据-添加", notes = "培训阶段数据-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody PhaseItem phaseItem) {

        LambdaQueryWrapper<PhaseItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PhaseItem::getPhaseId, phaseItem.getPhaseId())
                .eq(PhaseItem::getType, phaseItem.getType())
                .eq(PhaseItem::getDataId, phaseItem.getDataId())
                .eq(PhaseItem::getDelFlag, CommonConstant.DEL_FLAG_0);

        if (!service.list(queryWrapper).isEmpty()) {
            return Result.error("此阶段已经存在此数据");
        }

        switch (phaseItem.getType()) {
            case 0:
                Category category = categoryService.getById(phaseItem.getDataId());
                phaseItem.setDataType(category.getType());
                phaseItem.setCategoryId(category.getParentId());
                phaseItem.setIndustryIds(category.getIndustryIds());
                break;
            case 1:
                Paper paper = paperService.getById(phaseItem.getDataId());
                phaseItem.setCategoryId(paper.getCategoryId());
                phaseItem.setDataType(paper.getType());
                phaseItem.setTimeFactor(paper.getTimeFactor());
                phaseItem.setPostFactor(paper.getPostFactor());
                phaseItem.setGroupFactor(paper.getGroupFactor());
                phaseItem.setIndustryIds(paper.getIndustryIds());
                break;
            case 2:
                Study study = studyService.getById(phaseItem.getDataId());
                phaseItem.setCategoryId(study.getCategoryId());
                phaseItem.setDataType(study.getType());
                phaseItem.setTimeFactor(study.getTimeFactor());
                phaseItem.setPostFactor(study.getNeedRead());
                phaseItem.setGroupFactor(study.getGroupFactor());
                phaseItem.setIndustryIds(study.getIndustryIds());
                break;
        }

        phaseItem.setDelFlag(0);
        phaseItem.setMode(0);

        if ("0".equals(phaseItem.getForUserId())) {
            phaseItem.setForUserId("");
        }

        //改成和上级相同的模式，注意是对应mode=1的情况
        if (phaseItem.getParentId() != 0) {
            PhaseItem pi = service.getById(phaseItem.getParentId());
            if (pi != null) {
                phaseItem.setMode(pi.getMode());
                phaseItem.setForUserId(pi.getForUserId());
            }
        }

        service.save(phaseItem);
        userPhaseHandleService.pushAll();

        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param phaseItem
     * @return
     */
    @AutoLog(value = "培训阶段数据-编辑")
    @ApiOperation(value = "培训阶段数据-编辑", notes = "培训阶段数据-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody PhaseItem phaseItem) {

        LambdaQueryWrapper<PhaseItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(PhaseItem::getId, phaseItem.getId())
                .eq(PhaseItem::getPhaseId, phaseItem.getPhaseId())
                .eq(PhaseItem::getDataType, phaseItem.getDataType())
                .eq(PhaseItem::getDataId, phaseItem.getDataId())
                .eq(PhaseItem::getDelFlag, CommonConstant.DEL_FLAG_0);

        if (!service.list(queryWrapper).isEmpty()) {
            return Result.error("此阶段已经存在此数据");
        }

        switch (phaseItem.getType()) {
            case 0:
                Category category = categoryService.getById(phaseItem.getDataId());
                phaseItem.setDataType(category.getType());
                phaseItem.setCategoryId(category.getParentId());
                phaseItem.setIndustryIds(category.getIndustryIds());
                break;
            case 1:
                Paper paper = paperService.getById(phaseItem.getDataId());
                phaseItem.setCategoryId(paper.getCategoryId());
                phaseItem.setDataType(paper.getType());
                phaseItem.setIndustryIds(paper.getIndustryIds());
                break;
            case 2:
                Study study = studyService.getById(phaseItem.getDataId());
                phaseItem.setCategoryId(study.getCategoryId());
                phaseItem.setDataType(study.getType());
                phaseItem.setIndustryIds(study.getIndustryIds());
                break;
        }

        if ("0".equals(phaseItem.getForUserId())) {
            phaseItem.setForUserId("");
        }

        service.updateById(phaseItem);
        syncStateService.resetSyncState(CommonConstant.PHASE_ITEM, phaseItem.getId().toString());
        userPhaseHandleService.pushAll();

        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "培训阶段数据-删除")
    @ApiOperation(value = "培训阶段数据-删除", notes = "培训阶段数据-删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "ids") String ids) {
        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<PhaseItem> items = service.listByIds(idList);
        List<Long> delIds = new ArrayList<>();

        //树状结构，还需要循环删除下级子数据
        for (PhaseItem item : items) {
            deleteAllItems(item, delIds);
        }

        if (!delIds.isEmpty()) {
            LambdaQueryWrapper<UserPhaseItem> q3 = new LambdaQueryWrapper<>();
            q3.in(UserPhaseItem::getPhaseItemId, delIds);
            List<UserPhaseItem> userPhaseItemList = userPhaseItemService.list(q3);
            for (UserPhaseItem item : userPhaseItemList) {
                userPhaseItemService.removeById(item.getId());
                syncStateService.resetSyncState(CommonConstant.USER_PHASE_ITEM, item.getId().toString());
            }
        }

        //已经清理过了，就不需要
        //pushPhaseService.pushAll();

        return Result.ok();
    }

    private void deleteAllItems(PhaseItem item, List<Long> delIds) {
        delIds.add(item.getId());

        service.removeById(item.getId());
        syncStateService.resetSyncState(CommonConstant.PHASE_ITEM, item.getId().toString());

        LambdaQueryWrapper<PhaseItem> q = new LambdaQueryWrapper<>();
        q.eq(PhaseItem::getParentId, item.getId());
        List<PhaseItem> items = service.list(q);

        for (PhaseItem item1 : items) {
            deleteAllItems(item1, delIds);
        }
    }
}
