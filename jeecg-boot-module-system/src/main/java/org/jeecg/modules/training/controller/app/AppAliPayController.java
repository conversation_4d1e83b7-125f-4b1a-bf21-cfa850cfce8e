package org.jeecg.modules.training.controller.app;


import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.diagnosis.DiagnosisUtils;
import com.alipay.api.domain.AlipayTradeQueryModel;
import com.alipay.api.domain.AlipayTradeWapPayModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.request.AlipayTradeWapPayRequest;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.alipay.api.response.AlipayTradeWapPayResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBoot401Exception;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.system.configuration.AliPayProperties;
import org.jeecg.modules.training.entity.Orders;
import org.jeecg.modules.training.service.IOrdersService;
import org.jeecg.modules.training.service.IPackagePriceCalculationService;
import org.jeecg.modules.training.service.TraineeService;
import org.jeecg.modules.training.vo.PriceInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/app/aliPay")
// 支付宝支付
public class AppAliPayController {

    @Autowired(required = false)
    private AlipayClient alipayClient;

    @Autowired(required = false)
    private AliPayProperties aliPayProperties;

    @Autowired
    private IPackagePriceCalculationService packagePriceCalculationService;

    @Autowired
    private IOrdersService ordersService;

    // 调用支付宝创建订单接口，并创建Orders对象
    @PostMapping("/createOrder")
    public Result<?> createOrder(
            @RequestParam Integer productCategorie,
            @RequestParam Long productDetail) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (oConvertUtils.isEmpty(loginUser)) {
            // 直接抛出401异常，会被全局异常处理器处理
            throw new JeecgBoot401Exception(CommonConstant.TOKEN_IS_INVALID_MSG);
        }

        // 调用支付宝创建订单接口，并创建Orders对象
        return Result.OK(createOrder(loginUser, productCategorie, productDetail));
    }

    // 调用支付宝创建订单接口，并创建Orders对象
    private JSONObject createOrder(LoginUser loginUser, Integer productCategorie, Long productDetail) {

        JSONObject jsonObject = new JSONObject();
        String orderString;

        // 构造请求参数以调用接口 以下都是必需参数
        AlipayTradeWapPayRequest request = new AlipayTradeWapPayRequest();
        AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();

        // 获取船员ID
        String traineeId = loginUser.getId();
        // 获取部门ID
        String sysDepartId = StrUtil.subAfter(loginUser.getDepRoute(), "/", true);
        // 获取商品信息(价格和说明)
        PriceInfoVO priceInfoVO = packagePriceCalculationService.getPrice(traineeId, sysDepartId, productCategorie, productDetail);
        // 获取支付金额
        BigDecimal amount = priceInfoVO.getPrice();
        // 生成支付宝订单号
        String outTradeNo = System.currentTimeMillis() + RandomUtil.randomNumbers(4);
        // 设置商户订单号
        model.setOutTradeNo(outTradeNo);
        // 设置订单总金额,标准十进制表示（不使用指数）
        model.setTotalAmount(amount.toPlainString());
        // 设置订单标题
        String productDescription = priceInfoVO.getDescription();
        model.setSubject(productDescription);
        // 设置销售产品码，商家和支付宝签约的产品码。手机网站支付为：QUICK_WAP_WAY
        model.setProductCode(CommonConstant.QUICK_WAP_WAY);

        request.setBizModel(model);
        request.setReturnUrl(aliPayProperties.getReturnUrl());
        request.setNotifyUrl(aliPayProperties.getNotifyUrl());

        try {
            AlipayTradeWapPayResponse response = alipayClient.pageExecute(request, "POST");

            if (response.isSuccess()) {
                orderString = response.getBody();
                // 根据商品类型设置商品描述
                Orders orders = ordersService.initOrders(loginUser, outTradeNo, amount, 0, productCategorie, productDetail, productDescription);
                jsonObject.put("orderString", orderString);
                jsonObject.put("orderId", orders.getId());
                log.info("创建订单成功！orderId：{}", orders.getId());
            } else {
                String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                log.error("调用创建订单接口失败，诊断信息diagnosisUrl：{}", diagnosisUrl);
                throw new JeecgBootException("支付失败！请联系管理人员");
            }

        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        }

        return jsonObject;
    }

    // 查询支付宝订单状态，并更新船员会员状态(来自APP的回调)
    @GetMapping("/tradeQuery")
    public Result<?> tradeQuery(@RequestParam(name = "orderId") String orderId) {

        log.info("主动查询支付宝订单id:{}的状态，并更新船员会员状态", orderId);

        Orders orders = ordersService.getById(orderId);
        if (orders == null) {
            log.error("系统中找不到orderId：{}对应的订单", orderId);
            return Result.error("开通失败！系统中找不到orderId对应的订单");
        }

        if (orders.getStatus() != 0) {
            log.info("该订单对应的船员已开通会员，请勿重复开通（orderId：{}）", orderId);
            return Result.OK("该订单对应的船员已开通会员，请勿重复开通");
        }

        if (orders.getOrdersNum() == null) {
            log.error("开通失败！该订单的订单号不存在！（orderId：{}）", orderId);
            return Result.error("开通失败！该订单的订单号不存在！");
        }

        // 获取查询结果
        String orderString = tradeQuery(orders);

        JSONObject jsonObject = JSONObject.parseObject(orderString);
        JSONObject responseValue  = jsonObject.getJSONObject(CommonConstant.RESPONSE_KEY);
        if (responseValue == null) {
            log.error("id ={}的orders记录，获取支付宝的alipay_trade_query_response值为空", orderId);
            return Result.error("开通失败！支付宝返回的支付信息为空");
        }

        String tradeStatus = responseValue.getString(CommonConstant.TRADE_STATUS);

        /*
                    TRADE_SUCCESS 可退款的商品交易成功
                    TRADE_FINISHED 不可退款的商品交易完成
                    上面两种情况可以开通会员
                */
        if (tradeStatus.equals(CommonConstant.TRADE_SUCCESS) ||
                tradeStatus.equals(CommonConstant.TRADE_FINISHED)) { // 进入开启会员的逻辑
            ordersService.openMember(orders);
        } else {
            log.info("开通失败，支付宝返回的支付状态不是支付成功（订单id：{}, 支付宝支付状态：{}）",
                    orders.getId(), tradeStatus);
            return Result.error("开通失败，支付宝返回的支付状态不是支付成功");
        }

        return Result.OK("接口执行成功！");
    }

    // 查询支付宝订单状态
    private String tradeQuery(Orders orders) {

        String orderString;

        // 构造请求参数以调用接口
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        AlipayTradeQueryModel model = new AlipayTradeQueryModel();

        // 设置订单支付时传入的商户订单号
        model.setOutTradeNo(orders.getOrdersNum());

        // 设置查询选项
        List<String> queryOptions = new ArrayList<>();
        // 交易结算信息
        queryOptions.add(CommonConstant.TRADE_SETTLE_INFO);
        model.setQueryOptions(queryOptions);

        request.setBizModel(model);

        AlipayTradeQueryResponse response;
        try {
            response = alipayClient.execute(request);

        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        }

        if (response.isSuccess()) {
            orderString = response.getBody();
        } else {
            String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            log.error("调用查询订单接口失败，诊断信息diagnosisUrl：{}", diagnosisUrl);
            throw new JeecgBootException("查询失败！查询支付宝订单状态出错");
        }

        return orderString;
    }

    // 接收支付宝返回的支付信息，并开通会员(来自支付宝的回调)
    @PostMapping("/notify")
    public void notify(HttpServletRequest request) {

        log.info("支付宝回调接口开始执行");

        //获取支付宝POST过来反馈信息
        Map<String, String> params = new HashMap<>();
        getParams(params, request);

        try {
            boolean flag = AlipaySignature.rsaCheckV1(params,aliPayProperties.getAlipayPublicKey(), "UTF-8","RSA2");
            if (flag) { // 数据验证成功，是支付宝发送的信息
                // 获取状态信息
                String outTradeNo = params.get(CommonConstant.OUT_TRADE_NO);
                String tradeStatus = params.get(CommonConstant.TRADE_STATUS);

                // 获取订单号对应的未支付状态的订单
                Orders orders = ordersService.lambdaQuery()
                        .eq(Orders::getOrdersNum, outTradeNo)
                        .one();
                if (orders == null) {
                    log.error("系统中找不到订单号outTradeNo：{}对应的订单", outTradeNo);
                    return;
                }

                if (orders.getStatus() != 0) {
                    log.info("该订单对应的船员已开通会员，请勿重复开通（orderId：{}）", orders.getId());
                    return;
                }
                /*
                    TRADE_SUCCESS 可退款的商品交易成功
                    TRADE_FINISHED 不可退款的商品交易完成
                    上面两种情况可以开通会员
                */
                if (tradeStatus.equals(CommonConstant.TRADE_SUCCESS) ||
                        tradeStatus.equals(CommonConstant.TRADE_FINISHED)) { // 进入开启会员的逻辑
                    ordersService.openMember(orders);
                } else {
                    log.info("开通失败，支付宝返回的支付状态不是支付成功（订单id：{}, 支付宝支付状态：{}）",
                            orders.getId(), tradeStatus);
                }

                log.info("支付宝回调接口执行完成");
            }
        } catch (AlipayApiException e) {
            log.error("支付宝回调接口执行失败！", e);
        }
    }

    // 获取支付宝发送的参数
    private void getParams(Map<String, String> params, HttpServletRequest request) {
        Map<String, String[]> requestParams = request.getParameterMap();
        for (String name : requestParams.keySet()) {
            String[] values = requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            //乱码解决，这段代码在出现乱码时使用。
            //valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
            params.put(name, valueStr);
        }
    }

}
