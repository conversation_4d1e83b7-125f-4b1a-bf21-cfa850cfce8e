package org.jeecg.modules.training.controller.admin;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PageMode;
import org.apache.pdfbox.pdmodel.interactive.documentnavigation.destination.PDPageDestination;
import org.apache.pdfbox.pdmodel.interactive.documentnavigation.destination.PDPageFitWidthDestination;
import org.apache.pdfbox.pdmodel.interactive.documentnavigation.outline.PDDocumentOutline;
import org.apache.pdfbox.pdmodel.interactive.documentnavigation.outline.PDOutlineItem;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.fields.FieldFilter;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.Md5Util;
import org.jeecg.common.util.NumberUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.common.util.poi.CustomJeecgEntityExcelView;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.training.async.materials.MaterialHandlerService;
import org.jeecg.modules.training.async.phase.PushPhaseService;
import org.jeecg.modules.training.async.share.PushShareService;
import org.jeecg.modules.training.entity.PhaseItem;
import org.jeecg.modules.training.entity.Study;
import org.jeecg.modules.training.service.IIndustryService;
import org.jeecg.modules.training.service.IPhaseItemService;
import org.jeecg.modules.training.service.IStudyService;
import org.jeecg.modules.training.util.PDFBoxUtils;
import org.jeecg.modules.training.util.PdfBoxData;
import org.jeecg.modules.training.vo.StudyVO;
import org.jeecg.modules.training.vo.UserStudy;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 学习表
 * @Author: hzk
 * @Date: 2022-09-02
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "学习表")
@RestController
@RequestMapping("/adm/study")
public class StudyController extends JeecgController<Study, IStudyService> {
    private final static SimpleDateFormat format = new SimpleDateFormat("yyyyMM");

    @Value(value = "${jeecg.path.dataRoot}")
    private String dataRoot;

    @Autowired
    private IStudyService studyService;

    @Autowired
    private NonStaticResourceHttpRequestHandler nonStaticResourceHttpRequestHandler;


    @Autowired
    private IPhaseItemService phaseItemService;

    @Autowired
    private MaterialHandlerService materialHandlerService;

    @Resource
    private ISyncStateService syncStateService;

    @Resource
    private PushPhaseService userPhaseHandleService;

    @Value("${jeecg.path.upload}")
    private String upLoadPath;

    @Autowired
    private ISysDepartService sysDepartService;

    @Autowired
    private IIndustryService industryService;

    @Autowired
    private PushShareService pushShareService;

    /**
     * 分页列表查询
     *
     * @param study
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "学习表-分页列表查询")
    @ApiOperation(value = "学习表-分页列表查询", notes = "学习表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(StudyVO study,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<StudyVO> queryWrapper = QueryGenerator.initQueryWrapper(study, req.getParameterMap());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.and(q -> q.lambda().eq(Study::getDepRoute, loginUser.getDepRoute()));

        Page<StudyVO> page = new Page<>(pageNo, pageSize);
        IPage<StudyVO> pageList = studyService.listPages(page, queryWrapper);
        for (StudyVO studyVO : page.getRecords()) {

            if (studyVO.getPostFactor() != null) {
                while (studyVO.getPostFactor().startsWith(","))
                    studyVO.setPostFactor(studyVO.getPostFactor().substring(1));
                while (studyVO.getPostFactor().endsWith(","))
                    studyVO.setPostFactor(studyVO.getPostFactor().substring(0, studyVO.getPostFactor().length() - 1));
            }

            if (studyVO.getTimeFactor() != null) {
                while (studyVO.getTimeFactor().startsWith(","))
                    studyVO.setTimeFactor(studyVO.getTimeFactor().substring(1));
                while (studyVO.getTimeFactor().endsWith(","))
                    studyVO.setTimeFactor(studyVO.getTimeFactor().substring(0, studyVO.getTimeFactor().length() - 1));
            }
            //if (studyVO.getNeedAttention() != null) {
            //    while (studyVO.getNeedAttention().startsWith(","))
            //        studyVO.setNeedAttention(studyVO.getNeedAttention().substring(1));
            //    while (studyVO.getNeedAttention().endsWith(","))
            //        studyVO.setNeedAttention(studyVO.getNeedAttention().substring(0, studyVO.getNeedAttention().length() - 1));
            //}
            if (studyVO.getNeedRead() != null) {
                while (studyVO.getNeedRead().startsWith(","))
                    studyVO.setNeedRead(studyVO.getNeedRead().substring(1));
                while (studyVO.getNeedRead().endsWith(","))
                    studyVO.setNeedRead(studyVO.getNeedRead().substring(0, studyVO.getNeedRead().length() - 1));
            }
            studyVO.setIndustryNames(industryService.getNamesByIndustryIds(studyVO.getIndustryIds()));
        }
        return Result.OK(pageList);
    }


    /**
     * 添加
     *
     * @param study
     * @return
     */
    @AutoLog(value = "学习表-添加")
    @ApiOperation(value = "学习表-添加", notes = "学习表-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody Study study) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        Study studyDb = studyService.getOne(new LambdaQueryWrapper<Study>()
                .eq(Study::getName, study.getName())
                .eq(Study::getDelFlag, CommonConstant.DEL_FLAG_0)
                .eq(Study::getDepRoute, loginUser.getDepRoute()));
        if (studyDb != null) {
            return Result.error("此学习资料已存在");
        }

        String industryIds = industryService.getIndustryIdsByNames(study.getIndustryNames());
        study.setIndustryIds(industryIds);
        study.setDepRoute(loginUser.getDepRoute());
        study.setDelFlag(CommonConstant.DEL_FLAG_0);
        study.setSize(0L);

        fixFactors(study);
        if (NumberUtil.equalsValue(study.getType(), CommonConstant.STUDY_TYPE_HTML)) {
            htmlString2File(study);
        }

        studyService.save(study);

        //add 则content就是原始文件
        File file = new File(dataRoot + File.separator + study.getContent());
        if (file.exists() && file.isFile()) { // 处理文件是异步的，在独立的线程执行
            //处理文件
            materialHandlerService.beginHandleMaterial(study, study.getType(), dataRoot + File.separator + study.getContent(), null, loginUser.getUsername());
        } else if (study.getType().equals(2)) {
            materialHandlerService.beginHandleMaterial(study, study.getType(), dataRoot + File.separator + study.getContent(), study.getContentHtml(), loginUser.getUsername());
        }

        // 添加培训阶段记录
        phaseItemService.createOneStudyPhaseItem(study);

        return Result.OK("添加成功！");
    }

    private void fixFactors(Study study) {
        if (study.getPostFactor() != null) {
            study.setPostFactor("," + study.getPostFactor().trim() + ",");
            if (study.getPostFactor().equals(",,")) {
                study.setPostFactor("");
            }
        }

        if (study.getTimeFactor() != null) {
            study.setTimeFactor("," + study.getTimeFactor().trim() + ",");
            if (study.getTimeFactor().equals(",,")) {
                study.setTimeFactor("");
            }
        }

        if (study.getNeedRead() != null) {
            study.setNeedRead("," + study.getNeedRead().trim() + ",");
            if (study.getNeedRead().equals(",,")) {
                study.setNeedRead("");
            }
        }
        if (StringUtils.isNotEmpty(study.getGroupFactor())) {
            study.setGroupFactor(study.getGroupFactor().trim().replaceAll(" ", "").replace('，', ','));
        }
    }

    /**
     * 编辑
     *
     * @param study
     * @return
     */
    @AutoLog(value = "学习表-编辑")
    @ApiOperation(value = "学习表-编辑", notes = "学习表-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody StudyVO study) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        Study studyByName = studyService.getOne(new LambdaQueryWrapper<Study>()
                .eq(Study::getName, study.getName())
                .eq(Study::getDelFlag, CommonConstant.DEL_FLAG_0)
                .eq(Study::getDepRoute, loginUser.getDepRoute()));

        if (studyByName != null && !studyByName.getId().equals(study.getId())) {
            return Result.error("此学习资料已存在");
        }

        //判断是否需要分享
        String industryIds;
        if (study.getIndustryNames() != null && !study.getIndustryNames().trim().isEmpty()) {
            industryIds = industryService.getIndustryIdsByNames(study.getIndustryNames());
            study.setIndustryIds(industryIds);
        }

        fixFactors(study);
        study.setDelFlag(CommonConstant.DEL_FLAG_0);
        if (NumberUtil.equalsValue(study.getType(), CommonConstant.STUDY_TYPE_HTML)) {
            htmlString2File(study);
        }

        Study studyOld = studyService.getById(study.getId());
        boolean needReCalc = false;

        if ((study.getReCalc() != null && study.getReCalc())) {
            needReCalc = true;
        }
        if (!Objects.equals(studyOld.getContent(), study.getContent())) {
            //删除旧文件
            try {
                //删除原始文件和压缩文件(如果有压缩文件的话)
                File compressFile = new File(dataRoot + File.separator + studyOld.getContent());
                File sourceFile = new File(dataRoot + File.separator + studyOld.getFilePath());
                if (compressFile.exists() && compressFile.isFile()) {
                    compressFile.delete();
                }
                if (!StringUtils.equals(studyOld.getContent(), studyOld.getFilePath())) {
                    if (sourceFile.exists() && sourceFile.isFile()) {
                        sourceFile.delete();
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
            }

            //修改了文件内容之后需要修改filePath
            study.setFilePath(study.getContent());
            needReCalc = true;
        } else {
            study.setFilePath(studyOld.getFilePath());

            File file = new File(dataRoot + File.separator + study.getContent());
            if (file.exists() && file.isFile()) {
                study.setSize(file.length());
                if (study.getCompressDpi() != null && !NumberUtil.equals(study.getCompressDpi(), 0)) {

                    if (NumberUtil.equals(study.getType(), CommonConstant.STUDY_TYPE_PDF)) {
                        //压缩pdf文件
                        needReCalc = true;
                    }
                    /* else if (NumberUtil.equals(study.getType(), CommonConstant.STUDY_TYPE_HTML)) {
                    //当学习资料类型是Html的时候，压缩html文件
                    handleHtml(file, study, uploadpath);
                    }*/
                }
            } else {
                study.setSize(0L);
            }
        }

        study.setUpdateTime(new Date());
		// 添加、更新培训阶段记录
        phaseItemService.createOneStudyPhaseItem(study);
        studyService.updateById(study);

        syncStateService.resetSyncState(CommonConstant.STUDY, study.getId().toString());

        LambdaUpdateWrapper<PhaseItem> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(PhaseItem::getGroupFactor, study.getGroupFactor())
                .set(PhaseItem::getDataType, study.getType())
                .set(PhaseItem::getPostFactor, study.getNeedRead())
                .set(PhaseItem::getGroupFactor, study.getGroupFactor())
                .eq(PhaseItem::getType, 2)
                .eq(PhaseItem::getDataId, study.getId().toString());

        phaseItemService.update(updateWrapper);

        userPhaseHandleService.pushAll(true);

        if (needReCalc) {
            if (study.getFilePath() == null) {
                study.setFilePath(study.getContent());
            }
            // 文件内容修改了，需要删除ES中之前的记录，创建新的
            studyService.deleteStudyToIndex(String.valueOf(study.getId()));
            materialHandlerService.beginHandleMaterial(study, study.getType(), dataRoot + File.separator + study.getFilePath(), study.getContentHtml(), loginUser.getUsername());
        } else { // 文件内容没有被修改
            studyService.updateStudyToIndex(study, false);
            //复制数据
            if ("admin".equals(loginUser.getUsername()) && "1".equals(study.getShareMode())) {
                pushShareService.pushData("study", study.getId().toString());
            }
        }

        return Result.OK("编辑成功!");
    }

    /**
     * 批量编辑
     *
     * @param study
     * @return
     */
    @AutoLog(value = "学习表-批量编辑")
    @ApiOperation(value = "学习表-批量编辑", notes = "学习表-批量编辑")
    @RequestMapping(value = "/updateBatch", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> updateBatch(@RequestBody Study study, HttpServletRequest request) {

        //借用content字段传递ids
        String ids = study.getContent();
        if (StringUtils.isBlank(ids)) {
            return Result.error("请选择要修改的学习资料");
        }

        String[] idList = ids.split(",");
        List<Study> studies = studyService.listByIds(Arrays.stream(idList).collect(Collectors.toSet()));

        for (Study study0 : studies) {

            if (study.getNeedDownload() != null) {
                study0.setNeedDownload(study.getNeedDownload());
            }

            if (study.getTotalTime() != null) {
                study0.setTotalTime(study.getTotalTime());
            }

            if (study.getPostFactor() != null) {
                study0.setPostFactor(study.getPostFactor());
            }
            if (study.getNeedRead() != null) {
                study0.setNeedRead(study.getNeedRead());
            }
//
//            if (study.getNeedAttention() != null) {
//                study0.setNeedAttention(study.getNeedAttention());
//            }

            if (study.getCategoryId() != null) {
                study0.setCategoryId(study.getCategoryId());
            }

            if (study.getTimeFactor() != null) {
                study0.setTimeFactor(study.getTimeFactor());
            }

            if (study.getDisabled() != null) {
                study0.setDisabled(study.getDisabled());
            }

            if (study.getDescription() != null) {
                study0.setDescription(study.getDescription());
            }

            study0.setUpdateTime(new Date());
            syncStateService.resetSyncState(CommonConstant.STUDY, study0.getId().toString());
        }

        studyService.updateBatchById(studies);
        for (Study study0 : studies) {
            LambdaUpdateWrapper<PhaseItem> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(PhaseItem::getGroupFactor, study0.getGroupFactor())
                    .set(PhaseItem::getDataType, study0.getType())
                    .set(PhaseItem::getPostFactor, study0.getNeedRead())
                    .set(PhaseItem::getGroupFactor, study0.getGroupFactor())
                    .eq(PhaseItem::getType, 2)
                    .eq(PhaseItem::getDataId, study0.getId().toString());

            phaseItemService.update(updateWrapper);

        }

        userPhaseHandleService.pushAll(true);

        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */

    @AutoLog(value = "学习表-通过id删除")
    @ApiOperation(value = "学习表-通过id删除", notes = "学习表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {

        Study study = studyService.getById(id);
        if (study != null) {
            studyService.removeById(id);
            syncStateService.resetSyncState(CommonConstant.STUDY, id);
            // 删除ES中的对应文档
            studyService.deleteStudyToIndex(id);

        }


        LambdaQueryWrapper<PhaseItem> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper.eq(PhaseItem::getType, 2)
                .eq(PhaseItem::getDataId, study.getId().toString());
        phaseItemService.remove(updateWrapper);

        userPhaseHandleService.pushAll(true);

        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "学习表-批量删除")
    @ApiOperation(value = "学习表-批量删除", notes = "学习表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {

        String[] idList = ids.split(",");
        for (String id : idList) {
            delete(id);
            studyService.deleteStudyToIndex(id);
            syncStateService.resetSyncState(CommonConstant.STUDY, id);
        }

        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "学习表-通过id查询")
    @ApiOperation(value = "学习表-通过id查询", notes = "学习表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        Study study = studyService.getById(id);
        return Result.OK(study);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param study
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StudyVO study) {

        // Step.1 组装查询条件
        QueryWrapper<StudyVO> queryWrapper = QueryGenerator.initQueryWrapper(study, request.getParameterMap());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.and(q -> q.lambda().eq(Study::getDepRoute, loginUser.getDepRoute()));


        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            queryWrapper.in("id", selectionList);
        }

        IPage<StudyVO> pageList = studyService.listPages(new Page<>(1, 1000000), queryWrapper);
        for (StudyVO studyVO : pageList.getRecords()) {
            if (studyVO.getPostFactor() != null) {
                while (studyVO.getPostFactor().startsWith(","))
                    studyVO.setPostFactor(studyVO.getPostFactor().substring(1));
                while (studyVO.getPostFactor().endsWith(","))
                    studyVO.setPostFactor(studyVO.getPostFactor().substring(0, studyVO.getPostFactor().length() - 1));
            }
            if (studyVO.getTimeFactor() != null) {
                while (studyVO.getTimeFactor().startsWith(","))
                    studyVO.setTimeFactor(studyVO.getTimeFactor().substring(1));
                while (studyVO.getTimeFactor().endsWith(","))
                    studyVO.setTimeFactor(studyVO.getTimeFactor().substring(0, studyVO.getTimeFactor().length() - 1));
            }
        }

        // Step.2 获取导出数据
        List<StudyVO> exportList = pageList.getRecords();

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new CustomJeecgEntityExcelView());
        if (request.getParameter(QueryGenerator.QUERY_COLUMNS) != null) {
            mv.addObject(NormalExcelConstants.EXPORT_FIELDS, request.getParameter(QueryGenerator.QUERY_COLUMNS));
        }
        //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, "学习资料");
        mv.addObject(NormalExcelConstants.CLASS, StudyVO.class);
        //update-begin--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置--------------------
        ExportParams exportParams = new ExportParams("学习资料报表", "导出人:" + loginUser.getRealname(), "学习资料");
        exportParams.setImageBasePath(upLoadPath);
        //update-end--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置----------------------

        exportParams.setType(ExcelType.XSSF);
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;

    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Study.class);
    }

    public void htmlString2File(Study study) {

        study.setContentHtml(study.getContent());

        String ymName = format.format(new Date());
        String fileName = DigestUtils.md5Hex(study.getContent().getBytes()) + ".html";

        //用于存储html字符串
        StringBuilder filePath = new StringBuilder();

        //文件路径(相对)
        filePath.append("StudyFile");
        filePath.append(File.separator);
        filePath.append("html");
        filePath.append(File.separator);
        filePath.append(ymName);
        filePath.append(File.separator);
        filePath.append(fileName);
        study.setContent(filePath.toString());
        study.setFilePath(filePath.toString());
    }


    /**
     * 文件上传方法
     *
     * @param request
     * @param response
     * @return
     */
    @PostMapping(value = "/upload")
    public Result<?> upload(HttpServletRequest request, HttpServletResponse response) {
        Result<String> result = new Result<>();

        String ymName = format.format(new Date());

        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        // 获取上传文件对象
        MultipartFile file = multipartRequest.getFile("file");
        String orgName ="";
        String ext = "";
        if (file != null) {
            orgName = file.getOriginalFilename();
        } else {
            return Result.error("上传失败！");
        }
        if (orgName != null) {
            // 获取文件后缀(转小写)
            ext = FilenameUtils.getExtension(orgName).toLowerCase();
        }

        String type;
        switch (ext) {
            case "pdf":
            case "docx":
                type = "pdf";
                break;
            case "html":
                type = "html";
                break;
            default:
                type = "video";
                break;
        }

        StringBuffer relatePath = new StringBuffer();
        relatePath.append("StudyFile");
        relatePath.append(File.separator);
        relatePath.append(type);
        relatePath.append(File.separator);
        relatePath.append(ymName);

        String endFile = saveUploadFile(file, relatePath.toString());

        if (oConvertUtils.isNotEmpty(endFile)) {
            result.setResult(endFile);
            result.setCode(200);
            result.setMessage("上传成功");
        } else {
            result.setMessage("上传失败！");
            result.setSuccess(false);
        }
        return result;
    }

    /**
     * 本地文件上传
     *
     * @param mf         文件
     * @param relatePath 自定义路径
     * @return
     */
    private String saveUploadFile(MultipartFile mf, String relatePath) {
        try {

            File dir = new File(dataRoot + File.separator + relatePath);
            if (!dir.exists()) {
                // 创建文件根目录
                dir.mkdirs();
            }

            // 获取文件名
            String orgName = CommonUtils.getFileName(mf.getOriginalFilename());
            String ext = FilenameUtils.getExtension(mf.getOriginalFilename()).toLowerCase();
            String fileName = Md5Util.md5Encode(DigestUtils.md5Hex(mf.getBytes()) + orgName + (new Date().getTime()), "UTF-8") + "." + ext;
            String saveFile = dir.getPath() + File.separator + fileName;
            File file = new File(saveFile);
            try {
                if (file.exists()) {
                    file.delete();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            //保存文件
            mf.transferTo(file);

            //返回文件名称
            String endFile = FilenameUtils.removeExtension(saveFile).substring(dataRoot.length() + 1) + "." + ext;
            return endFile;

        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return "";
    }

    /**
     * 根据id 获取学习文件
     *
     * @param request
     * @param response
     */
    @AutoLog(value = "学习表-据id 获取学习文件")
    @ApiOperation("学习表-据id 获取学习文件")
    @GetMapping(value = "/getStudyFile")
    @PostMapping(value = "/getStudyFile")
    public void getStudyFile(HttpServletRequest request, HttpServletResponse response) {

        FileInputStream fis = null;
        OutputStream outputStream = null;

        try {
            String id = request.getParameter("id");
            Study study = studyService.getById(id);
            if (study == null) {
                throw new JeecgBootException("学习资料不存在");
            }
            String path = dataRoot + File.separator + study.getContent();
            fis = new FileInputStream(path);
            if (NumberUtil.equalsValue(study.getType(), CommonConstant.STUDY_TYPE_PDF)) {
                response.setContentType("application/pdf");
            } else if (NumberUtil.equalsValue(study.getType(), CommonConstant.STUDY_TYPE_VIDEO)) {
                response.setContentType("video/mp4");
                request.setAttribute(NonStaticResourceHttpRequestHandler.ATTR_FILE, new File(path).toPath());
                nonStaticResourceHttpRequestHandler.handleRequest(request, response);
                return;
            } else if (NumberUtil.equalsValue(study.getType(), CommonConstant.STUDY_TYPE_HTML)) {
                response.setContentType("text/html;charset=UTF-8");
            } else if (NumberUtil.equalsValue(study.getType(), CommonConstant.STUDY_TYPE_HST)) {
                response.setContentType("application/pdf");
            }
            response.setContentLengthLong(new File(path).length());
            outputStream = response.getOutputStream();
            int len;
            byte[] buf = new byte[4096];
            while ((len = fis.read(buf)) != -1) {
                outputStream.write(buf, 0, len);
            }
            response.flushBuffer();
        } catch (Exception e) {
            log.error("获取学习文件失败", e.getMessage());
            response.setStatus(404);
            //e.printStackTrace();
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }

            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }


    /**
     * 根据id 下载学习文件
     *
     * @param request
     * @param response
     */
    @AutoLog(value = "学习表-据id 下载学习文件")
    @ApiOperation("学习表-据id 下载学习文件")
    @GetMapping(value = "/downloadStudyFile")
    @PostMapping(value = "/downloadStudyFile")
    public void downloadStudyFile(HttpServletRequest request, HttpServletResponse response) {

        FileInputStream fis = null;
        OutputStream outputStream = null;

        try {
            String id = request.getParameter("id");
            Study study = studyService.getById(id);
            if (study == null) {
                throw new JeecgBootException("学习资料不存在");
            }
            String path = dataRoot + File.separator + study.getContent();
            fis = new FileInputStream(path);
            response.setContentType("application/force-download");
            response.addHeader("Content-Disposition", "attachment;fileName=" + new String(study.getName().getBytes("UTF-8"), "iso-8859-1"));
            response.setContentLengthLong(new File(path).length());
            outputStream = response.getOutputStream();
            int len;
            byte[] buf = new byte[4096];
            while ((len = fis.read(buf)) != -1) {
                outputStream.write(buf, 0, len);
            }
            response.flushBuffer();
        } catch (Exception e) {
            log.error("下载学习文件失败", e.getMessage());
            response.setStatus(404);
            //e.printStackTrace();
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }

            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 根据学习资料id获取Json字符串
     */
    @GetMapping("/getToc")
    public Result getToc(@RequestParam("id") Long id) {
        Study study = studyService.getById(id);
        String content = study.getContent();
        String jsonFilePath = dataRoot + File.separator + FilenameUtils.removeExtension(content) + ".json";
        File file = new File(jsonFilePath);
        if (!file.exists()) {
            return Result.error("目录Json文件不存在");
        }


        FileInputStream is = null;
        try {
            is = new FileInputStream(jsonFilePath);
            int available = is.available();
            byte[] bytes = new byte[available];
            is.read(bytes);
            String jsonString = new String(bytes);

            Result<Object> result = Result.ok(jsonString);
            result.setMessage("");
            return result;
        } catch (Exception e) {
            //return Result.error("此文件没有目录信息");
            e.printStackTrace();
        } finally {
            try {
                is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return Result.error("修改失败，出现未知错误。");
    }

    /**
     * 根据前端选择的目录和首页页数修改pdf文档
     */
    @PostMapping("/editPdf")
    public Result editPdf(@RequestParam("id") String id,
                          @RequestParam("tocType") String tocType,
                          @RequestParam("firstPage") Integer firstPage) {
        Study study = studyService.getById(id);
        String content = study.getContent();
        String jsonFilePath = dataRoot + File.separator + FilenameUtils.removeExtension(content) + ".json";
        //1.更新study的目录类型以及首页页码
        study.setTocType(tocType);
        study.setFirstPage(firstPage);
        studyService.updateById(study);

        //2.根据所选的目录类型以及首页页码重新生成pdf
        FileInputStream is = null;
        PDDocument pdDocument = null;
        try {
            is = new FileInputStream(jsonFilePath);
            int available = is.available();
            byte[] bytes = new byte[available];
            is.read(bytes);
            String jsonString = new String(bytes);
            JSONArray jsonArray = JSONArray.parseArray(jsonString);
            String toc = null;
            if ("source".equals(tocType)) {
                toc = jsonArray.getJSONObject(1).getString("directory");
            } else if ("generate".equals(tocType)) {
                toc = jsonArray.getJSONObject(0).getString("directory");
            }
            //2.1修改文件
            List<PdfBoxData> allBookList = JSONArray.parseArray(toc, PdfBoxData.class);
            String filePath = dataRoot + File.separator + content;
            File file = new File(filePath);

            PDDocument document = PDFBoxUtils.load(file);
            PDDocumentOutline documentOutline = new PDDocumentOutline();
            document.getDocumentCatalog().setDocumentOutline(documentOutline);

            for (PdfBoxData pdfBoxData : allBookList) {
                PDPageDestination pageDestination = new PDPageFitWidthDestination();
                pageDestination.setPage(document.getPage(pdfBoxData.getPage() + firstPage - 2));
                PDOutlineItem bookmark = new PDOutlineItem();
                bookmark.setDestination(pageDestination);
                bookmark.setTitle(pdfBoxData.getTitle());
                documentOutline.addLast(bookmark);
            }

            documentOutline.openNode();
            document.getDocumentCatalog().setPageMode(PageMode.USE_OUTLINES);
            document.save(filePath);

            return Result.ok("修改成功");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            PDFBoxUtils.close(pdDocument);
            try {
                is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return Result.error("修改失败，出现未知错误。");
    }


    /**
     * 根据前端选择的目录和首页页数修改pdf文档
     */
    @PostMapping("/recheckVideo")
    public Result recheckVideo() {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        materialHandlerService.rechackVideo(loginUser.getUsername());
        return Result.OK();
    }


    /**
     * 查询当前登录用户可获取的分享的试卷列表 sharemodel = 2
     */
    @FieldFilter(value = {"categoryId", "categoryName", "chargeStatus", "content", "createBy",
            "createTime", "delFlag", "depRoute", "disabled", "feedbackStatus", "id",
            "industryIds", "name", "needDownload", "shareMode", "totalTime", "type"})
    @GetMapping("/getShareList")
    public Result<?> getSharePaperList(StudyVO studyVO,
                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                       HttpServletRequest req) {
        QueryWrapper<StudyVO> queryWrapper = QueryGenerator.initQueryWrapper(studyVO, req.getParameterMap());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        queryWrapper.in("share_mode", 1, 2);

        // 获取该部门所属行业的id集合
        String industryIds = sysDepartService.getDepartByOrgCode(loginUser.getOrgCode()).getIndustryIds();
        if (!industryIds.isEmpty()) {
            // 格式化行业id字符串
            industryIds = industryService.formatIndustryIdsExp(industryIds);
            queryWrapper.apply("industry_ids regexp {0}", industryIds);
        }

        Page<StudyVO> page = new Page<>(pageNo, pageSize);
        IPage<StudyVO> pageList = studyService.listPages(page, queryWrapper);

        return Result.ok(pageList);
    }


    /**
     * 设置用户选择的公共资料
     */
    @GetMapping("setShareStudy")
    public Result<?> setShareStudy(@RequestParam List<Long> ids) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 共享数据
        ids.forEach(item -> {
            service.shareStudy(item, loginUser.getDepRoute(), true);
        });

        return Result.ok("获取成功");
    }

    /**
     * 获取未完成学习的人员
     */
    @FieldFilter({"realname", "post", "teamName", "phaseName", "studyName"})
    @GetMapping("/getUnfinishedStudyUser")
    public Result<?> getUnfinishedStudyUser(StudyVO studyVO,
                                            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                            HttpServletRequest req) {
        QueryWrapper<StudyVO> queryWrapper = QueryGenerator.initQueryWrapper(studyVO, req.getParameterMap());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.likeRight("team_route", loginUser.getDepRoute());
        queryWrapper.likeRight("study_dep_route", loginUser.getDepRoute());

        Page<UserStudy> page = new Page<>(pageNo, pageSize);
        IPage<UserStudy> userStudyIPage = service.listUnfinishedStudyUserPage(page, queryWrapper);
        return Result.OK(userStudyIPage);
    }

}
