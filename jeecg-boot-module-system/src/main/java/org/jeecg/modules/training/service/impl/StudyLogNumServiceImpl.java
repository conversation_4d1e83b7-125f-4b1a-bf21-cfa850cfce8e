package org.jeecg.modules.training.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.modules.training.entity.StudyLogNum;
import org.jeecg.modules.training.mapper.StudyLogNumMapper;
import org.jeecg.modules.training.service.IStudyLogNumService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Description: 学习日志数量关系表
 * @Author: hzk
 * @Date: 2022-09-02
 * @Version: V1.0
 */
@Service
public class StudyLogNumServiceImpl extends ServiceImpl<StudyLogNumMapper, StudyLogNum> implements IStudyLogNumService {

    @Override
    public void saveLogNum(String userId, Long studyId) {
        LambdaQueryWrapper<StudyLogNum> query = new LambdaQueryWrapper<>();
        query.eq(StudyLogNum::getStudyId, studyId);
        query.eq(StudyLogNum::getUserId, userId);
        List<StudyLogNum> studyLogNums = baseMapper.selectList(query);
        if (CollectionUtils.isEmpty(studyLogNums)) {
            StudyLogNum studyLogNum = new StudyLogNum();
            studyLogNum.setStudyId(studyId);
            studyLogNum.setUserId(userId);
            studyLogNum.setNum(1);
            studyLogNum.setDelFlag(CommonConstant.DEL_FLAG_0);
            baseMapper.insert(studyLogNum);
        } else {
            StudyLogNum studyLogNum = studyLogNums.get(0);
            studyLogNum.setNum(studyLogNum.getNum() + 1);
            baseMapper.updateById(studyLogNum);
        }
    }
}
