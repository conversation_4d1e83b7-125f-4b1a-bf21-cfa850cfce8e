<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.StudyTimeUserMapper">

    <select id="getPageList" resultType="org.jeecg.modules.training.vo.StudyTimeUserVO">
        select * from (
        select
        ifnull(sum(stu.study_time),0) as study_time,
        su.realname
        from trainee su
        left join study_time_user stu on stu.user_id = su.id
        <if test="scope == 'week'">
            and WEEK(stu.date, 1) = WEEK(now(), 1)
            and YEAR(stu.date) = YEAR(CURDATE())
        </if>
        <if test="scope == 'month'">
            and MONTH(stu.date) = MONTH(NOW())
            and YEAR(stu.date) = YEAR(CURDATE())
        </if>
        where
        su.del_flag = 0
        and su.status = 1
        and stu.department_id like concat(#{depRoute},'%')
        group by su.id
        ) as t
        ${ew.customSqlSegment}
    </select>

    <!--    学习资料船员学习总时长 统计表专用-->
    <select id="getTotalTime" resultType="org.jeecg.modules.training.vo.StudyLogVO">
        select trainee.realname, sum(stu.study_time) as total_time
        from study_time_user stu
                 join trainee on trainee.id = stu.user_id
        where trainee.del_flag = 0
          and stu.date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
          and stu.department_id like CONCAT(#{depRoute}, '%')
          and trainee.dep_route like CONCAT(#{depRoute}, '%')
        group by stu.user_id
        order by total_time desc
        limit 10;
    </select>

</mapper>

