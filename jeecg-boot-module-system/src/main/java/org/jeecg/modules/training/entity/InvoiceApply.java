package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("invoice_apply")
@ApiModel(value = "InvoiceApply", description = "发票")
public class InvoiceApply {

    /**
     * 发票表主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 发票id
     */
    private Long invoiceId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNum;

    /**
     * 开票状态  未开:0  开票中:1  已开票:2  开票失败:3
     */
    @Range(min = 0, max = 3, message = "开票状态不正确")
    private Integer status;

    /**
     * 发票抬头名称
     */
    @NotBlank(message = "发票抬头名称不能为空")
    private String payerName;

    /**
     * 纳税人识别号
     */
    @NotBlank(message = "纳税人识别号不能为空")
    private String payerRegisterNo;

    /**
     * 开票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 用户接收电子发票邮箱
     */
    @Email(message = "邮箱格式不正确")
    @NotBlank(message = "邮箱不能为空")
    private String email;

    /**
     * 支付方式  0：支付宝  1：微信
     */
    private Integer payType;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
