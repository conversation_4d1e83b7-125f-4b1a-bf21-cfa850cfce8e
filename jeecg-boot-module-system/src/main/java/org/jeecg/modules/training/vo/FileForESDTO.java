package org.jeecg.modules.training.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FileForESDTO implements Serializable {
    // 学习资料id
    private String id;
    // 学习资料名称
    private String name;
    // 学习资料类型 和 Study一致 类型：0：pdf；1：video；2：html
    private Integer type;
    // 学习资料描述
    private String description;
    // 学习资料的Base64编码
    private String content;
}
