package org.jeecg.modules.training.controller.admin;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.training.service.StudyStatisticsService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@Api(tags = "学习考试统计信息")
@RestController
@RequestMapping("adm/studyStatistics")
public class StudyStatisticsController {

    @Resource
    private StudyStatisticsService studyStatisticsService;

    @ApiOperation(value = "学习日志表-分页列表查询", notes = "学习日志表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> list() {
        return Result.OK(studyStatisticsService.list());
    }
}
