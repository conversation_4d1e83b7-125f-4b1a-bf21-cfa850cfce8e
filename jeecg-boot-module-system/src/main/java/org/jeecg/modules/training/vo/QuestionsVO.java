package org.jeecg.modules.training.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.modules.training.entity.Questions;

import java.util.ArrayList;
import java.util.List;

/**
 * 题目展示类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年05月05日 19:17
 */
@Data
@ApiModel(value = "questionsVO对象", description = "题库VO")
public class QuestionsVO extends Questions {
    /**
     * 分类名称
     */
    //    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    /**
     * 知识点名称
     */
    //    @ApiModelProperty(value = "知识点名称")
    private String knowledgeTitle;

    /**
     * 题目选项
     */
    @ApiModelProperty(value = "题目选项")
    transient List<QuestionItemVO> itemList = new ArrayList<>();

    /**
     * 关联行业名称集合
     */
    private String industryNames;


}
