package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class Orders {

    @TableId(type = IdType.AUTO)
    private Long id;
    // 创建人姓名
    private String name;
    // 创建人ID
    private String userId;
    // 创建时间
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    // 订单号
    private String ordersNum;
    // 支付方式 0 支付宝，1 微信
    private Integer payType;
    // 支付时间
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date payTime;
    // 订单金额
    private BigDecimal amount;
    // 订单状态 0 未支付，1 已支付，2 已过期
    private Integer status;
    // 过滤路径
    private String depRoute;
    // 删除标记
    @TableLogic
    private Integer delFlag;
    // 开发票状态  未开:0  开票中:1  已开票:2  开票失败:3
    private Integer invoiceStatus;

    // 商品描述
    private String productDescription;
    // 商品类型 0:套餐升级, 1:套餐续费, 2:单独购买考试, 3:单独购买学习资料
    private Integer productCategorie;
    // 商品详情 productCategorie=0 or 1，此处返回套餐lev:( 1:基础版, 2:标准版, 3: 尊享版)；productCategorie=2 or 3，此处返回资料id:(paper_id or study_id)
    private Long productDetail;

}
