package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.StudyTimeFleet;
import org.jeecg.modules.training.vo.StudyTimeFleetVO;

import java.util.List;

/**
 * 船队每日学习时长表(StudyTimeFleet)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-12-22 10:47:41
 */
public interface StudyTimeFleetMapper extends BaseMapper<StudyTimeFleet> {

    Page<StudyTimeFleet> getPageList(Page<StudyTimeFleet> page,@Param("depRoute") String depRoute, @Param("ew") QueryWrapper<StudyTimeFleet> queryWrapper, @Param("scope") String scope);

    // 学习资料船队平均学习时长 统计表专用
    List<StudyTimeFleetVO> getAvgTime(String depRoute);
}

