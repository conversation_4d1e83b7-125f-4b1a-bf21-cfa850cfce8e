package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.StudyFeedback;
import org.jeecg.modules.training.vo.StudyFeedbackVO;

@Mapper
public interface StudyFeedbackMapper extends BaseMapper<StudyFeedback> {
    // 分页查询
    IPage<StudyFeedbackVO> listPages(Page<StudyFeedbackVO> page, @Param("ew") QueryWrapper<StudyFeedbackVO> queryWrapper);
}
