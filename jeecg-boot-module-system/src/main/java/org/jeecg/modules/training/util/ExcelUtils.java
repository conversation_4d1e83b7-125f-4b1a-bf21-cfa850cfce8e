package org.jeecg.modules.training.util;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * (导出工具类).
 *
 * <AUTHOR>
 */
public class ExcelUtils {

    public final static int EXCEL_2007 = 2007;

    public final static int EXCEL_2003 = 2003;

    public final static String EXCEL_2007_SUFFIX = ".xlsx,.xlsm";

    public final static String EXCEL_2003_SUFFIX = ".xls";

    /**
     * 获取绝对路径方法.
     *
     * @param request
     * @param relativeFileName
     * @return
     */
    public static String getRealFileName(HttpServletRequest request, String relativeFileName) {
        String contextRealPath = request.getSession().getServletContext().getRealPath("/");
        File realFile = new File(contextRealPath, relativeFileName);
        if (!realFile.getParentFile().exists()) {
            realFile.getParentFile().mkdirs();
        }
        String realFileName = realFile.getAbsolutePath();
        return realFileName;
    }

    public static HSSFWorkbook getExcelTemplate(String templateAbsPath) {
        HSSFWorkbook book = null;
        try {
            byte[] bookbyte = getFileInput(templateAbsPath);
            ByteArrayInputStream bais = new ByteArrayInputStream(bookbyte);
            book = new HSSFWorkbook(bais);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return book;
    }

    public static XSSFWorkbook getExcelTemplateByXSSFWorkbook(String templateAbsPath) {
        XSSFWorkbook book = null;
        try {
            byte[] bookbyte = getFileInput(templateAbsPath);
            ByteArrayInputStream bais = new ByteArrayInputStream(bookbyte);
            book = new XSSFWorkbook(bais);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return book;
    }

    public static byte[] getFileInput(String templateAbsPath) {
        FileInputStream fileInputStream = null;
        try {
            fileInputStream = new FileInputStream(templateAbsPath);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();

            byte[] readin = new byte[512];
            int length = 0;
            while ((length = fileInputStream.read(readin)) > 0) {
                baos.write(readin, 0, length);
            }
            return baos.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return new byte[0];
    }

    /**
     * <p>
     * 将单元格的内容取出,结果转换成String
     * </p>
     * <p>
     * 注:如果是数字型和常规型单元格,数字会自动带上小数点
     * </p>
     *
     * @param cell 单元格
     * @return String
     */
    public static String getCellValueString(Cell cell) {
        Object value = getCellValue(cell);
        if (value != null) {
            return ObjectUtils.toString(value);
        }
        return null;
    }

    /**
     * <p>
     * 将单元格的内容取出,结果转换成Integer
     * </p>
     *
     * @param cell 单元格
     * @return Integer
     */
    public static Integer getCellValueInteger(Cell cell) {
        String value = getCellValueString(cell);
        if (value != null) {
            return NumberUtils.toInt(value);
        }
        return null;
    }

    /**
     * <p>
     * 将单元格的内容取出,结果转换成Double
     * </p>
     *
     * @param cell 单元格
     * @return Double
     */
    public static Double getCellValueDouble(Cell cell) {
        String value = getCellValueString(cell);
        if (value != null) {
            return NumberUtils.toDouble(value);
        }
        return null;
    }

    /**
     * <p>
     * 将单元格的内容取出,结果转换成BigDecimal
     * </p>
     *
     * @param cell 单元格
     * @return BigDecimal
     */
    public static BigDecimal getCellValueBigDecimal(Cell cell) {
        String value = getCellValueString(cell);
        if (value != null) {
            return NumberUtils.createBigDecimal(value);
        }
        return null;
    }

    /**
     * <p>
     * 将单元格的内容取出,不做类型转换
     * </p>
     * <p>
     * 对于常规型单元格,数字会自动转换为double型
     * </p>
     * <p>
     * 对于公式型单元格,本方法只返回计算结果
     * </p>
     *
     * @param cell 单元格
     * @return Object 单元格的内容
     */
    public static Object getCellValue(Cell cell) {
        if (null != cell) {
            Object result = null;
            CellType type = cell.getCellType();
            switch (type) {
                case BLANK: {
                    result = "";
                    break;
                }
                case BOOLEAN: {
                    result = cell.getBooleanCellValue();
                    break;
                }
                case ERROR: {
                    result = "error";
                    break;
                }
                case NUMERIC: {
                    result = cell.getNumericCellValue();
                    break;
                }
                case FORMULA: {
                    switch (cell.getCachedFormulaResultType()) {
                        case STRING:
                            result = cell.getStringCellValue();
                            break;
                        case NUMERIC:
                            result = cell.getNumericCellValue();
                            break;
                        default:
                            result = cell.getStringCellValue();
                            break;
                    }
                    break;
                }
                case STRING: {
                    result = cell.getStringCellValue();
                    break;
                }
                default: {
                    result = cell + "";
                }
            }
            return result;
        }
        return null;
    }

    /**
     * 获取单元格值
     *
     * @param cell
     * @return
     * @throws ParseException
     */
    public static String getValue(Cell cell) throws ParseException {
        String value = "";
        switch (cell.getCellType()) {
            case NUMERIC: // 数值型
                if (HSSFDateUtil.isCellDateFormatted(cell)) {
                    // 如果是date类型则 ，获取该cell的date值
                    value = HSSFDateUtil.getJavaDate(cell.getNumericCellValue()).toString();
                    java.util.Date date1 = new Date(value);
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                    value = format.format(date1);
                } else {// 纯数字
                    value = String.valueOf(cell.getNumericCellValue()).trim();
                }
                break;
            //* 此行表示单元格的内容为string类型 *//*
            case STRING: // 字符串型
                value = cell.getStringCellValue().trim();
                break;
            case FORMULA:// 公式型
                // 读公式计算值
                value = String.valueOf(cell.getNumericCellValue()).trim();
                if (value.equals("NaN")) {// 如果获取的数据值为非法值,则转换为获取字符串
                    value = cell.getStringCellValue().toString().trim();
                }
                cell.getCellFormula();
                break;
            case BOOLEAN:// 布尔
                value = " " + cell.getBooleanCellValue();
                break;
            //* 此行表示该单元格值为空 *//*
            case BLANK: // 空值
                value = "";
                break;
            case ERROR: // 故障
                value = "";
                break;
            default:
                value = cell.getStringCellValue().toString().trim();
        }
        return value;
    }

    public static Workbook createWorkbook(MultipartFile uploadFile) {
        Workbook book = null;
        int excelType = ExcelUtils.getExcelType(uploadFile.getOriginalFilename());
        try {
            if (EXCEL_2003 == excelType) {
                book = new HSSFWorkbook(uploadFile.getInputStream());
            } else {
                book = new XSSFWorkbook(uploadFile.getInputStream());
                // 使用支持大数据的WorkBook
                // book = new SXSSFWorkbook(xssfbook);
            }
        } catch (Exception e) {
            throw new JeecgBootException(e);
        }
        return book;
    }

    /**
     * <p>
     * 获取Excel文件类型
     * </p>
     * <p>
     * 细节描述
     * </p>
     *
     * @return 2003, 2007
     */
    public static int getExcelType(String fullname) {
        int result = 0;
        if (StringUtils.isNotBlank(fullname)) {
            String suffix = FilenameUtils.getExtension(fullname);
            if (StringUtils.contains(EXCEL_2003_SUFFIX, suffix)) {
                result = EXCEL_2003;
            } else if (StringUtils.contains(EXCEL_2007_SUFFIX, suffix)) {
                result = EXCEL_2007;
            }
        }
        return result;
    }

    /**
     * 读取字符串
     */
    public static String getStr(Row row, int rownum, int colNum, String colName, boolean required, int len) {
        String str = ExcelRead.getCellValStr(row, colNum); // 获取row对象指定列号对应的单元格值
        str = str.replaceAll("[　*| *| *|\\s*]*", ""); // 去除空格
        if (StringUtils.isEmpty(str)) { // 判断值是否为空
            if (required) { // 为空且必填则抛出异常
                throw new JeecgBootException("第" + rownum + "行[" + colName + "]不能为空！");
            }
        } else { // 不为空，判断字符长度是否满足需求
            if (len > 0 && str.length() > len) {
                throw new JeecgBootException("第" + rownum + "行[" + colName + "]字符串长度不大于" + len + "！");
            }
        }
        return str;
    }
}
