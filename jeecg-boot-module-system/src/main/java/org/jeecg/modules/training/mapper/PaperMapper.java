package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.Paper;
import org.jeecg.modules.training.vo.PaperVO;

import java.util.List;

/**
 * @Description: 试卷库
 * @Author: huazhengkan
 * @Date: 2022-06-15
 * @Version: V1.0
 */
public interface PaperMapper extends BaseMapper<Paper> {

    IPage<PaperVO> listPapers(Page<PaperVO> page, @Param("ew") QueryWrapper<PaperVO> queryWrapper);

    List<PaperVO> listPapers(@Param("ew") QueryWrapper<PaperVO> queryWrapper);

    Paper getById(@Param("id") Long id);
}
