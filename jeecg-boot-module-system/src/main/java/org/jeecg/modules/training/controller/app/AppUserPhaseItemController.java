package org.jeecg.modules.training.controller.app;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.training.entity.Phase;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.entity.UserPhaseItem;
import org.jeecg.modules.training.service.IPhaseService;
import org.jeecg.modules.training.service.IUserPhaseItemService;
import org.jeecg.modules.training.service.TraineeService;
import org.jeecg.modules.training.util.ExamUtils;
import org.jeecg.modules.training.vo.UserPhaseItemVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 用户成长阶段
 * @Author: hzk
 * @Date: 2022-06-15
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "船员培训阶段数据")
@RestController
@RequestMapping("/app/userPhaseItem")
public class AppUserPhaseItemController extends JeecgController<UserPhaseItem, IUserPhaseItemService> {

    @Autowired
    private IUserPhaseItemService userPhaseItemService;

    @Autowired
    private TraineeService traineeService;

    @Autowired
    private IPhaseService phaseService;

    /**
     * 列表查询
     *
     * @return
     */
    @GetMapping(value = "/getMyPhaseItems")
    public Result<?> getMyPhaseItems(@RequestParam("phaseId") Long phaseId, @RequestParam(value = "parentId", required = false) Long parentId) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        if (parentId == null) {
            parentId = 0L;
        }

        List<UserPhaseItemVo> userPhaseItems = userPhaseItemService.getUserPhaseItems(loginUser.getId(), phaseId, parentId, false);
        return Result.OK(userPhaseItems);
    }


    /**
     * 列表查询
     *
     * @return
     */
    @GetMapping(value = "/getMyTodoItems")
    public Result<?> getMyTodoItems(@RequestParam("phaseId") Long phaseId,
                                    @RequestParam(value = "type") Integer type,
                                    @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                    @RequestParam(value = "pageSize", required = false) Integer pageSize) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        Phase phase = phaseService.getById(phaseId);
        if (phase == null) {
            throw new JeecgBootException("阶段不存在！");
        }

        List<UserPhaseItemVo> userPhaseItems = new ArrayList<>();
        switch (type) {
            case 1:
                userPhaseItems = userPhaseItemService.getUserTodoPapers(loginUser.getId(), phase.getLevel());
                break;
            case 2: {
                Trainee trainee = traineeService.getById(loginUser.getId());

                //上船时间
                String boardTime = ExamUtils.getUserBoardTimeName(trainee);

                //职务
                String post = "," + trainee.getPost() + ",";

                userPhaseItems = userPhaseItemService.getUserTodoStudies(loginUser.getId(), phase.getLevel(), boardTime, post, pageNo, pageSize);
            }
            break;
            case 3: {
                Trainee trainee = traineeService.getById(loginUser.getId());

                //上船时间
                String boardTime = ExamUtils.getUserBoardTimeName(trainee);

                //职务
                String post = "," + trainee.getPost() + ",";

                userPhaseItems = userPhaseItemService.getUserUpdatedStudies(phase.getLevel(), loginUser.getId(), boardTime, post, pageNo, pageSize, trainee.getDepRoute());
            }
            break;
        }

        return Result.OK(userPhaseItems);
    }

}
