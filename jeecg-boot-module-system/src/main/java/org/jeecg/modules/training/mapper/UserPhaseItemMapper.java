package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.UserPhaseItem;
import org.jeecg.modules.training.vo.UserPhaseItemVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【user_phase_item(用户特有的阶段数据明细)】的数据库操作Mapper
 * @createDate 2023-09-23 21:49:32
 * @Entity org.jeecg.modules.training.entity.UserPhaseItem
 */
public interface UserPhaseItemMapper extends BaseMapper<UserPhaseItem> {

    List<UserPhaseItemVo> getUserPhaseItems(@Param("userId") String userId,
                                            @Param("phaseId") Long phaseId,
                                            @Param("parentId") Long parentId,
                                            @Param("showAll") Boolean showAll);

    List<UserPhaseItemVo> getAllUserPhaseItems(@Param("userId") String userId, @Param("phaseId") Long phaseId);

    Long getUserTodoPapersCount(@Param("userId") String userId, @Param("phaseLevel") String phaseLevel);

    List<UserPhaseItemVo> getUserTodoPapers(@Param("userId") String userId, @Param("phaseLevel") String phaseLevel,
                                            @Param("start") Integer start, @Param("pageSize") Integer pageSize);

    Long getUserTodoStudiesCount(@Param("userId") String userId, @Param("phaseLevel") Integer phaseLevel,
                                 @Param("boardTime") String boardTime, @Param("posts") String posts);

    List<UserPhaseItemVo> getUserTodoStudies(@Param("userId") String userId, @Param("phaseLevel") Integer phaseLevel,
                                             @Param("boardTime") String boardTime, @Param("posts") String posts,
                                             @Param("start") Integer start, @Param("pageSize") Integer pageSize);

    Long getUserUpdatedStudiesCount(@Param("userId") String userId, @Param("phaseLevel") String phaseLevel,
                                    @Param("boardTime") String boardTime, @Param("posts") String posts,
                                    @Param("depRoute") String depRoute);

    List<UserPhaseItemVo> getUserUpdatedStudies(@Param("userId") String userId, @Param("phaseLevel") String phaseLevel,
                                                @Param("boardTime") String boardTime, @Param("posts") String posts,
                                                @Param("start") Integer start, @Param("pageSize") Integer pageSize,
                                                @Param("depRoute") String depRoute);

    void setStatus(@Param("id") Long id, @Param("status") Integer status);


    void removeByUserPhaseId(@Param("phaseId") Long phaseId, @Param("userId") String userId);

}




