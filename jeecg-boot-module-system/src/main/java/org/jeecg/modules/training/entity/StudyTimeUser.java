package org.jeecg.modules.training.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 受试人每日学习时长表(StudyTimeUser)表实体类
 *
 * <AUTHOR>
 * @since 2023-12-22 10:47:41
 */
@Data
@ApiModel("受试人每日学习时长")
public class StudyTimeUser {
    //ID
    private Long id;
    //用户ID
    private String userId;
    //日期
    private Date date;
    //学习时长
    @ApiModelProperty("学习时长，单位，分钟")
    private Double studyTime;
    //所属部门id
    private String departmentId;
    private Integer week;

}

