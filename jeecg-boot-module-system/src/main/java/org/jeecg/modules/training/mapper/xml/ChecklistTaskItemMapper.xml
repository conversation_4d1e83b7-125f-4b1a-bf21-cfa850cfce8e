<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.ChecklistTaskItemMapper">

  <select id="getByNodeStatus" resultType="org.jeecg.modules.training.vo.ChecklistTaskItemVO">
    select *,ci.is_first from checklist_task_item cti, checklist_item ci
    where ci.id = cti.checklist_item_id
    and checklist_task_id=#{checkTaskId}
    <if test="passed!=null and passed!=''">
      and node_status = 2
    </if>
    <if test="current!=null and current!=''">
      and node_status = 1
      <if test="post!=null and post!=''">
        and find_in_set( #{post}, posts)
      </if>
    </if>
    <if test="passedOrCurrent!=null and passedOrCurrent!=''">
      and ( (node_status = 1
      <if test="post!=null and post!=''">
        and find_in_set( #{post}, posts)
      </if>
      ) or node_status = 2)
    </if>

  </select>

  <select id="getCurrentTaskItems" resultType="org.jeecg.modules.training.vo.ChecklistTaskItemVO">
    select *,ci.is_first from checklist_task_item cti, checklist_item ci
    where ci.id = cti.checklist_item_id
    and (cti.node_status = 1 or cti.node_status = 0)
    and cti.checklist_task_id=#{checkTaskId}
    <if test="nodeId!=null and nodeId!=''">
      and ci.node_id = #{nodeId}
    </if>
    <if test="isFirst!=null and isFirst!=''">
      and ci.is_first = true
    </if>
  </select>

  <select id="getByTaskId" resultType="org.jeecg.modules.training.vo.ChecklistTaskItemVO">
    SELECT *, ci.is_first
    FROM checklist_task_item cti,
         checklist_item ci
    WHERE cti.checklist_item_id = ci.id
      AND cti.checklist_task_id = #{taskId}
  </select>

  <update id="updateNodeStatus">
    UPDATE checklist_item ci,
    checklist_task_item cti
    SET cti.node_status = #{nodeStatus}
    WHERE ci.id = cti.checklist_item_id
    AND cti.checklist_task_id = #{taskId}
    <if test="exclusionNodeId==null or exclusionNodeId==''">
      AND ci.node_id = #{nodeId}
    </if>
    <if test="exclusionNodeId!=null and exclusionNodeId!=''">
      AND ci.node_id &lt;&gt; #{nodeId}
    </if>
  </update>

</mapper>
