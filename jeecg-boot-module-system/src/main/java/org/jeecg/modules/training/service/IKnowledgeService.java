package org.jeecg.modules.training.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.training.entity.Knowledge;
import org.jeecg.modules.training.vo.KnowledgeVo;

import java.util.List;
import java.util.Map;

/**
 * @Description: 知识点
 * @Author: huazhengkan
 * @Date: 2022-05-04
 * @Version: V1.0
 */
public interface IKnowledgeService extends IService<Knowledge> {

    /**
     * 逻辑删除
     *
     * @param id
     */
    Result<Knowledge> detleteById(String id);

    /**
     * 逻辑批量删除
     *
     * @param ids
     */
    Result<Knowledge> deleteBatch(List<String> ids);

    /**
     * 查询list
     *
     * @param page
     * @param params
     * @return
     */
    IPage<KnowledgeVo> getList(Page<KnowledgeVo> page, Map<String, Object> params);

    IPage<KnowledgeVo> getPageList(Page<KnowledgeVo> page, QueryWrapper<KnowledgeVo> queryWrapper);

    Long shareKnowledge(Long knowledgeId, String route, boolean force);
}
