package org.jeecg.modules.training.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.training.entity.Team;
import org.jeecg.modules.training.mapper.TeamMapper;
import org.jeecg.modules.training.service.TeamService;
import org.jeecg.modules.training.vo.TeamVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 团队
 */
@Service
@Slf4j
public class TeamServiceImpl extends ServiceImpl<TeamMapper, Team> implements TeamService {

    @Autowired(required = false)
    private TeamMapper teamMapper;

    /**
     * 根据船名和导入人的部门路径进行查询
     *
     * @param teamName
     * @param route
     * @return
     */
    @Override
    public Team queryTeamByName(String teamName, String route) {

        return this.getOne(new LambdaQueryWrapper<Team>()
                .eq(Team::getTeamName, teamName)
                .eq(Team::getRoute, route));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Team saveTeamData(Team team, String depRoute) {
        if (team != null) {
            if (team.getParentId() == null) {
                team.setParentId("");
            }
            // 新创建的船的Route为 当前登录用户部门路径
            String id = UUID.randomUUID().toString().replace("-", "");
            team.setId(id);
            team.setRoute(depRoute);
            team.setOrgType("1");
            team.setCreateTime(new Date());
            team.setDelFlag(CommonConstant.DEL_FLAG_0);

            this.save(team);
            return team;
        }
        return null;
    }

    @Override
    public List<Team> getTeamList(QueryWrapper<Team> queryWrapper) {
        return teamMapper.getTeamList(queryWrapper);
    }

    @Override
    public List<Team> queryTreeListByPid(String parentId, String ids, String searchKey) {
        LambdaQueryWrapper<Team> lqw = new LambdaQueryWrapper<>();
        lqw.eq(Team::getDelFlag, CommonConstant.DEL_FLAG_0);
        lqw.like(!oConvertUtils.isEmpty(searchKey), Team::getTeamName, searchKey);

        if (oConvertUtils.isNotEmpty(ids)) {
            lqw.in(Team::getId, (Object[]) ids.split(","));
        } else if (oConvertUtils.isEmpty(searchKey)) {
            if (oConvertUtils.isEmpty(parentId)) {
                lqw.and(q -> q.isNull(true, Team::getParentId).or().eq(true, Team::getParentId, ""));
            } else {
                lqw.eq(true, Team::getParentId, parentId);
            }
        }

        lqw.orderByDesc(Team::getTeamOrder);

        List<Team> list = list(lqw);

        return list;

    }

    @Override
    public Page<TeamVO> pageAll(Page<TeamVO> page, QueryWrapper<TeamVO> queryWrapper) {
        return baseMapper.pageAll(page, queryWrapper);
    }
}
