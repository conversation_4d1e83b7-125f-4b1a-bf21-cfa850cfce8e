package org.jeecg.modules.training.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.fields.FieldFilter;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.training.async.phase.PushPhaseService;
import org.jeecg.modules.training.async.share.PushShareService;
import org.jeecg.modules.training.entity.Phase;
import org.jeecg.modules.training.entity.PhaseItem;
import org.jeecg.modules.training.entity.UserPhase;
import org.jeecg.modules.training.entity.UserPhaseItem;
import org.jeecg.modules.training.service.*;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 培训阶段
 * @Author: hzk
 * @Date: 2022-06-15
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "培训阶段")
@RestController
@RequestMapping("/adm/phase")
public class PhaseController extends JeecgController<Phase, IPhaseService> {

    @Autowired
    private IPhaseService phaseService;

    @Autowired
    private IPhaseItemService phaseItemService;

    @Autowired
    private IUserPhaseService userPhaseService;

    @Autowired
    private IUserPhaseItemService userPhaseItemService;

    @Autowired
    private ISyncStateService syncStateService;

    @Autowired
    private PushPhaseService userPhaseHandleService;

    @Autowired
    private ISysDepartService sysDepartService;

    @Autowired
    private IIndustryService industryService;

    @Autowired
    private PushShareService pushShareService;

    /**
     * 列表查询
     *
     * @return
     */
    @GetMapping(value = "/list")
    @FieldFilter({"autoPush", "createBy", "createTime", "delFlag", "depRoute", "icon", "id", "industryIds", "industryNames", "level", "name", "rule", "shareMode", "updateBy", "updateTime"})
    public Result<?> list() {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        LambdaQueryWrapper<Phase> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(Phase::getDepRoute, loginUser.getDepRoute());
        queryWrapper.orderByAsc(Phase::getLevel);

        List<Phase> list = phaseService.list(queryWrapper);
        list.forEach(item -> {
            item.setIndustryNames(industryService.getNamesByIndustryIds(item.getIndustryIds()));
        });


        return Result.OK(list);
    }

    /**
     * 添加
     *
     * @param phase
     * @return
     */
    @AutoLog(value = "培训阶段添加")
    @ApiOperation(value = "培训阶段添加", notes = "培训阶段添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody Phase phase) {

        phase.setDelFlag(CommonConstant.DEL_FLAG_0);
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        LambdaQueryWrapper<Phase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Phase::getLevel, phase.getLevel());
        queryWrapper.eq(Phase::getCreateBy, loginUser.getUsername());
        if (!service.list(queryWrapper).isEmpty()) {
            return Result.error("此阶段序号已经存在，序号不能重复");
        }


        phase.setDepRoute(loginUser.getDepRoute());

        String industryIds = null;
        if (phase.getIndustryNames() != null && !phase.getIndustryNames().trim().isEmpty()) {
            industryIds = industryService.getIndustryIdsByNames(phase.getIndustryNames());
            phase.setIndustryIds(industryIds);
        }
        service.save(phase);

        //复制数据
        if ("admin".equals(loginUser.getUsername()) && StringUtils.isNotBlank(phase.getIndustryNames()) && "1".equals(phase.getShareMode())) {

            pushShareService.pushData("phase", phase.getId().toString());

        }

        userPhaseHandleService.pushAll();
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param phase
     * @return
     */
    @AutoLog(value = "培训阶段编辑")
    @ApiOperation(value = "培训阶段编辑", notes = "培训阶段编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody Phase phase) {

        Long shareId = phase.getId();
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        String industryIds = null;

        phase.setDelFlag(CommonConstant.DEL_FLAG_0);

        LambdaQueryWrapper<Phase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Phase::getLevel, phase.getLevel()).ne(Phase::getId, phase.getId());
        queryWrapper.isNull(Phase::getShareId);
        queryWrapper.eq(Phase::getCreateBy, loginUser.getUsername());
        if (service.count(queryWrapper) > 0) {
            return Result.error("此阶段序号已经存在，序号不能重复");
        }
        if (phase.getIndustryNames() != null && !phase.getIndustryNames().trim().isEmpty()) {
            industryIds = industryService.getIndustryIdsByNames(phase.getIndustryNames());
            phase.setIndustryIds(industryIds);
        }

        service.updateById(phase);

        //复制数据,sharemode修改成1的时候才需要修改
        if ("admin".equals(loginUser.getUsername()) && "1".equals(phase.getShareMode())) {

            pushShareService.pushData("phase", phase.getId().toString());

        }

        syncStateService.resetSyncState(CommonConstant.PHASE, phase.getId().toString());
        userPhaseHandleService.pushAll();

        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "培训阶段通过id删除")
    @ApiOperation(value = "培训阶段通过id删除", notes = "培训阶段通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "ids") String ids) {
        List<String> phaseIds = Arrays.asList(ids.split(","));
        service.removeByIds(phaseIds);
        for (String id : phaseIds) {
            syncStateService.resetSyncState(CommonConstant.PHASE, id);
        }

        LambdaQueryWrapper<PhaseItem> q1 = new LambdaQueryWrapper<>();
        q1.in(PhaseItem::getPhaseId, phaseIds);
        List<PhaseItem> phaseItemList = phaseItemService.list(q1);
        for (PhaseItem item : phaseItemList) {
            phaseItemService.removeById(item.getId());
            syncStateService.resetSyncState(CommonConstant.PHASE_ITEM, item.getId().toString());
        }


        LambdaQueryWrapper<UserPhase> q2 = new LambdaQueryWrapper<>();
        q2.in(UserPhase::getPhaseId, phaseIds);
        List<UserPhase> userPhaseList = userPhaseService.list(q2);
        for (UserPhase item : userPhaseList) {
            userPhaseService.removeById(item.getId());
            syncStateService.resetSyncState(CommonConstant.USER_PHASE, item.getId().toString());
        }

        if (!userPhaseList.isEmpty() && !phaseItemList.isEmpty()) {
            LambdaQueryWrapper<UserPhaseItem> q3 = new LambdaQueryWrapper<>();
            q3.in(UserPhaseItem::getPhaseItemId, phaseItemList.stream().map(PhaseItem::getId).collect(Collectors.toList()));
            List<UserPhaseItem> userPhaseItemList = userPhaseItemService.list(q3);
            for (UserPhaseItem item : userPhaseItemList) {
                userPhaseItemService.removeById(item.getId());
                syncStateService.resetSyncState(CommonConstant.USER_PHASE_ITEM, item.getId().toString());
            }
        }

        return Result.ok();
    }

    /**
     * 查询当前登录用户可获取的分享的试卷列表 sharemodel = 2
     */
    @GetMapping("/getShareList")
    @FieldFilter({"autoPush", "createBy", "createTime", "delFlag", "depRoute", "icon", "id", "industryIds", "level", "name", "rule", "shareMode", "updateBy", "updateTime"})
    public Result getSharePaperList(Phase phase,
                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                    HttpServletRequest req) {
        QueryWrapper<Phase> queryWrapper = QueryGenerator.initQueryWrapper(phase, req.getParameterMap());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        queryWrapper.in("share_mode", 1, 2);

        // 获取该部门所属行业的id集合
        String industryIds = sysDepartService.getDepartByOrgCode(loginUser.getOrgCode()).getIndustryIds();
        if (!industryIds.isEmpty()) {
            // 格式化行业id字符串
            industryIds = industryService.formatIndustryIdsExp(industryIds);
            queryWrapper.apply("industry_ids regexp {0}", industryIds);
        }

        Page page = new Page(pageNo, pageSize);
        service.page(page, queryWrapper);

        return Result.ok(page);
    }


    /**
     * 设置用户选择的公共资料
     */
    @GetMapping("setSharePhase")
    public Result<?> setSharePhase(@RequestParam List<Long> ids) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 共享数据
        ids.forEach(item -> {
            service.sharePhase(item, loginUser.getDepRoute(), true);
        });

        return Result.ok("获取成功");
    }
}
