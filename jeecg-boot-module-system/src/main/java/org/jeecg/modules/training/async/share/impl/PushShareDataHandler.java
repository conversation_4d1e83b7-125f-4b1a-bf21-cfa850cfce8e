package org.jeecg.modules.training.async.share.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.entity.SysPosition;
import org.jeecg.modules.training.async.share.PushShareHandler;
import org.jeecg.modules.training.entity.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@Scope("prototype")
public class PushShareDataHandler extends PushShareHandler {

    private String tableName;
    private String dataId;

    public void run() {
        if (StringUtils.isBlank(tableName))
            return;

        shareDeparts();

        if (tableName.equals("paper"))
            pushPaperService.pushAll();

        if (tableName.equals("phase"))
            pushPhaseService.pushAll();

    }

    protected void autoShareData(SysDepart depart, String industryIds) {
        log.info("开始处理共享： {}", depart.getDepartName());
        switch (tableName) {
            case "study":
                shareStudies(depart, industryIds);
                break;
            case "paper":
                sharePapers(depart, industryIds);
                break;
            case "phase":
                sharePhases(depart, industryIds);
                break;
            case "category":
                shareCategories(depart, industryIds);
                break;
            case "knowledge":
                shareKnowledges(depart, industryIds);
                break;
            case "questions":
                shareQuestions(depart, industryIds);
                break;
            case "sys_position":
                shareSysPositions(depart, industryIds);
                break;
            case "rule":
                shareRules(depart, industryIds);
                break;
            default:
                break;
        }
        log.info("完成处理共享： {}", depart.getDepartName());
    }

    private void shareRules(SysDepart depart, String industryIds) {
        List<Rule> list = ruleService.list(new LambdaQueryWrapper<Rule>()
                .isNull(Rule::getShareId)
                .apply("industry_ids regexp {0}", industryIds)
                .eq(!StringUtils.isEmpty(dataId), Rule::getId, dataId)
                .eq(Rule::getShareMode, "1"));
        log.info("开始共享：{} 个 rule for {}", list.size(), depart.getDepartName());
        list.forEach(rule -> ruleService.shareRule(rule.getId(), depart.getRoute(), force));
        log.info("完成共享：{} 个 rule for {}", list.size(), depart.getDepartName());
    }

    private void shareSysPositions(SysDepart depart, String industryIds) {
        List<SysPosition> list = sysPositionService.list(new LambdaQueryWrapper<SysPosition>()
                .isNull(SysPosition::getShareId)
                .apply("industry_ids regexp {0}", industryIds)
                .eq(!StringUtils.isEmpty(dataId), SysPosition::getId, dataId)
                .eq(SysPosition::getShareMode, "1"));
        log.info("开始共享：{} 个 sysPosition for {}", list.size(), depart.getDepartName());
        list.forEach(sysPosition -> sysPositionService.shareSysPosition(sysPosition.getId(), depart.getRoute(), force));
        log.info("完成共享：{} 个 sysPosition for {}", list.size(), depart.getDepartName());
    }

    private void shareQuestions(SysDepart depart, String industryIds) {
        List<Questions> list = questionsService.list(new LambdaQueryWrapper<Questions>()
                .isNull(Questions::getShareId)
                .apply("industry_ids regexp {0}", industryIds)
                .eq(!StringUtils.isEmpty(dataId), Questions::getId, dataId)
                .eq(Questions::getShareMode, "1"));
        log.info("开始共享：{} 个 questions for {}", list.size(), depart.getDepartName());
        list.forEach(questions -> questionsService.shareQuestion(questions.getId(), depart.getRoute(), force));
        log.info("完成共享：{} 个 questions for {}", list.size(), depart.getDepartName());
    }

    private void shareKnowledges(SysDepart depart, String industryIds) {
        List<Knowledge> list = knowledgeService.list(new LambdaQueryWrapper<Knowledge>()
                .isNull(Knowledge::getShareId)
                .apply("industry_ids regexp {0}", industryIds)
                .eq(!StringUtils.isEmpty(dataId), Knowledge::getId, dataId)
                .eq(Knowledge::getShareMode, "1"));
        log.info("开始共享：{} 个 knowledge for {}", list.size(), depart.getDepartName());
        list.forEach(knowledge -> knowledgeService.shareKnowledge(knowledge.getId(), depart.getRoute(), force));
        log.info("完成共享：{} 个 knowledge for {}", list.size(), depart.getDepartName());
    }

    private void shareCategories(SysDepart depart, String industryIds) {
        List<Category> list = categoryService.list(new LambdaQueryWrapper<Category>()
                .isNull(Category::getShareId)
                .apply("industry_ids regexp {0}", industryIds)
                .eq(!StringUtils.isEmpty(dataId), Category::getId, dataId)
                .eq(Category::getShareMode, "1")
                .orderByAsc(Category::getParentId));
        log.info("开始共享：{} 个 category for {}", list.size(), depart.getDepartName());
        list.forEach(category -> categoryService.shareCategory(category.getId(), depart.getRoute(), force));
        log.info("完成共享：{} 个 category for {}", list.size(), depart.getDepartName());
    }

    private void sharePhases(SysDepart depart, String industryIds) {
        List<Phase> list = phaseService.list(new LambdaQueryWrapper<Phase>()
                .isNull(Phase::getShareId)
                .apply("industry_ids regexp {0}", industryIds)
                .eq(!StringUtils.isEmpty(dataId), Phase::getId, dataId)
                .eq(Phase::getShareMode, "1"));
        log.info("开始共享：{} 个 phase for {}", list.size(), depart.getDepartName());
        list.forEach(phase -> phaseService.sharePhase(phase.getId(), depart.getRoute(), force));
        log.info("完成共享：{} 个 phase for {}", list.size(), depart.getDepartName());
    }

    private void sharePapers(SysDepart depart, String industryIds) {
        List<Paper> list = paperService.list(new LambdaQueryWrapper<Paper>()
                .isNull(Paper::getShareId)
                .apply("industry_ids regexp {0}", industryIds)
                .eq(!StringUtils.isEmpty(dataId), Paper::getId, dataId)
                .eq(Paper::getShareMode, "1"));
        log.info("开始共享：{} 个 paper for {}", list.size(), depart.getDepartName());
        list.forEach(paper -> paperService.sharePaper(paper.getId(), depart.getRoute(), force));
        log.info("完成共享：{} 个 paper for {}", list.size(), depart.getDepartName());
    }

    private void shareStudies(SysDepart depart, String industryIds) {
        List<Study> list = studyService.list(new LambdaQueryWrapper<Study>()
                .isNull(Study::getShareId)
                .apply("industry_ids regexp {0}", industryIds)
                .eq(!StringUtils.isEmpty(dataId), Study::getId, dataId)
                .eq(Study::getShareMode, "1"));
        log.info("开始共享：{} 个 study for {}", list.size(), depart.getDepartName());
        list.forEach(study -> studyService.shareStudy(study.getId(), depart.getRoute(), force));
        log.info("完成共享：{} 个 study  for {}", list.size(), depart.getDepartName());
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }
}
