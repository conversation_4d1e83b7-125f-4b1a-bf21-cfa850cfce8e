package org.jeecg.modules.training.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.training.entity.Category;
import org.jeecg.modules.training.entity.Study;
import org.jeecg.modules.training.vo.StudyPage;
import org.jeecg.modules.training.vo.StudyVO;
import org.jeecg.modules.training.vo.UserStudy;

import java.util.List;

/**
 * @Description: 学习表
 * @Author: hzk
 * @Date: 2022-09-02
 * @Version: V1.0
 */
public interface IStudyService extends IService<Study> {

    /**
     * 分页列表查询
     *
     * @param page
     * @param queryWrapper
     * @return
     */
    IPage<StudyVO> listPages(Page<StudyVO> page, QueryWrapper<StudyVO> queryWrapper);

    /**
     * APP端查询学习资料列表（基础版本）
     *
     * <p>
     * 此方法提供基础的学习资料查询功能，不包含会员权限过滤和自定义排序。
     * 适用于不需要会员制功能的场景（例如: COSCO）
     * </p>
     *
     * @param studyPage 分页参数
     * @param categoryId 分类ID
     * @param userId 用户ID
     * @param boardTime 上船时间
     * @param post 职务
     * @param groupInfo 组信息
     * @param menu 菜单
     * @param lambdaQueryWrapper 查询条件
     * @return 分页结果
     */
    IPage<StudyVO> findStudies(StudyPage<StudyVO> studyPage, Long categoryId, String userId, String boardTime, String post, String groupInfo, String menu, LambdaQueryWrapper<StudyVO> lambdaQueryWrapper);

    /**
     * APP端查询学习资料列表（增强版本 - 带权限过滤和排序）
     *
     * <p>
     * 功能增强：
     *  根据 iteams_display_order 表进行自定义排序
     *  基于会员等级进行权限过滤
     *  支持企业会员制和船员会员制两种模式（企业会员制不展示套餐权限不足、无法查看的学习资料）
     *  在SQL层面实现权限检查，提升查询效率
     * 将 findStudies 方法拆分为两个重载版本的原因：
     *   功能区分明确：基础版本用于简单查询，增强版本用于会员制场景
     *   参数一致性：增强版本的三个关键参数（departId、memberType、memberLevel）必须同时为有效值才能正确工作，分离接口可避免参数传递错误
     *   代码清晰：调用者可根据业务需求选择合适的方法<
     * </p>
     *
     * @param studyPage 分页参数
     * @param categoryId 分类ID
     * @param userId 用户ID（船员ID）
     * @param boardTime 上船时间
     * @param post 职务
     * @param groupInfo 组信息
     * @param menu 菜单
     * @param departId 部门ID（必须有效）
     * @param memberType 会员类型（必须有效：0=船员会员模式, 1=企业会员模式）
     * @param memberLevel 会员等级（必须有效：0=无有效套餐, 1=基础版, 2=标准版, 3=尊享版）
     * @param lambdaQueryWrapper 查询条件
     * @return 分页结果（包含权限过滤后的学习资料，按自定义顺序排列）
     */
    IPage<StudyVO> findStudies(StudyPage<StudyVO> studyPage, Long categoryId, String userId, String boardTime, String post, String groupInfo, String menu, String departId, Integer memberType, Short memberLevel, LambdaQueryWrapper<StudyVO> lambdaQueryWrapper);

    /**
     * 获取当前用户需要阅读学习资料的数量
     *
     * @param userId
     * @param boardTime
     * @param post
     * @param groupInfo
     * @param category
     * @return
     */
    Long getNeedReadNumber(String userId, String boardTime, String post, String groupInfo, Category category);

    /**
     * 获取当前用户需要关注学习资料的数量
     *
     * @param userId
     * @param boardTime
     * @param post
     * @param groupInfo
     * @param category
     * @return
     */
    Long getNeedAttentionNumber(String userId, String boardTime, String post, String groupInfo, Category category);

    List<Study> getStudyByName(String studyName, String menu);

    Long shareStudy(Long studyId, String route, boolean force);

    IPage<UserStudy> listUnfinishedStudyUserPage(Page<UserStudy> page, QueryWrapper<StudyVO> queryWrapper);

    // 判断ES中是否存在指定id对应的数据 存在返回true，不存在返回false
    Boolean existsById(String studyId);

    // 把单个学习资料存放到ES的索引库中
    void addStudyToIndex(Study study);

    /**
     * 把当前库中所有学习资料存放到ES的索引库中
     * @param force 当ES中数据存在时，是否强制更新 true：删除旧的，创建新的；false：不更新
     */
    void fixStudyIndex(Boolean force);

    // 删除磁盘中不存在的学习资料记录
    void deleteNotExistStudy();

    // 按学习资料内容Elasticsearch搜索
    List<String> fullTextSearch(String content);

    // 更新ES中的文档 updateContent表示是否修改文件文本内容
    void updateStudyToIndex(StudyVO study, Boolean updateContent);

    // 删除ES中指定的文档
    void deleteStudyToIndex(String studyId);

    // 获取搜索内容的分词结果
    List<String> getSearchTokens(String content);
}
