package org.jeecg.modules.training.controller.miniProgram;

import com.alibaba.fastjson.JSONObject;
import com.xkcoding.http.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.training.entity.MiniProgram;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.service.IMiniProgramService;
import org.jeecg.modules.training.service.TraineeService;
import org.jeecg.modules.training.util.HttpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


@RestController
@RequestMapping("/miniProgram/login")
@Slf4j
@Api(tags = "微信小程序登录测试")
public class MiniProgramLoginController {

    @Value("${justauth.type.WECHAT_OPEN.client-id}")
    private String appid;

    @Value("${justauth.type.WECHAT_OPEN.client-secret}")
    private String secret;

    @Autowired
    private TraineeService traineeService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private IMiniProgramService miniProgramService;

    @Autowired
    private ISysDepartService sysDepartService;


    /**
     * 处理微信小程序根据OpenId的登录请求。
     * <p>
     * 该接口主要负责校验OpenId的有效性，并根据OpenId完成用户的登录流程。
     * </p>
     *
     * @param user     包含用户OpenId信息的对象，请求体中传入。
     * @param response HTTP响应对象，用于在登录成功时设置相关cookie。
     * @return 返回登录结果，成功则返回登录后的用户信息，失败则返回错误信息。
     */
    @ApiOperation(value = "微信小程序根据OPenId登录", notes = "微信小程序根据OPenId登录")
    @PostMapping(value = "/loginByOpenId")
    public Result<?> loginByOpenId(@RequestBody SysUser user, HttpServletResponse response) {
        log.info("微信小程序根据OPenId登录start,openId:{}", user.getOpenId());
        // 校验OpenId是否为空
        if (StringUtil.isEmpty(user.getOpenId())) {
            log.info("openId为空");
            return Result.error("openId为空");
        }
        // 通过OpenId查询用户信息
        Trainee sysUser = traineeService.getUserByOpenId(user.getOpenId());

        // 用户不存在处理
        if (sysUser == null) {
            log.info("用户不存在");
            return Result.error("用户不存在");
        }

        // 对用户信息进行深度处理
        sysUser = getDepth(sysUser);
        // 完成用户信息补充，并设置相关响应信息
        return Result.OK(traineeService.appendInfo(sysUser, response));
    }

    /**
     * 微信小程序根据手机号登录
     *
     * @param user     包含登录所需信息的用户对象，如手机号和openId
     * @param response HTTP响应对象，用于在登录成功时进行相关操作
     * @return 返回登录结果，如果成功，包含登录后的用户信息；如果失败，返回错误信息。
     */
    @ApiOperation(value = "微信小程序根据手机号登录", notes = "微信小程序根据手机号登录")
    @PostMapping(value = "/loginByPhone")
    public Result<?> loginByPhone(@RequestBody SysUser user, HttpServletResponse response) {

        // 验证手机号是否为空
        if (StringUtil.isEmpty(user.getPhone())) {
            return Result.error("手机号为空");
        }
        // 通过手机号查询用户
        Trainee sysUser = traineeService.getUserByPhone(user.getPhone());

        // 用户不存在处理
        if (sysUser == null) {
            log.info("用户不存在");
            return Result.error("用户不存在");
        }
        // 更新用户OpenId
        sysUser.setOpenId(user.getOpenId());
        traineeService.updateById(sysUser);

        sysUser = getDepth(sysUser);
        // 登录成功，返回处理后的用户信息
        return Result.OK("登录成功", traineeService.appendInfo(sysUser, response));
    }

    /**
     * 获取用户所在组织的深度信息。
     *
     * @param sysUser 用户对象，包含用户ID。
     * @return 返回经过组织深度信息处理的用户对象。
     */
    public Trainee getDepth(Trainee sysUser) {

        // 根据用户ID查询用户所属的组织部门
        List<SysDepart> departs = sysDepartService.queryTraineeDeparts(sysUser.getId());
        // 如果查询结果不为空，设置用户的组织代码文本为第一个组织部门的名称
        if (!CollectionUtils.isEmpty(departs)) {
            sysUser.setOrgCodeTxt(departs.get(0).getDepartName());
        }
        return sysUser;
    }

    /**
     * 获取微信小程序openId
     * 该接口用于通过微信小程序的code换取用户的openId。
     *
     * @param jsonObject 包含code的JSON对象，其中code是由微信小程序提供用于换取openId的参数。
     * @return Result<?> 返回一个结果对象，如果成功则包含openId，失败则返回错误信息。
     */
    @ApiOperation(value = "获取微信小程序openId", notes = "获取微信小程序openId")
    @PostMapping(value = "/getOpenId")
    public Result<?> getOpenId(@RequestBody JSONObject jsonObject) {
        // 从请求体中获取code
        String code = jsonObject.getString("code");

        // 验证code是否为空
        if (StringUtil.isEmpty(code)) {
            return Result.error("登录失败");
        }

        // 构建请求微信小程序API的URL和参数
        String url = "https://api.weixin.qq.com/sns/jscode2session";
        String params = "appid=" + appid + "&secret=" + secret + "&js_code=" + code + "&grant_type=authorization_code";

        // 向微信服务器发送请求，获取响应
        String sr = HttpUtil.sendGet(url, params);
        JSONObject jsonObject1 = JSONObject.parseObject(sr);
        // 从响应中提取openId
        String string = jsonObject1.getString("openid");
        return Result.ok(string);
    }

    /**
     * 获取小程序分享参数
     *
     * @param id 小程序的唯一标识符
     * @return Result<?> 包含分享参数的结果对象。如果id为空，返回错误信息；否则，返回获取到的分享参数。
     */
    @ApiOperation(value = "获取小程序分享参数", notes = "获取小程序分享参数")
    @GetMapping("/getQRCodeScene")
    public Result<?> getQRCodeScene(@RequestParam(value = "id") String id) {
        log.info("获取小程序传参，id:{}", id);
        // 检查传入的id是否为空
        if (StringUtil.isEmpty(id)) {
            return Result.error("获取小程序分享参数失败");
        }
        // 通过id获取小程序实例
        MiniProgram miniProgram = miniProgramService.getById(id);
        JSONObject obj = new JSONObject();
        obj.put("miniProgram", miniProgram);


        return Result.OK("获取小程序分享参数成功", obj);

    }


}