package org.jeecg.modules.training.service;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.training.entity.StudyFeedback;
import org.jeecg.modules.training.vo.StudyFeedbackVO;

public interface IStudyFeedbackService extends IService<StudyFeedback> {
    // app添加一条反馈记录
    void submitStudyFeedback(StudyFeedback feedback);

    // 处理船员学习资料反馈
    void processStudyFeedback(StudyFeedback feedback);

    // 分页查询反馈记录
    IPage<StudyFeedbackVO> listPages(Page<StudyFeedbackVO> page, QueryWrapper<StudyFeedbackVO> queryWrapper);
}
