package org.jeecg.modules.training.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.training.entity.StudyTimeFleet;
import org.jeecg.modules.training.mapper.StudyTimeFleetMapper;
import org.jeecg.modules.training.service.StudyTimeFleetService;
import org.jeecg.modules.training.vo.StudyTimeFleetVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 船队每日学习时长表(StudyTimeFleet)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-22 10:47:41
 */
@Service("studyTimeFleetService")
public class StudyTimeFleetServiceImpl extends ServiceImpl<StudyTimeFleetMapper, StudyTimeFleet> implements StudyTimeFleetService {

    @Resource
    private StudyTimeFleetMapper studyTimeFleetMapper;

    @Override
    public Page<StudyTimeFleet> getPageList(Page<StudyTimeFleet> page,String depRoute, QueryWrapper<StudyTimeFleet> queryWrapper, String scope) {
        return studyTimeFleetMapper.getPageList(page,depRoute, queryWrapper, scope);
    }

    // 学习资料船队平均学习时长 统计表专用
    @Override
    public List<StudyTimeFleetVO> getAvgTime(String depRoute) {
        return studyTimeFleetMapper.getAvgTime(depRoute);
    }


}

