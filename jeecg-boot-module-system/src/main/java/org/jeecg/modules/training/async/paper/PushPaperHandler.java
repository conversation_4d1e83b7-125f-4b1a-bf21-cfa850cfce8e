package org.jeecg.modules.training.async.paper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.modules.training.entity.Paper;
import org.jeecg.modules.training.entity.UserPaper;
import org.jeecg.modules.training.service.IPaperService;
import org.jeecg.modules.training.service.IUserPaperService;
import org.jeecg.modules.training.service.TraineeService;
import org.jeecg.modules.training.util.DateTimeUtils;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-12-16 01:22
 **/
@Slf4j
//@Component
//@Scope("prototype")
public abstract class PushPaperHandler {
    protected static final long ONE_DAY_MS = 24 * 3600 * 1000L;
    protected static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    @Lazy
    protected ISysBaseAPI sysBaseAPI;

    @Autowired
    @Lazy
    protected IUserPaperService userPaperService;

    @Autowired
    @Lazy
    protected IPaperService paperService;

    @Autowired(required = false)
    @Lazy
    protected TraineeService traineeService;

    @Resource
    private ISyncStateService syncStateService;

    @Autowired
    private SaveUserPaperService saveUserPaperService;

    public abstract void run();

    public static void makeNewUserPaper(UserPaper userPaper, Paper paper, UserInfo userInfo, Date nowDate) {

        // 注意：paperId,userId,boardTime,depRoute 的值已经有了，查询返回的
        // userPaper.setDepRoute(paper.getDepRoute());

        userPaper.setTeamId(userInfo.getTeamId());
        userPaper.setPost(userInfo.getPost());
        userPaper.setType(0);
        userPaper.setStatus(0);
        userPaper.setDoneTimes(0);
        userPaper.setPassedTimes(0);
        userPaper.setFailedTimes(0);
        userPaper.setTotalDuration(0L);
        userPaper.setCreateTime(new Date());
        userPaper.setUpdateTime(new Date());
        userPaper.setDelFlag(0);

        //名称
        if (StringUtils.isNotEmpty(paper.getTimeFactor())) {
            //加上上船状态
            String[] pns = paper.getName().split("-");
            pns[0] += " (" + userInfo.getTimeFactor() + ")";
            userPaper.setPaperName(String.join("-", pns));
        } else {
            userPaper.setPaperName(paper.getName());
            userPaper.setBoardTime(null);
        }

        //总次数
        userPaper.setTotalTimes(paper.getNumExam());
        userPaper.setRetryTimes(paper.getNumRetries());

        //开始时间
        switch (paper.getStartFlag()) {
            case 0:
                break;
            case 1:
                userPaper.setStartTime(userInfo.getOnboardDate());
                break;
            case 2:
                userPaper.setStartTime(nowDate);
                break;
        }

        //过期和告警时间
        if (userPaper.getStartTime() != null) {
            if (paper.getExpiredDuration() != 0) {
                userPaper.setEndTime(DateTimeUtils.addDate(userPaper.getStartTime(), paper.getExpiredDuration().intValue()));
            } else {
                userPaper.setEndTime(null);
            }
            if (paper.getWarnDuration() != 0) {
                userPaper.setWarnTime(DateTimeUtils.addDate(userPaper.getStartTime(), paper.getWarnDuration().intValue()));
            } else {
                userPaper.setWarnTime(null);
            }
        } else {
            userPaper.setEndTime(null);
            userPaper.setWarnTime(null);
        }
    }

    protected void makeEditUserPaper(UserPaper userPaper, Paper paper, UserInfo userInfo, Date nowDate) {

        //paperId,userId,boardTime,depRoute 的值已经有了，查询返回的

        //这些不能修改，否则会导致考试结果和推送列表不同
        //userPaper.setOrgCode(userInfo.getOrgCode());
        //userPaper.setPost(userInfo.getPost());
        //userPaper.setCreateTime(new Date());
        userPaper.setTeamId(userInfo.getTeamId());

        userPaper.setUpdateTime(new Date());

        //总次数
        userPaper.setTotalTimes(paper.getNumExam());
        userPaper.setRetryTimes(paper.getNumRetries());

        //名称
        if (StringUtils.isNotEmpty(paper.getTimeFactor())) {
            //加上上船状态
            String[] pns = paper.getName().split("-");
            pns[0] += " (" + userInfo.getTimeFactor() + ")";
            userPaper.setPaperName(String.join("-", pns));
        } else {
            userPaper.setPaperName(paper.getName());
            userPaper.setBoardTime(null);
        }

        //开始时间
        switch (paper.getStartFlag()) {
            case 0:
                break;
            case 1:
                userPaper.setStartTime(userInfo.getOnboardDate());
                break;
            case 2:
                userPaper.setStartTime(nowDate);
                break;
        }

        //过期和告警时间
        if (userPaper.getStartTime() != null) {
            if (paper.getExpiredDuration() != 0) {
                userPaper.setEndTime(DateTimeUtils.addDate(userPaper.getStartTime(), paper.getExpiredDuration().intValue()));
            } else {
                userPaper.setEndTime(null);
            }
            if (paper.getWarnDuration() != 0) {
                userPaper.setWarnTime(DateTimeUtils.addDate(userPaper.getStartTime(), paper.getWarnDuration().intValue()));
            } else {
                userPaper.setWarnTime(null);
            }
        } else {
            userPaper.setEndTime(null);
            userPaper.setWarnTime(null);
        }

        //如果考试次数被改成无限次，并且是已考完，则改成可考试
        if (userPaper.getTotalTimes() == 0 && userPaper.getStatus() == 2) {
            userPaper.setStatus(0);
        }

        //如果考试次数被改成无需报警或者报警时间比当前时间大，并且是已告警，则改成可考试
        if ((userPaper.getWarnTime() == null || userPaper.getWarnTime().getTime() > System.currentTimeMillis())
                && userPaper.getStatus() == 1) {
            userPaper.setStatus(0);
        }

        //如果考试次数被改成无时限或者结束时间比当前时间大，并且是已过期，则改成可考试
        if ((userPaper.getEndTime() == null || userPaper.getEndTime().getTime() > System.currentTimeMillis())
                && userPaper.getStatus() == 3) {
            userPaper.setStatus(0);
        }

        syncStateService.resetSyncState(CommonConstant.USER_PAPER, userPaper.getId().toString());

    }

    protected void doFixPush(Date nowDate, List<UserPaper> needPushList, Map<Long, Paper> paperMap, Map<String, UserInfo> userMap) {
        List<UserPaper> userPaperList = new ArrayList<>();

        for (UserPaper userPaper : needPushList) {
            //paperId,userId,boardTime,depRoute 的值已经有了，查询返回的

            //试卷
            Paper paper = paperMap.get(userPaper.getPaperId());
            if (paper == null) {
                log.error("Not found paper " + userPaper.getPaperId() + " for user " + userPaper.getUserId() + " when push paper");
                continue;
            }

            //用户
            UserInfo userInfo = userMap.get(userPaper.getUserId());
            if (userInfo == null) {
                log.error("Not found user " + userPaper.getUserId() + " for paper " + userPaper.getPaperId() + " when push paper");
                continue;
            }

            //找出现在已经有的
            LambdaQueryWrapper<UserPaper> queryWrapper = new LambdaQueryWrapper<>();

            //不要过滤状态，只要有未删除的记录，就不需要再推了。
            queryWrapper.eq(UserPaper::getUserId, userPaper.getUserId())
                    .eq(UserPaper::getPaperId, userPaper.getPaperId())
                    .eq(UserPaper::getDelFlag, CommonConstant.DEL_FLAG_0);

            //新：这里不再判断自动过期配置，自动过期的规则已经变成使用 paper.keep_duration 来处理了。
//            if (paper.getAutoExpire() != null && paper.getAutoExpire() == 1) {
//                queryWrapper.eq(UserPaper::getOrgCode, userInfo.getOrgCode())
//                        .eq(UserPaper::getPost, userInfo.getPost());
//            }

            if (StringUtils.isNotEmpty(paper.getTimeFactor()))
                queryWrapper.eq(UserPaper::getBoardTime, userInfo.getBoardTime());
            else
                queryWrapper.isNull(UserPaper::getBoardTime);

            List<UserPaper> list = userPaperService.list(queryWrapper);

            if (list.isEmpty()) {
                //推送信息
                makeNewUserPaper(userPaper, paper, userInfo, nowDate);

                //添加到保存服务，进行统一保存处理，防止多线程下推送了重复的考试
                saveUserPaperService.addUserPaper(userPaper);
            } else {
                //更新系统推送
                for (UserPaper up : list) {
                    if (up.getType() == 0 && up.getStatus() < 2) {
                        makeEditUserPaper(up, paper, userInfo, nowDate);
                        userPaperList.add(up);
                    }
                }
            }

            //批量处理，避免内存溢出
            if (userPaperList.size() == 200) {
                userPaperService.updateBatchById(userPaperList);
                userPaperList.clear();
            }
        }

        if (!userPaperList.isEmpty()) {
            userPaperService.updateBatchById(userPaperList);
        }

        //执行保存
        saveUserPaperService.doSave();

    }

}
