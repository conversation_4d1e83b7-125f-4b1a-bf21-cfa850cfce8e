package org.jeecg.modules.training.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.training.entity.StudyLogDailyStatistics;
import org.jeecg.modules.training.mapper.StudyLogDailyStatisticsMapper;
import org.jeecg.modules.training.service.IStudyLogDailyStatisticsService;
import org.jeecg.modules.training.vo.StudyLogDailyStatisticsVO;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class StudyLogDailyStatisticsServiceImpl extends ServiceImpl<StudyLogDailyStatisticsMapper, StudyLogDailyStatistics> implements IStudyLogDailyStatisticsService {
    // 从study_log表获取指定阅读类型和日期的学习记录并返回其他类型
    @Override
    public List<StudyLogDailyStatistics> getTimeAndTypeList(Integer type, LocalDate startDate, LocalDate endDate) {
        return baseMapper.getTimeAndTypeList(type, startDate, endDate);
    }

    // 从study_log表获取至今为止指定阅读类型的学习记录并返回其他类型
    @Override
    public List<StudyLogDailyStatistics> getSoFarAndTypeList(Integer type) {
        return baseMapper.getSoFarAndTypeList(type);
    }

    // 获取近一个月学习资料被学习次数 统计表专用
    @Override
    public List<StudyLogDailyStatisticsVO> getList(String depRoute) {
        return baseMapper.getList(depRoute);
    }


}
