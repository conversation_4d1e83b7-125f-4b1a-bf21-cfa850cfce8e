package org.jeecg.modules.transform.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.transform.entity.TransformClient;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.transform.vo.TransformClientVo;

/**
 * <AUTHOR>
 * @description 针对表【transform_client(中转服务心跳数据)】的数据库操作Mapper
 * @createDate 2023-10-06 20:50:20
 * @Entity org.jeecg.modules.transform.entity.TransformClient
 */
public interface TransformClientMapper extends BaseMapper<TransformClient> {

    IPage<TransformClientVo> queryPageList(IPage<TransformClientVo> page, @Param("ew") QueryWrapper<TransformClientVo> queryWrapper);

}




