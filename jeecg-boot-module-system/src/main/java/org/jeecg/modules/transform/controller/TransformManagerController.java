package org.jeecg.modules.transform.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.fields.FieldFilter;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.training.entity.Team;
import org.jeecg.modules.training.service.TeamService;
import org.jeecg.modules.transform.entity.TransformClient;
import org.jeecg.modules.transform.entity.TransformHeart;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.jeecg.modules.transform.service.ITransformClientService;
import org.jeecg.modules.transform.service.ITransformHeartService;
import org.jeecg.modules.transform.vo.TransformClientVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 中转服务同步数据接口
 *
 * <AUTHOR>
 * @since 2023-06-25 16:09:57
 */
@RestController
@RequestMapping("/sys/transfer")
@Slf4j
public class TransformManagerController extends JeecgController<TransformClient, ITransformClientService> {

    @Value(value = "${jeecg.path.upload}")
    private String uploadPath;

    @Autowired
    private ISyncStateService syncStateService;

    @Autowired
    private ITransformHeartService transformHeartService;

    @Autowired
    private TeamService teamService;

    // 查询所有数据
    @GetMapping("/list")
    @FieldFilter({"id", "deviceId", "orgId", "teamName", "createTime", "version", "onlineTime", "heartStatus", "heart", "command"})
    public Result<?> queryPageList(TransformClientVo clientVo,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<TransformClientVo> queryWrapper = QueryGenerator.initQueryWrapper(clientVo, req.getParameterMap());
        IPage<TransformClientVo> page = new Page<>(pageNo, pageSize);
        service.queryPageList(page, queryWrapper);
        return Result.OK(page);
    }


    // 获取心跳日志
    @GetMapping("/getHeartLogList")
    public Result<?> getHeartLogList(@RequestParam(name = "teamId", defaultValue = "") String teamId,
                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                     HttpServletRequest req) {
        QueryWrapper<TransformHeart> queryWrapper = QueryGenerator.initQueryWrapper(new TransformHeart(), req.getParameterMap());
        queryWrapper.eq("org_id", teamId);
        IPage<TransformHeart> page = new Page<>(pageNo, pageSize);
        transformHeartService.page(page, queryWrapper);
        return Result.OK(page);
    }

    // 根据id获取数据
    @GetMapping("/queryById")
    public Result<?> queryById(@RequestParam(name = "id") Long id) {

        TransformClient transformClient = service.getById(id);

        Team team = teamService.getById(transformClient.getOrgId());
        if (team == null) {
            throw new JeecgBootException("该中转服务对应的船不存在");
        }
        TransformClientVo transformClientVo = new TransformClientVo();
        transformClientVo.setTeamName(team.getTeamName());

        BeanUtils.copyProperties(transformClient, transformClientVo);
        return Result.OK(transformClientVo);
    }

    /**
     * 批量删除中转服务
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "批量删除中转服务")
    @RequestMapping(value = "/deleteBatch", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> deleteBatch(@RequestParam("ids") String ids) {
        service.removeBatchByIds(Arrays.asList(ids.split(",")));
        return Result.OK("删除成功!");
    }

    /**
     * 设置中转服务ID
     *
     * @param id
     * @param ordId
     * @return
     */
    @AutoLog(value = "设置中转服务ID")
    @RequestMapping(value = "/setOrgId", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> setOrgId(@RequestParam("id") String id, @RequestParam("teamId") String ordId) {

        LambdaUpdateWrapper<TransformClient> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(TransformClient::getOrgId, ordId)
                .eq(TransformClient::getId, id);

        service.update(updateWrapper);
        return Result.OK("设置成功!");
    }


    /**
     * 设置下发命令
     *
     * @param id
     * @param command
     * @return
     */
    @AutoLog(value = "设置下发命令")
    @RequestMapping(value = "/setCommand", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> setCommand(@RequestParam("id") String id, @RequestParam("command") String command) {

        TransformClient transformClient = service.getById(id);

        //下发命令的时候发现有resync指令，则清空对应船舶的同步记录
        // 双向数据不能清理同步状态，会造成中转服务那边大量重复数据
        if (command.contains("resync")) {
            syncStateService.resyncState(transformClient.getOrgId());
        }

        LambdaUpdateWrapper<TransformClient> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(TransformClient::getCommand, command)
                .eq(TransformClient::getId, id);

        service.update(updateWrapper);
        return Result.OK("设置成功!等待下一次中转服务心跳时执行。");
    }


    /**
     * 重置同步状态
     *
     * @param ids
     * @param tbs
     * @return
     */
    @AutoLog(value = "重置同步状态")
    @RequestMapping(value = "/resetSyncState", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> resetSyncState(@RequestParam("ids") String ids, @RequestParam("tbs") String tbs) {

        List<TransformClient> clients = service.listByIds(Arrays.stream(ids.split(",")).collect(Collectors.toSet()));
        clients.forEach(client -> {
            Arrays.stream(tbs.split(",")).forEach(tableName -> syncStateService.resetSyncState(tableName, null, client.getOrgId()));
        });

        return Result.OK("重置成功!等待中转服务自动同步数据。");
    }


    /**
     * 获取日志列表
     *
     * @param id
     * @return
     */
    @AutoLog(value = "获取日志列表")
    @RequestMapping(value = "/getLogs", method = {RequestMethod.GET, RequestMethod.POST})
    public Result<?> getLogs(@RequestParam("id") Long id) {
        TransformClient client = service.getById(id);
        File dir = new File(uploadPath + File.separator + client.getOrgId());
        if (dir.exists()) {
            String[] files = dir.list();
            return Result.OK("", Arrays.stream(Objects.requireNonNull(files)).sorted(Comparator.reverseOrder()).collect(Collectors.toList()));
        }
        return Result.OK("", new ArrayList<String>());
    }

    /**
     * 获取日志内容
     *
     * @param id
     * @return
     */
    @AutoLog(value = "获取日志内容")
    @RequestMapping(value = "/getLogContent", method = {RequestMethod.GET, RequestMethod.POST})
    public Result<?> getLogContent(@RequestParam("id") Long id, @RequestParam("file") String file) throws IOException {
        TransformClient client = service.getById(id);
        String enc = "utf-8";
        if (client.getHeart() != null) {
            JSONObject heart = JSONObject.parseObject((String) client.getHeart());
            String os = heart.getString("os");
            if (os != null && os.toLowerCase().startsWith("win")) {
                enc = "gbk";
            }
        }
        return Result.OK("", FileUtils.readFileToString(new File(uploadPath + File.separator + client.getOrgId() + File.separator + file), enc));
    }

    /**
     * 删除日志
     *
     * @param id
     * @return
     */
    @AutoLog(value = "删除日志")
    @RequestMapping(value = "/delLog", method = {RequestMethod.GET, RequestMethod.POST})
    public Result<?> delLog(@RequestParam("id") Long id, @RequestParam("file") String file) throws IOException {
        TransformClient client = service.getById(id);
        new File(uploadPath + File.separator + client.getOrgId() + File.separator + file).delete();
        return Result.OK();
    }

}

