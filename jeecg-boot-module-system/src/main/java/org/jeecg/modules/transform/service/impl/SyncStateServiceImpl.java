package org.jeecg.modules.transform.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.system.entity.*;
import org.jeecg.modules.training.mapper.ExamQuestionMapper;
import org.jeecg.modules.training.mapper.MmpiMapper;
import org.jeecg.modules.transform.mapper.SyncStateMapper;
import org.jeecg.modules.transform.entity.SyncState;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.jeecg.modules.training.entity.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 同步标记表(SyncState)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-25 16:09:57
 */
@Slf4j
@Service("syncStateService")
public class SyncStateServiceImpl extends ServiceImpl<SyncStateMapper, SyncState> implements ISyncStateService {
    @Resource
    private SyncStateMapper syncStateMapper;
    @Resource
    private ExamQuestionMapper examQuestionMapper;
    private MmpiMapper mmpiMapper;

    /**
     * resync
     */
    public void resyncState(String teamId) {
        syncStateMapper.resetSyncState("trainee", null, teamId);
        syncStateMapper.resetSyncState("category", null, teamId);
        syncStateMapper.resetSyncState("knowledge", null, teamId);
        syncStateMapper.resetSyncState("rule", null, teamId);
        syncStateMapper.resetSyncState("questions", null, teamId);
        syncStateMapper.resetSyncState("paper", null, teamId);
        syncStateMapper.resetSyncState("paper_question", null, teamId);
        syncStateMapper.resetSyncState("user_paper", null, teamId);
        syncStateMapper.resetSyncState("phase", null, teamId);
        syncStateMapper.resetSyncState("phase_item", null, teamId);
        syncStateMapper.resetSyncState("user_phase", null, teamId);
        syncStateMapper.resetSyncState("user_phase_item", null, teamId);
        syncStateMapper.resetSyncState("sys_user", null, teamId);
        syncStateMapper.resetSyncState("sys_role", null, teamId);
        syncStateMapper.resetSyncState("sys_user_role", null, teamId);
        syncStateMapper.resetSyncState("sys_user_depart", null, teamId);
        syncStateMapper.resetSyncState("sys_trainee_depart", null, teamId);
        syncStateMapper.resetSyncState("sys_depart", null, teamId);
        syncStateMapper.resetSyncState("sys_position", null, teamId);
    }

    /**
     * 数据发生修改之后，需要将同步的数据改为未同步，也就是将同步状态改为0
     */
    public void resetSyncState(String tableName) {
        syncStateMapper.resetSyncState(tableName, null, null);
    }

    /**
     * 数据发生修改之后，需要将同步的数据改为未同步，也就是将同步状态改为0
     */
    public void resetSyncState(String tableName, String dataIds) {
        if (dataIds != null && !dataIds.isEmpty() && !dataIds.contains(",") && !dataIds.startsWith("'")) {
            dataIds = "'" + dataIds + "'";
        }
        syncStateMapper.resetSyncState(tableName, dataIds, null);
    }

    public void resetSyncState(String tableName, String dataIds, String teamId) {
        if (dataIds != null && !dataIds.isEmpty() && !dataIds.contains(",") && !dataIds.startsWith("'")) {
            dataIds = "'" + dataIds + "'";
        }
        syncStateMapper.resetSyncState(tableName, dataIds, teamId);
    }

    @Override
    public List<String> getSyncStateIds(String tableName, String idStr) {
        return syncStateMapper.getSyncStateIds(tableName, idStr);
    }

    @Override
    public Integer setSyncState(Integer op, String tableName, String teamId, String id) {
        if (op == 1)
            return syncStateMapper.addSyncState(tableName, teamId, id);
        else
            return syncStateMapper.setSyncState(tableName, teamId, id);
    }

    @Override
    public List<Team> syncTeam(String teamId) {
        //数据量小，无需分页
        return syncStateMapper.syncTeam(teamId);
    }

    @Override
    public List<Trainee> syncTrainee(String teamId) {
        //数据量小，无需分页
        String depRoute = getDepRouteByOrgId(teamId);
        return baseMapper.syncTrainee(teamId, depRoute);
    }

    @Override
    public List<Category> syncCategory(String teamId) {
        //数据量小，无需分页
        String depRoute = getDepRouteByOrgId(teamId);
        return baseMapper.syncCategory(teamId, depRoute);
    }

    @Override
    public List<Knowledge> syncKnowledge(String teamId) {
        //数据量小，无需分页
        String depRoute = getDepRouteByOrgId(teamId);
        return baseMapper.syncKnowledge(teamId, depRoute);
    }

    @Override
    public List<Rule> syncRule(String teamId) {
        //数据量小，无需分页
        String depRoute = getDepRouteByOrgId(teamId);
        return baseMapper.syncRule(teamId, depRoute);
    }

    @Override
    public List<Packages> syncPackages(String teamId) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncPackages(teamId, depRoute);
    }

    @Override
    public List<Questions> syncQuestions(String teamId, Long startIndex, Integer pageSize) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncQuestions(teamId, depRoute, startIndex, pageSize);
    }

    @Override
    public List<Paper> syncPaper(String teamId, Integer pageSize) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncPaper(teamId, depRoute, pageSize);
    }

    @Override
    public List<PaperQuestion> syncPaperQuestion(String teamId, Integer pageSize) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncPaperQuestion(teamId, depRoute, pageSize);
    }

    @Override
    public List<UserPaper> syncUserPaper(String teamId, Integer pageSize) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncUserPaper(teamId, depRoute, pageSize);
    }

    @Override
    public List<Exam> syncExam(String teamId, Integer pageSize) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncExam(teamId, depRoute, pageSize);
    }

    @Override
    public List<ExamQuestion> syncExamQuestion(String teamId, Long examId) {
        return syncStateMapper.syncExamQuestion(teamId, examId);
    }

    @Override
    public List<Mmpi> syncMmpi(String teamId, Long examId) {
        return syncStateMapper.syncMmpi(teamId, examId);
    }

    @Override
    public List<Study> syncStudy(String teamId, Long maxSize, Long startIndex, Integer pageSize) {
        String depRoute = getDepRouteByOrgId(teamId);
        log.info("syncStudy: teamId={}, maxSize={}, startIndex={}, pageSize={}", teamId, maxSize, startIndex, pageSize);
        return syncStateMapper.syncStudy(teamId, depRoute, maxSize, startIndex, pageSize);
    }

    @Override
    public List<StudyLog> syncStudyLog(String teamId, Integer pageSize) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncStudyLog(teamId, depRoute, pageSize);
    }

    @Override
    public List<Phase> syncPhase(String teamId, Integer pageSize) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncPhase(teamId, depRoute, pageSize);
    }

    @Override
    public List<PhaseItem> syncPhaseItem(String teamId, Integer pageSize) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncPhaseItem(teamId, depRoute, pageSize);
    }

    @Override
    public List<UserPhase> syncUserPhase(String teamId, Integer pageSize) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncUserPhase(teamId, depRoute, pageSize);
    }

    @Override
    public List<UserPhaseItem> syncUserPhaseItem(String teamId, Integer pageSize) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncUserPhaseItem(teamId, depRoute, pageSize);
    }

    @Override
    public List<SysDepart> syncSysDepart(String teamId) {
        return syncStateMapper.syncSysDepart(teamId);
    }

    @Override
    public List<SysUser> syncSysUser(String teamId) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncSysUser(teamId, depRoute);
    }

    @Override
    public List<SysRole> syncSysRole(String teamId) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncSysRole(teamId, depRoute);
    }

    @Override
    public List<SysUserRole> syncSysUserRole(String teamId) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncSysUserRole(teamId, depRoute);
    }

    @Override
    public List<SysUserDepart> syncSysUserDepart(String teamId) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncSysUserDepart(teamId, depRoute);
    }

    @Override
    public List<SysTraineeDepart> syncSysTraineeDepart(String teamId) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncSysTraineeDepart(teamId, depRoute);
    }

    @Override
    public List<SysPosition> syncSysPosition(String teamId) {
        String depRoute = getDepRouteByOrgId(teamId);
        return syncStateMapper.syncSysPosition(teamId, depRoute);
    }

    private String getDepRouteByOrgId(String teamId) {

        List<String> depRouteByOrgIds = baseMapper.getDepRouteByOrgId(teamId);
        String depRoute = null;
        if (!depRouteByOrgIds.isEmpty()) {
            depRoute = depRouteByOrgIds.get(0);
            if (depRoute != null && depRoute.split("/").length >= 4) {
                depRoute = depRoute.substring(0, depRoute.lastIndexOf("/"));
            }
        }
        return depRoute;
    }

}

