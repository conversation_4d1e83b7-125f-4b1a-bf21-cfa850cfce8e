<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.transform.mapper.TransformClientMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.transform.vo.TransformClientVo">
        select *
        from (select tc.*, sd.team_name
              from transform_client tc
                       left join team sd on tc.org_id = sd.id) transform_client
            ${ew.customSqlSegment}
    </select>
</mapper>
