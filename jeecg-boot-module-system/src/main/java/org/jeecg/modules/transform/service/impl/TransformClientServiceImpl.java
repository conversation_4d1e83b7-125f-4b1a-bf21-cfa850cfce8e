package org.jeecg.modules.transform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.transform.entity.TransformClient;
import org.jeecg.modules.transform.service.ITransformClientService;
import org.jeecg.modules.transform.mapper.TransformClientMapper;
import org.jeecg.modules.transform.vo.TransformClientVo;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【transform_client(中转服务心跳数据)】的数据库操作Service实现
 * @createDate 2023-10-06 20:50:20
 */
@Service
public class TransformClientServiceImpl extends ServiceImpl<TransformClientMapper, TransformClient> implements ITransformClientService {
    @Override
    public IPage<TransformClientVo> queryPageList(IPage<TransformClientVo> page, QueryWrapper<TransformClientVo> queryWrapper) {
        return baseMapper.queryPageList(page, queryWrapper);
    }
}




