package org.jeecg.modules.transform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.system.entity.*;
import org.jeecg.modules.training.entity.*;
import org.jeecg.modules.transform.entity.SyncState;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 同步标记表(SyncState)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-25 16:09:57
 */
public interface ISyncStateService extends IService<SyncState> {

    @Transactional
    void resetSyncState(String tableName);

    @Transactional
    void resetSyncState(String tableName, String dataIds);

    @Transactional
    void resetSyncState(String tableName, String dataIds, String teamId);

    List<String> getSyncStateIds(String tableName, String idStr);

    @Transactional
    Integer setSyncState(Integer op, String tableName, String teamId, String id);

    List<Category> syncCategory(String teamId);

    List<Knowledge> syncKnowledge(String teamId);

    List<Rule> syncRule(String teamId);

    List<Questions> syncQuestions(String teamId, Long startIndex, Integer pageSize);

    List<Paper> syncPaper(String teamId, Integer pageSize);

    List<PaperQuestion> syncPaperQuestion(String teamId, Integer pageSize);

    List<UserPaper> syncUserPaper(String teamId, Integer pageSize);

    List<Exam> syncExam(String teamId, Integer pageSize);

    List<ExamQuestion> syncExamQuestion(String teamId, Long examId);

    List<Mmpi> syncMmpi(String teamId, Long examId);

    List<Study> syncStudy(String teamId, Long maxSize, Long startIndex, Integer pageSize);

    List<StudyLog> syncStudyLog(String teamId, Integer pageSize);


    List<SysDepart> syncSysDepart(String teamId);

    List<Team> syncTeam(String teamId);

    List<SysUser> syncSysUser(String teamId);

    List<Trainee> syncTrainee(String teamId);

    List<SysRole> syncSysRole(String teamId);

    List<SysUserRole> syncSysUserRole(String teamId);

    List<SysUserDepart> syncSysUserDepart(String teamId);

    List<SysTraineeDepart> syncSysTraineeDepart(String teamId);

    List<SysPosition> syncSysPosition(String teamId);

    List<Phase> syncPhase(String teamId, Integer pageSize);

    List<PhaseItem> syncPhaseItem(String teamId, Integer pageSize);

    List<UserPhase> syncUserPhase(String teamId, Integer pageSize);

    List<UserPhaseItem> syncUserPhaseItem(String teamId, Integer pageSize);

    void resyncState(String orgId);

    // 同步套餐表
    List<Packages> syncPackages(String teamId);
}

