package org.jeecg.modules.transform.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.transform.entity.TransformHeart;
import org.jeecg.modules.transform.service.ITransformHeartService;
import org.jeecg.modules.transform.mapper.TransformHeartMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【transform_heart(中转服务心跳数据)】的数据库操作Service实现
* @createDate 2023-09-21 11:11:22
*/
@Service
public class TransformHeartServiceImpl extends ServiceImpl<TransformHeartMapper, TransformHeart>
    implements ITransformHeartService {

}




