package org.jeecg.modules.system.configuration;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "wechatpay")
// 微信支付相关配置
public class WechatPayProperties {
    // appID
    private String appid;
    // 商户号
    private String mchid;
    // APIV3密钥
    private String apiV3Key;
    // 微信支付公钥的存放路径
    private String publicKeyPath;
    // 支付成功回调地址
    private String notifyUrl;
    // 商户API私钥的存放路径
    private String privateKeyPath;
    // 商户API证书序列号
    private String merchantSerialNumber;
    // 微信支付公钥id
    private String publicKeyId;
    // 支付成功返回地址
    private String redirectUrl;

}
