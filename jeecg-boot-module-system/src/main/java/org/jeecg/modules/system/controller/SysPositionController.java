package org.jeecg.modules.system.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.fields.FieldFilter;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.ImportExcelUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.system.entity.SysPosition;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.system.service.ISysPositionService;
import org.jeecg.modules.training.async.share.PushShareService;
import org.jeecg.modules.training.service.IIndustryService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Description: 职务表
 * @Author: jeecg-boot
 * @Date: 2019-09-19
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "职务表")
@RestController
@RequestMapping("/sys/position")
public class SysPositionController {

    @Autowired
    private ISysPositionService sysPositionService;

    @Autowired
    private ISysDepartService sysDepartService;

    @Autowired
    private IIndustryService industryService;

    @Autowired
    private PushShareService pushShareService;

    /**
     * 分页列表查询
     *
     * @param sysPosition
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @FieldFilter({"code", "createBy", "createTime", "delFlag", "depRoute", "id", "industryIds",
            "industryNames", "name", "postRank", "shareMode", "sysOrgCode", "postRank_dictText"})
    @AutoLog(value = "职务表-分页列表查询")
    @ApiOperation(value = "职务表-分页列表查询", notes = "职务表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<SysPosition>> queryPageList(SysPosition sysPosition,
                                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                    HttpServletRequest req) {
        Result<IPage<SysPosition>> result = new Result<>();
        QueryWrapper<SysPosition> queryWrapper = QueryGenerator.initQueryWrapper(sysPosition, req.getParameterMap());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.and(q -> q.lambda().eq(SysPosition::getDepRoute, loginUser.getDepRoute()));

        Page<SysPosition> page = new Page<>(pageNo, pageSize);
        IPage<SysPosition> pageList = sysPositionService.page(page, queryWrapper);
        // 设置返回的行业名称
        pageList.getRecords().forEach(item -> {
            item.setIndustryNames(industryService.getNamesByIndustryIds(item.getIndustryIds()));
        });

        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 不分页列表查询
     *
     * @param sysPosition
     * @param req
     * @return
     */
    @AutoLog(value = "职务表-不分页列表查询")
    @ApiOperation(value = "职务表-不分页列表查询", notes = "职务表-分页列表查询")
    @GetMapping(value = "/getList")
    public Result<List<SysPosition>> queryList(SysPosition sysPosition,
                                               HttpServletRequest req) {
        Result<List<SysPosition>> result = new Result<>();
        QueryWrapper<SysPosition> queryWrapper = QueryGenerator.initQueryWrapper(sysPosition, req.getParameterMap());
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.and(q -> q.lambda().eq(SysPosition::getDepRoute, loginUser.getDepRoute()));

        List<SysPosition> list = sysPositionService.list(queryWrapper);
        // 设置返回的行业名称
        list.forEach(item -> {
            item.setIndustryNames(industryService.getNamesByIndustryIds(item.getIndustryIds()));
        });

        result.setSuccess(true);
        result.setResult(list);
        return result;
    }

    /**
     * 添加
     *
     * @param sysPosition
     * @return
     */
    @AutoLog(value = "职务表-添加")
    @ApiOperation(value = "职务表-添加", notes = "职务表-添加")
    @PostMapping(value = "/add")
    public Result<SysPosition> add(@RequestBody SysPosition sysPosition) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        Result<SysPosition> result = new Result<>();
        try {

            sysPosition.setIndustryIds(industryService.getIndustryIdsByNames(sysPosition.getIndustryNames()));
            sysPosition.setDepRoute(loginUser.getDepRoute());

            sysPositionService.save(sysPosition);

            //复制数据
            if ("admin".equals(loginUser.getUsername()) && StringUtils.isNotBlank(sysPosition.getIndustryIds()) && "1".equals(sysPosition.getShareMode())) {

                pushShareService.pushData("sys_position", sysPosition.getId());

            }

            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 编辑
     *
     * @param sysPosition
     * @return
     */
    @AutoLog(value = "职务表-编辑")
    @ApiOperation(value = "职务表-编辑", notes = "职务表-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<SysPosition> edit(@RequestBody SysPosition sysPosition) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        Result<SysPosition> result = new Result<>();
        SysPosition sysPositionEntity = sysPositionService.getById(sysPosition.getId());
        if (sysPositionEntity == null) {
            result.error500("未找到对应实体");
        } else {

            // 修改行业
            if (sysPosition.getIndustryNames() != null && !sysPosition.getIndustryNames().trim().isEmpty()) {
                sysPosition.setIndustryIds(industryService.getIndustryIdsByNames(sysPosition.getIndustryNames()));
            }

            boolean ok = sysPositionService.updateById(sysPosition);

            //复制数据
            if ("admin".equals(loginUser.getUsername()) && "1".equals(sysPosition.getShareMode())) {

                pushShareService.pushData("sys_position", sysPosition.getId());

            }
            //TODO 返回false说明什么？
            if (ok) {
                result.success("修改成功!");
            }
        }

        return result;
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "职务表-通过id删除")
    @ApiOperation(value = "职务表-通过id删除", notes = "职务表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        try {
            sysPositionService.removeById(id);
        } catch (Exception e) {
            log.error("删除失败{}", e.getMessage());
            return Result.error("删除失败!");
        }
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "职务表-批量删除")
    @ApiOperation(value = "职务表-批量删除", notes = "职务表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<SysPosition> deleteBatch(@RequestParam(name = "ids") String ids) {
        Result<SysPosition> result = new Result<>();
        if (ids == null || ids.trim().isEmpty()) {
            result.error500("参数不识别！");
        } else {
            this.sysPositionService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @FieldFilter({"code", "createBy", "createTime", "delFlag", "depRoute", "id", "industryIds",
            "industryNames", "name", "postRank", "sysOrgCode", "shareMode"})
    @AutoLog(value = "职务表-通过id查询")
    @ApiOperation(value = "职务表-通过id查询", notes = "职务表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<SysPosition> queryById(@RequestParam(name = "id") String id) {
        Result<SysPosition> result = new Result<>();
        SysPosition sysPosition = sysPositionService.getById(id);
        if (sysPosition == null) {
            result.error500("未找到对应实体");
        } else {
            sysPosition.setIndustryNames(industryService.getNamesByIndustryIds(sysPosition.getIndustryIds()));
            result.setResult(sysPosition);
            result.setSuccess(true);
        }
        return result;
    }

    /**
     * 导出excel
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request) {
        // Step.1 组装查询条件
        QueryWrapper<SysPosition> queryWrapper = QueryGenerator.initQueryWrapper(new SysPosition(), request.getParameterMap());
        try {
            String paramsStr = request.getParameter("paramsStr");
            if (oConvertUtils.isNotEmpty(paramsStr)) {
                String deString = URLDecoder.decode(paramsStr, "UTF-8");
                SysPosition sysPosition = JSON.parseObject(deString, SysPosition.class);
                queryWrapper = QueryGenerator.initQueryWrapper(sysPosition, request.getParameterMap());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.and(q -> q.lambda().likeRight(SysPosition::getDepRoute, loginUser.getDepRoute()));

        //Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<SysPosition> pageList = sysPositionService.list(queryWrapper);

        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "职务表列表");
        mv.addObject(NormalExcelConstants.CLASS, SysPosition.class);

        ExportParams exportParams = new ExportParams("职务表列表数据", "导出人:" + loginUser.getRealname(), "导出信息");
        exportParams.setType(ExcelType.XSSF);

        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) throws IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        // 错误信息
        List<String> errorMessage = new ArrayList<>();
        int successLines = 0, errorLines = 0;
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<Object> listSysPositions = ExcelImportUtil.importExcel(file.getInputStream(), SysPosition.class, params);
                List<String> list = ImportExcelUtil.importDateSave(listSysPositions, ISysPositionService.class, errorMessage, CommonConstant.SQL_INDEX_UNIQ_CODE);
                errorLines += list.size();
                successLines += (listSysPositions.size() - errorLines);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return ImportExcelUtil.importReturnRes(errorLines, successLines, errorMessage, null);
    }

    /**
     * 通过code查询
     *
     * @param code
     * @return
     */
    @AutoLog(value = "职务表-通过code查询")
    @ApiOperation(value = "职务表-通过code查询", notes = "职务表-通过code查询")
    @GetMapping(value = "/queryByCode")
    public Result<SysPosition> queryByCode(@RequestParam(name = "code") String code) {
        Result<SysPosition> result = new Result<SysPosition>();
        QueryWrapper<SysPosition> queryWrapper = new QueryWrapper<SysPosition>();
        queryWrapper.eq("code", code);
        SysPosition sysPosition = sysPositionService.getOne(queryWrapper);
        if (sysPosition == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(sysPosition);
            result.setSuccess(true);
        }
        return result;
    }

    /**
     * 查询当前登录用户可获取的分享的试卷列表 shareModel = 2
     */
    @FieldFilter({"code", "createBy", "createTime", "delFlag", "depRoute", "id", "industryIds",
            "name", "positionRank", "shareMode", "sysOrgCode", "postRank", "postRank_dictText"})
    @GetMapping("/getShareList")
    public Result<?> getSharePaperList(SysPosition sysPosition,
                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                       HttpServletRequest req) {
        QueryWrapper<SysPosition> queryWrapper = QueryGenerator.initQueryWrapper(sysPosition, req.getParameterMap());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        queryWrapper.in("share_mode", 1, 2);

        // 获取该部门所属行业的id集合
        String industryIds = sysDepartService.getDepartByOrgCode(loginUser.getOrgCode()).getIndustryIds();
        if (!industryIds.isEmpty()) {
            // 格式化行业id字符串
            industryIds = industryService.formatIndustryIdsExp(industryIds);
            queryWrapper.apply("industry_ids regexp {0}", industryIds);
        }

        Page<SysPosition> page = new Page<>(pageNo, pageSize);
        sysPositionService.page(page, queryWrapper);

        return Result.ok(page);
    }


    /**
     * 设置用户选择的公共资料
     */
    @GetMapping("/setShareSysPosition")
    public Result<?> setShareSysPosition(@RequestParam List<Long> ids) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 共享数据
        ids.forEach(item -> {
            sysPositionService.shareSysPosition(String.valueOf(item), loginUser.getDepRoute(), true);
        });

        return Result.ok("获取成功");
    }

    // 只获取职务的名称和编码
    @FieldFilter({"code", "id", "name", "postRank", "postRank_dictText"})
    @GetMapping("/getNameAndCode")
    public Result<?> getNameAndCode() {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            return Result.error("用户token过期，请重新登陆！");
        }

        Page<SysPosition> page = sysPositionService.lambdaQuery()
                .select(SysPosition::getId, SysPosition::getPostRank,
                        SysPosition::getCode, SysPosition::getName)
                .eq(SysPosition::getDepRoute, loginUser.getDepRoute())
                .page(new Page<>(1, 1000));

        return Result.OK(page);
    }
}
