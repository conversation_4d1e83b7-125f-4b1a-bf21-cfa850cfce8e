package org.jeecg.modules.system.configuration;

import cloud.tianai.captcha.common.constant.CaptchaTypeConstant;
import cloud.tianai.captcha.common.constant.CommonConstant;
import cloud.tianai.captcha.resource.ResourceStore;
import cloud.tianai.captcha.resource.common.model.dto.Resource;
import cloud.tianai.captcha.resource.common.model.dto.ResourceMap;
import cloud.tianai.mate.captcha.common.constant.MateCaptchaTypeConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import static cloud.tianai.captcha.common.constant.CommonConstant.DEFAULT_TAG;
import static cloud.tianai.mate.captcha.generator.impl.StandardWordOrderClickImageCaptchaGenerator.wordToResourceMap;

/**
 * @Author: 天爱有情
 * @date 2022/7/11 14:22
 * @Description 负责模板和背景图存储的地方
 */
@Component
@RequiredArgsConstructor
public class ResourceConfiguration implements InitializingBean {

    // 该ResourceStore 由 tianai-captcha-springboot-starter 自动装配
    private final ResourceStore resourceStore;


    @Override
    public void afterPropertiesSet() throws Exception {

        // todo 如果 ResourceStore 是RedisResourceStore的时候， 下面的方法只初始化一次即可，请勿重复将数据添加到redis缓存中


        // 滑块验证码 模板
        ResourceMap template1 = new ResourceMap(4);
        template1.put("active.png", new Resource("classpath", "template/1/active.png"));
        template1.put("fixed.png", new Resource("classpath", "template/1/fixed.png"));
        ResourceMap template2 = new ResourceMap(4);
        template2.put("active.png", new Resource("classpath", "template/2/active.png"));
        template2.put("fixed.png", new Resource("classpath", "template/2/fixed.png"));
        // 旋转验证码 模板
        ResourceMap template3 = new ResourceMap(4);
        template3.put("active.png", new Resource("classpath", "template/3/active.png"));
        template3.put("fixed.png", new Resource("classpath","template/3/fixed.png"));
        // 旋转验证码 带有混淆功能的混淆模板
        ResourceMap template4 = new ResourceMap( "obfuscate", 4);
        template4.put("active.png", new Resource("classpath", "template/4/active.png"));
        template4.put("fixed.png", new Resource("classpath", "template/4/fixed.png"));
        template4.put("mask.png", new Resource("classpath","template/4/mask.png"));
        resourceStore.addTemplate(CaptchaTypeConstant.ROTATE, template4);

        // 1. 添加一些模板
        resourceStore.addTemplate(CaptchaTypeConstant.SLIDER, template1);
        resourceStore.addTemplate(CaptchaTypeConstant.SLIDER, template2);
        resourceStore.addTemplate(CaptchaTypeConstant.ROTATE, template3);

        // (语序点选成语字典，这里只演示一个)
        resourceStore.addTemplate(MateCaptchaTypeConstant.WORD_ORDER_IMAGE_CLICK, wordToResourceMap("鸡你太美", "default"));

        // 添加icon(图标点选)
        resourceStore.addResource(CommonConstant.IMAGE_CLICK_ICON, new Resource("classpath", "icon/1.png", DEFAULT_TAG));
        resourceStore.addResource(CommonConstant.IMAGE_CLICK_ICON, new Resource("classpath", "icon/2.png", DEFAULT_TAG));
        resourceStore.addResource(CommonConstant.IMAGE_CLICK_ICON, new Resource("classpath", "icon/3.png", DEFAULT_TAG));
        resourceStore.addResource(CommonConstant.IMAGE_CLICK_ICON, new Resource("classpath", "icon/4.png", DEFAULT_TAG));
        resourceStore.addResource(CommonConstant.IMAGE_CLICK_ICON, new Resource("classpath", "icon/5.png", DEFAULT_TAG));
        resourceStore.addResource(CommonConstant.IMAGE_CLICK_ICON, new Resource("classpath", "icon/6.png", DEFAULT_TAG));
        resourceStore.addResource(CommonConstant.IMAGE_CLICK_ICON, new Resource("classpath", "icon/7.png", DEFAULT_TAG));
        resourceStore.addResource(CommonConstant.IMAGE_CLICK_ICON, new Resource("classpath", "icon/8.png", DEFAULT_TAG));

        // 点选icon(蜂窝图标)
        resourceStore.addResource(CommonConstant.HONEYCOMB_CLICK_ICON, new Resource("classpath", "honeycomb/mask.png", DEFAULT_TAG));
        resourceStore.addResource(CommonConstant.HONEYCOMB_CLICK_ICON, new Resource("classpath", "honeycomb/mask2.png", DEFAULT_TAG));
        resourceStore.addResource(CommonConstant.HONEYCOMB_CLICK_ICON, new Resource("classpath", "honeycomb/mask3.png", DEFAULT_TAG));
        resourceStore.addResource(CommonConstant.HONEYCOMB_CLICK_ICON, new Resource("classpath", "honeycomb/mask4.png", DEFAULT_TAG));
        resourceStore.addResource(CommonConstant.HONEYCOMB_CLICK_ICON, new Resource("classpath", "honeycomb/mask5.png", DEFAULT_TAG));
        resourceStore.addResource(CommonConstant.HONEYCOMB_CLICK_ICON, new Resource("classpath", "honeycomb/mask6.png", DEFAULT_TAG));
        resourceStore.addResource(CommonConstant.HONEYCOMB_CLICK_ICON, new Resource("classpath", "honeycomb/mask7.png", DEFAULT_TAG));

        // 2. 添加自定义背景图片
        resourceStore.addResource(CaptchaTypeConstant.SLIDER, new Resource("classpath", "bgimages/a.jpg"));
        resourceStore.addResource(CaptchaTypeConstant.ROTATE, new Resource("classpath", "bgimages/48.jpg"));
        resourceStore.addResource(CaptchaTypeConstant.CONCAT, new Resource("classpath", "bgimages/a.jpg"));
        resourceStore.addResource(MateCaptchaTypeConstant.ROTATE_DEGREE, new Resource("classpath", "bgimages/135.png"));
        resourceStore.addResource(MateCaptchaTypeConstant.ROTATE_DEGREE, new Resource("classpath", "bgimages/139.png"));
        resourceStore.addResource(MateCaptchaTypeConstant.ROTATE_DEGREE, new Resource("classpath", "bgimages/141.png"));

        resourceStore.addResource(CaptchaTypeConstant.WORD_IMAGE_CLICK, new Resource("classpath", "bgimages/a.jpg"));
        resourceStore.addResource(MateCaptchaTypeConstant.IMAGE_CLICK, new Resource("classpath", "bgimages/a.jpg"));
        resourceStore.addResource(MateCaptchaTypeConstant.HONEYCOMB_IMAGE_CLICK, new Resource("classpath", "bgimages/a.jpg"));
        resourceStore.addResource(MateCaptchaTypeConstant.WORD_ORDER_IMAGE_CLICK, new Resource("classpath", "bgimages/a.jpg"));
    }
}
