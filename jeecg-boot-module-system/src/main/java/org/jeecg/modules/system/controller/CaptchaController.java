package org.jeecg.modules.system.controller;

import cloud.tianai.captcha.common.constant.CaptchaTypeConstant;
import cloud.tianai.captcha.common.response.ApiResponse;
import cloud.tianai.captcha.common.response.ApiResponseStatusConstant;
import cloud.tianai.captcha.common.util.CaptchaTypeClassifier;
import cloud.tianai.captcha.generator.common.model.dto.GenerateParam;
import cloud.tianai.captcha.spring.application.ImageCaptchaApplication;
import cloud.tianai.captcha.spring.plugins.secondary.SecondaryVerificationApplication;
import cloud.tianai.captcha.spring.vo.CaptchaResponse;
import cloud.tianai.captcha.spring.vo.ImageCaptchaVO;
import cloud.tianai.captcha.validator.common.model.dto.ImageCaptchaTrack;
import cloud.tianai.mate.captcha.generator.impl.processor.ShuffleImageProcessor;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.util.encoders.Hex;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.system.util.AesUtils;
import org.jeecg.modules.system.util.RsaUtils;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.service.TraineeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.security.interfaces.RSAPrivateKey;
import java.util.Base64;
import java.util.Collections;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
@Api(tags = "验证码接口")
@RestController
@RequestMapping("/sys/captcha")
public class CaptchaController {
    @Resource
    private ImageCaptchaApplication captchaApplication;

    @Value("${SecretId}")
    private String accessKeyID;

    @Value("${SecretKey}")
    private String accessKeySecret;

    @Resource
    private RedisUtil redisUtil;

    @Autowired
    private TraineeService traineeService;


    // rsa私钥
    public static final String PRIVATE_KEY =
            "MIICWwIBAAKBgQC0nf3XQxOCL59bUEno36+d0PoGu82WDaeN6vYzlO5p5JOttGIuw0BLjXjJhpKrwU41tmYVygr+t3+y2GAgavEy2R+RSWCYgLEwpi8xjeV5ZfAWORYILF2YuZsG+276HapgQYxajzUlnHrO+En6LJ9sUglg9VTQNnawwKD6/cy8IQIDAQABAoGAWaBg21X8s0iAcmaYFogRdGXD/F1rOj/SWnky7QvFdySnJ7ZwVjYrjJJKUu6Fj9dfwQWfCMb2x8k1BAGdSdjnkPeCXj1+AvvMRzgTJTgRtl355N/AUsyekHnsYysYiDEIfahRP1BztpYR2K+Z+JGdQy+40TNfhNnkRT+FN71WZ7ECQQDZs75ysJFsCqr1TR7iTHtfXGAbwzIDHIy53dFQW6jehL7JgXA6uoRNYs8eqTNTixnQKux1CWKM4WdoBTYA4n+1AkEA1GQeeOqTGzy1eGBNqwQBqDUV0LSXstXO3s3tv1L9in2n8u49vAZi6yI+Gt1ZZLITUfgD+shaR1t/jaEm2qfWPQJAPlm+h/aOD9WWVR87YzDofM1mMU1ce4unumwPUOaPnPuD8Q6DaC6XSdqs22k2bA0A3aANjQ+dDCBaj24+o047qQJAWGbrwAxy+m1EYzxCV6ItRmQCFoJ0eb1Ag8BW6sGSQmiW1SiQjYhi99ei3e+v25e7Luz6SxHFcdoFt1+SGoStSQJAUhmOVRdbbfPk6sdxxcaxa3+dN6JdertI/91AcSrgOEKoE86yr980Njpm75Lj9NsjR8PasLLCkr04PV1Gmqr6CA==";

    /**
     * 生成验证码
     *
     * @param request request
     * @return
     */
    @ApiOperation("生成验证码")
    @RequestMapping("/genCaptcha")
    public CaptchaResponse<ImageCaptchaVO> genCaptcha(HttpServletRequest request,
                                                      @RequestParam(value = "type", required = false) String type,
                                                      @RequestParam(value = "tag1", required = false) String tag1,
                                                      @RequestParam(value = "tag2", required = false) String tag2,
                                                      @RequestBody(required = false) EncData data
    ) {
        if (StringUtils.isBlank(type)) {
            type = CaptchaTypeConstant.SLIDER;
        }
        if (data != null) {
            decode(data, request);
        }
        GenerateParam param = new GenerateParam();
        param.setType(type);
        param.setBackgroundImageTag(tag1);
        param.setTemplateImageTag(tag2);
        // 扩展功能， 是否将生成图片随机打乱(打乱后会在这前端用canvas重组)
        param.addParam(ShuffleImageProcessor.SHUFFLE_IMAGE_KEY, true);
        // 这行代码设置为true时表示开启混淆
        param.setObfuscate(false);
        if (param.getObfuscate()) {
            if ("ROTATE".equals(type)) {
                // 如果是旋转验证码，开启混淆后，选用有混淆功能的模板
                param.setTemplateImageTag("obfuscate");
            }
            if (CaptchaTypeClassifier.isClickCaptcha(type)) {
                // 开启混淆后，这个用来调整点选类图片的扭曲程度，数值越大扭曲越厉害
                param.addParam("clickImageDistort", ThreadLocalRandom.current().nextInt(3, 7));
            }
        }
        CaptchaResponse<ImageCaptchaVO> responseData = captchaApplication.generateCaptcha(param);
        return responseData;
    }


    /**
     * 验证函数
     *
     * @param data    加密数据
     * @param request request
     * @return ApiResponse<?>
     */
    @ApiOperation("检验验证码")
    @PostMapping("/checkCaptcha")
    public ApiResponse<?> checkCaptcha(@RequestBody EncData data,
                                       HttpServletRequest request) {
        ImageCaptchaTrack sliderCaptchaTrack;
        try {
            sliderCaptchaTrack = decode(data, request);
        } catch (Exception e) {
            // 解密失败
            return ApiResponse.ofMessage(ApiResponseStatusConstant.NOT_VALID_PARAM);
        }
        // 调用 matching 进行校验
        ApiResponse<?> response = captchaApplication.matching(data.getId(), sliderCaptchaTrack);
        if (response.isSuccess()) {
            ApiResponse<Object> convert = response.convert();
            // 验证成功后返回验证token，默认captcha-mate 内部实现了简单的二次验证逻辑， 配置文件中设置captcha.secondary.enabled=true即可
            // 如果想自己实现二次验证的逻辑，这里返回自己生成的验证token，并将captcha.secondary.enabled=false 设置为false，关闭内置的二次验证逻辑
            convert.setData(Collections.singletonMap("token", data.getId()));
            return convert;
        }
        return response;
    }

    @ApiOperation("发送短信验证码")
    @GetMapping("sendSms")
    public Result<?> sendSms(String identityCard, String token) {

        if (StringUtils.isBlank(token)) {
            return Result.error("验证码失效");
        }

        // 二次验证验证码token 只有开启了 captcha.secondary.enabled=true 才可转换为 SecondaryVerificationApplication
        if (!((SecondaryVerificationApplication) captchaApplication).secondaryVerification(token)) {
            return Result.error("验证码失效");
        }

        // 获取指定受试人
        Trainee trainee = traineeService.getOne(new LambdaQueryWrapper<Trainee>().eq(Trainee::getIdentityCard, identityCard));
        if (trainee == null) {
            return Result.error("发送失败！该身份证号没有找到对应的用户");
        }

        // 获取受试人的手机号
        String phone = trainee.getPhone();
        if (phone == null) {
            return Result.error("发送失败！该账号没有设置手机号，请联系管理员处理！");
        }

        //校验手机号是否正常
        if (!isMobile(phone)) {
            return Result.error("发送失败！该账号对应的手机号格式不符合规则，请联系管理员处理！");
        }

        try {
            /* 必要步骤：
             * 实例化一个认证对象，入参需要传入腾讯云账户密钥对secretId，secretKey。
             * 这里采用的是从环境变量读取的方式，需要在环境变量中先设置这两个值。
             * 您也可以直接在代码中写死密钥对，但是小心不要将代码复制、上传或者分享给他人，
             * 以免泄露密钥对危及您的财产安全。
             * SecretId、SecretKey 查询: https://console.cloud.tencent.com/cam/capi */
            Credential cred = new Credential(accessKeyID, accessKeySecret);

            // 实例化一个http选项，可选，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            // 设置代理（无需要直接忽略）
            // httpProfile.setProxyHost("真实代理ip");
            // httpProfile.setProxyPort(真实代理端口);
            /* SDK默认使用POST方法。
             * 如果您一定要使用GET方法，可以在这里设置。GET方法无法处理一些较大的请求 */
            httpProfile.setReqMethod("POST");
            /* SDK有默认的超时时间，非必要请不要进行调整
             * 如有需要请在代码中查阅以获取最新的默认值 */
            httpProfile.setConnTimeout(60);
            /* 指定接入地域域名，默认就近地域接入域名为 sms.tencentcloudapi.com ，也支持指定地域域名访问，例如广州地域的域名为 sms.ap-guangzhou.tencentcloudapi.com */
            httpProfile.setEndpoint("sms.tencentcloudapi.com");


            /* 非必要步骤:
             * 实例化一个客户端配置对象，可以指定超时时间等配置 */
            ClientProfile clientProfile = new ClientProfile();
            /* SDK默认用TC3-HMAC-SHA256进行签名
             * 非必要请不要修改这个字段 */
            clientProfile.setSignMethod("HmacSHA256");
            clientProfile.setHttpProfile(httpProfile);
            /* 实例化要请求产品(以sms为例)的client对象
             * 第二个参数是地域信息，可以直接填写字符串ap-guangzhou，支持的地域列表参考 https://cloud.tencent.com/document/api/382/52071#.E5.9C.B0.E5.9F.9F.E5.88.97.E8.A1.A8 */
            SmsClient client = new SmsClient(cred, "ap-nanjing", clientProfile);
            /* 实例化一个请求对象，根据调用的接口和实际情况，可以进一步设置请求参数
             * 您可以直接查询SDK源码确定接口有哪些属性可以设置
             * 属性可能是基本类型，也可能引用了另一个数据结构
             * 推荐使用IDE进行开发，可以方便的跳转查阅各个接口和数据结构的文档说明 */
            SendSmsRequest req = new SendSmsRequest();


            /* 填充请求参数,这里request对象的成员变量即对应接口的入参
             * 您可以通过官网接口文档或跳转到request对象的定义处查看请求参数的定义
             * 基本类型的设置:
             * 帮助链接：
             * 短信控制台: https://console.cloud.tencent.com/smsv2
             * 腾讯云短信小助手: https://cloud.tencent.com/document/product/382/3773#.E6.8A.80.E6.9C.AF.E4.BA.A4.E6.B5.81 */


            /* 短信应用ID: 短信SdkAppId在 [短信控制台] 添加应用后生成的实际SdkAppId，示例如1400006666 */
            // 应用 ID 可前往 [短信控制台](https://console.cloud.tencent.com/smsv2/app-manage) 查看
            String sdkAppId = "1400894677";
            req.setSmsSdkAppId(sdkAppId);


            /* 短信签名内容: 使用 UTF-8 编码，必须填写已审核通过的签名 */
            // 签名信息可前往 [国内短信](https://console.cloud.tencent.com/smsv2/csms-sign) 或 [国际/港澳台短信](https://console.cloud.tencent.com/smsv2/isms-sign) 的签名管理查看
            String signName = "苏州翔云瑞图";
            req.setSignName(signName);


            /* 模板 ID: 必须填写已审核通过的模板 ID */
            // 模板 ID 可前往 [国内短信](https://console.cloud.tencent.com/smsv2/csms-template) 或 [国际/港澳台短信](https://console.cloud.tencent.com/smsv2/isms-template) 的正文模板管理查看
            String templateId = "2094339";
            req.setTemplateId(templateId);


            /* 模板参数: 模板参数的个数需要与 TemplateId 对应模板的变量个数保持一致，若无模板参数，则设置为空 */

            int random = 1000 + new Random().nextInt(9000);

            System.out.println(random);

            String[] templateParamSet = {String.valueOf(random)};
            req.setTemplateParamSet(templateParamSet);


            /* 下发手机号码，采用 E.164 标准，+[国家或地区码][手机号]
             * 示例如：+8613711112222， 其中前面有一个+号 ，86为国家码，13711112222为手机号，最多不要超过200个手机号 */
            String[] phoneNumberSet = {"+86" + phone};
            req.setPhoneNumberSet(phoneNumberSet);


            /* 用户的 session 内容（无需要可忽略）: 可以携带用户侧 ID 等上下文信息，server 会原样返回 */
            String sessionContext = "";
            req.setSessionContext(sessionContext);


            /* 短信码号扩展号（无需要可忽略）: 默认未开通，如需开通请联系 [腾讯云短信小助手] */
            String extendCode = "";
            req.setExtendCode(extendCode);


            /* 国内短信无需填写该项；国际/港澳台短信已申请独立 SenderId 需要填写该字段，默认使用公共 SenderId，无需填写该字段。注：月度使用量达到指定量级可申请独立 SenderId 使用，详情请联系 [腾讯云短信小助手](https://cloud.tencent.com/document/product/382/3773#.E6.8A.80.E6.9C.AF.E4.BA.A4.E6.B5.81)。*/
            String senderid = "";
            req.setSenderId(senderid);


            /* 通过 client 对象调用 SendSms 方法发起请求。注意请求方法名与请求对象是对应的
             * 返回的 res 是一个 SendSmsResponse 类的实例，与请求对象对应 */
            SendSmsResponse res = client.SendSms(req);

            // 输出json格式的字符串回包
            log.info("发送短信的所有返回结果：{}", SendSmsResponse.toJsonString(res));

            // 获取短信发送状态的返回结果
            String sendResult = res.getSendStatusSet()[0].getCode();
            log.info("短信发送状态的返回结果：{}", sendResult);

            // 短信验证码状态处理
            switch (sendResult) {
                case "Ok":
                    //将random存到redis里面
                    redisUtil.set(phone, random, 300);
                    log.info("短信验证码发送成功");
                    return Result.ok("验证码发送成功，五分钟内有效");

                case "FailedOperation.InsufficientBalanceInSmsPackage":
                    log.info("{}：短信验证码套餐余额不足，请联系管理人员！", sendResult);
                    return Result.error("短信验证码套餐余额不足，请联系管理人员！");

                case "FailedOperation.PhoneNumberInBlackList":
                    log.info("{}：该手机号发送的验证码超过当天设定条数，请联系管理人员！", sendResult);
                    return Result.error("该手机号发送的验证码超过当天设定条数，请联系管理人员！");

                default: // 其他出错情况，直接返回英文报错
                    log.info("短信验证码发送失败，错误信息为：{}", sendResult);
                    return Result.error(sendResult);
            }

        } catch (TencentCloudSDKException e) {
            e.printStackTrace();
            return Result.error("短信验证码发送失败");
        }
    }

    public static boolean isMobile(final String str) {
        Pattern p;
        Matcher m;
        boolean b;
        p = Pattern.compile("^1[3,4578][0-9]{9}$"); // 验证手机号
        m = p.matcher(str);
        b = m.matches();
        return b;
    }


    @ApiOperation("短信验证码验证并修改密码")
    @GetMapping("resetPassword")
    public Result<?> resetPassword(String identityCard, String code, String newPassword) {

        // 获取指定受试人
        Trainee trainee = traineeService.getOne(new LambdaQueryWrapper<Trainee>().eq(Trainee::getIdentityCard, identityCard));
        if (trainee == null) {
            return Result.error("没有找到对应的用户");
        }

        // 获取受试人的手机号
        String phone = trainee.getPhone();
        if (phone == null) {
            return Result.error("发送失败！该账号没有设置手机号，请联系管理员处理！");
        }
        if (!isMobile(phone)) {
            return Result.error("手机号格式不对");
        }

        if (code.length() != 4) {
            return Result.error("验证码长度不对");
        }

        String smsCode = redisUtil.get(phone).toString();
        if (!code.equals(smsCode)) {
            return Result.error("验证码错误或过期");
        }

        trainee.setPassword(newPassword);

        traineeService.changePassword(trainee);

        //密码修改成功后，删除redis值
        redisUtil.del(phone);

        return Result.ok("修改密码成功");
    }

    /**
     * 一个解密函数，直接拷贝到自己项目里即可
     *
     * @param data    加密的数据
     * @param request 请求参数
     * @return ImageCaptchaTrack
     */
    public ImageCaptchaTrack decode(EncData data, HttpServletRequest request) {
        String ki = data.getKi();
        if (ki == null) {
            return null;
        }
        // todo 你的私钥
        String privateKey = PRIVATE_KEY;
        RSAPrivateKey key = RsaUtils.getPrivateKeyFromPemPKCS1(privateKey);
        byte[] keyAndIv = RsaUtils.decrypt(key, Base64.getDecoder().decode(ki));
        String[] split = new String(keyAndIv).split("\\|");
        String aesKkey = split[0];
        String aesIv = split[1];
        byte[] aesKeyBytes = Hex.decode(aesKkey);
        byte[] aesIvBytes = Hex.decode(aesIv);

        if (data.getCustom() != null) {
            byte[] decode = Base64.getDecoder().decode(data.getCustom());
            byte[] decryptData = AesUtils.decrypt(aesKeyBytes, aesIvBytes, decode);
            Map<String, Map<String, String>> custom = new Gson().fromJson(new String(decryptData), new TypeToken<Map<String, Map<String, String>>>() {
            }.getType());
            Map<String, String> session = custom.get("session");
            if (session != null) {
                session.forEach((k, v) -> request.getSession().setAttribute("captcha_" + k, v));
            }
        }

        if (data.getData() != null) {
            byte[] decode = Base64.getDecoder().decode(data.getData());
            byte[] decryptData = AesUtils.decrypt(aesKeyBytes, aesIvBytes, decode);
            ImageCaptchaTrack sliderCaptchaTrack = new Gson().fromJson(new String(decryptData), ImageCaptchaTrack.class);
            return sliderCaptchaTrack;
        }
        return null;
    }

    @Data
    public static class EncData {
        private String id;
        private String data;
        private String ki;
        private String custom;
    }
}

