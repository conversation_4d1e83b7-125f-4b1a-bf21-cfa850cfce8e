package org.jeecg.modules.system.configuration;

import cn.hutool.core.io.FileUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "alipay")
@ConditionalOnProperty(name = "training.memberEnable", havingValue = "true")
// 支付宝支付相关配置
public class AliPayProperties {
    // 应用ID
    private String appId;
    // 应用私钥存储路径
    private String privateKey;
    // 公钥存储路径
    private String alipayPublicKey;
    // 网关
    private String notifyUrl;
    // 支付成功回调地址
    private String serverUrl;
    // 支付成功返回地址
    private String returnUrl;

    @PostConstruct
    public void initKeys() {
        // 文件路径不为空是读取，读取应用私钥文件内容并赋值给appPrivateKey
        if (privateKey.isEmpty() || alipayPublicKey.isEmpty()) {
            log.error("应用私钥文件或支付宝公钥文件路径为空");
            return;
        }
        String readPrivateKey = FileUtil.readUtf8String(privateKey);
        if (readPrivateKey == null) {
            log.error("读取应用私钥文件为空，文件路径为：{}", privateKey);
        }
        this.privateKey = readPrivateKey;

        // 读取支付宝公钥文件内容并赋值给alipayPublicKey
        String readAlipayPublicKey = FileUtil.readUtf8String(alipayPublicKey);
        if (readAlipayPublicKey == null) {
            log.error("读取支付宝公钥文件为空，文件路径为：{}", alipayPublicKey);
        }
        this.alipayPublicKey = readAlipayPublicKey;
    }
}
