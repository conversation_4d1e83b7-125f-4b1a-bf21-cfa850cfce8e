package org.jeecg.modules.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.model.SysUserSysDepartModel;
import org.jeecg.modules.system.model.SysUserVO;
import org.jeecg.modules.system.vo.SysUserDepVo;

import java.util.List;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-20
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {
    /**
     * 通过用户账号查询用户信息
     *
     * @param username
     * @return
     */
    public SysUser getUserByName(@Param("username") String username);

    /**
     * 根据用户id无状态查询
     *
     * @param id
     * @return
     */
    SysUser getUserByIdIgnoreDelFag(@Param("id") String id);

    /**
     * 根据部门Id查询用户信息
     *
     * @param page
     * @param departId
     * @param username 用户登录账户
     * @return
     */
    IPage<SysUser> getUserByDepId(Page page, @Param("departId") String departId, @Param("username") String username);

    /**
     * 根据用户Ids,查询用户所属部门名称信息
     *
     * @param userIds
     * @return
     */
    List<SysUserDepVo> getDepNamesByUserIds(@Param("userIds") List<String> userIds);

    /**
     * 根据部门Ids,查询部门下用户信息
     *
     * @param page
     * @param departIds
     * @param username  用户登录账户
     * @return
     */
    IPage<SysUser> getUserByDepIds(Page page, @Param("departIds") List<String> departIds, @Param("username") String username);

    /**
     * 根据角色Id查询用户信息
     *
     * @param page
     * @param roleId   角色id
     * @param username 用户登录账户
     * @return
     */
    IPage<SysUserVO> getUserByRoleId(Page page, @Param("roleId") String roleId, @Param("username") String username);

    /**
     * 根据角色Id查询用户信息
     *
     * @return
     */
    IPage<SysUserVO> getUsersByRoleCode(Page page, @Param("roleCode") String roleCode, @Param("ew") QueryWrapper<SysUserVO> queryWrapper);


    /**
     * 根据角色Id查询用户信息不分页
     *
     * @return
     */
    List<SysUserVO> getUsersByRoleCode(@Param("roleCode") String roleCode, @Param("ew") QueryWrapper<SysUserVO> queryWrapper);

    /**
     * 根据用户名设置部门ID
     *
     * @param username
     * @param orgCode
     */
    void updateUserDepart(@Param("username") String username, @Param("orgCode") String orgCode);

    /**
     * 根据手机号查询用户信息
     *
     * @param phone
     * @return
     */
    public SysUser getUserByPhone(@Param("phone") String phone);

    /**
     * 查询手机号是否存在
     *
     * @param phone
     * @return
     */
    public SysUser getExistPhone(@Param("phone") String phone, @Param("userId") String userId);

    /**
     * 根据邮箱查询用户信息
     *
     * @param email
     * @return
     */
    public SysUser getUserByEmail(@Param("email") String email);

    /**
     * 根据 orgCode 查询用户，包括子部门下的用户
     *
     * @param page       分页对象, xml中可以从里面进行取值,传递参数 Page 即自动分页,必须放在第一位(你可以继承Page实现自己的分页对象)
     * @param orgCode
     * @param userParams 用户查询条件，可为空
     * @return
     */
    List<SysUserSysDepartModel> getUserByOrgCode(IPage page, @Param("orgCode") String orgCode, @Param("userParams") SysUser userParams);


    /**
     * 查询 getUserByOrgCode 的Total
     *
     * @param orgCode
     * @param userParams 用户查询条件，可为空
     * @return
     */
    Integer getUserByOrgCodeTotal(@Param("orgCode") String orgCode, @Param("userParams") SysUser userParams);

    /**
     * 批量删除角色与用户关系
     *
     * @param roleIdArray
     * <AUTHOR>
     * @Date 2019/12/13 16:10
     */
    void deleteBathRoleUserRelation(@Param("roleIdArray") String[] roleIdArray);

    /**
     * 批量删除角色与权限关系
     *
     * @param roleIdArray
     * <AUTHOR>
     * @Date 2019/12/13 16:10
     */
    void deleteBathRolePermissionRelation(@Param("roleIdArray") String[] roleIdArray);

    /**
     * 查询被逻辑删除的用户
     *
     * @param wrapper
     * @return List<SysUser>
     */
    List<SysUser> selectLogicDeleted(@Param(Constants.WRAPPER) Wrapper<SysUser> wrapper);

    /**
     * 还原被逻辑删除的用户
     *
     * @param userIds 用户id
     * @param entity
     * @return int
     */
    int revertLogicDeleted(@Param("userIds") String userIds, @Param("entity") SysUser entity);

    /**
     * 彻底删除被逻辑删除的用户
     *
     * @param userIds 多个用户id
     * @return int
     */
    int deleteLogicDeleted(@Param("userIds") String userIds);

    /**
     * 更新空字符串为null【此写法有sql注入风险，禁止随便用】
     *
     * @param fieldName
     * @return int
     */
    @Deprecated
    int updateNullByEmptyString(@Param("fieldName") String fieldName);

    /**
     * 根据部门Ids,查询部门下用户信息
     *
     * @param departIds
     * @param username  用户账户名称
     * @return
     */
    List<SysUser> queryByDepIds(@Param("departIds") List<String> departIds, @Param("username") String username);

    /**
     * 根据身份证好查询
     *
     * @param identityCard
     * @return
     */
    SysUser getUserByIDCard(@Param("identityCard") String identityCard);

    /**
     * 替换其他重复的phone手机号码
     *
     * @param id
     * @param phone
     * @return
     */
    int updatePhoneByRepeat(@Param("id") String id, @Param("phone") String phone);

    // 通过真实姓名和手机号（全部和后四位都支持）查找用户
    SysUser getUserByRealNameAndPhone(String realName, String phone, String depRoute);

    int revertLogicDeletedUserName(@Param("username") String username);
}
