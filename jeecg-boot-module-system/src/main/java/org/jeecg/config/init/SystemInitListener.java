package org.jeecg.config.init;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.modules.system.service.ISysGatewayRouteService;
import org.jeecg.modules.training.async.download.AsyncDownloadService;
import org.jeecg.modules.training.async.facial.FacialHandlerService;
import org.jeecg.modules.training.async.materials.MaterialHandlerService;
import org.jeecg.modules.training.async.paper.PushPaperService;
import org.jeecg.modules.training.async.paper.SaveUserPaperService;
import org.jeecg.modules.training.async.phase.PushPhaseService;
import org.jeecg.modules.training.async.share.PushShareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

/**
 * @desc: 启动程序，初始化路由配置
 * @author: flyme
 */
@Slf4j
@Component
public class SystemInitListener implements ApplicationListener<ApplicationReadyEvent>, Ordered {

    @Autowired
    private PushPhaseService userPhaseHandleService;

    @Autowired
    private ISysGatewayRouteService sysGatewayRouteService;

    @Autowired
    private PushPaperService pushPaperService;

    @Autowired
    private MaterialHandlerService materialHandlerService;

    @Autowired
    private AsyncDownloadService asyncDownloadService;

    @Autowired
    private FacialHandlerService faciallHandlerService;

    @Autowired
    private PushShareService pushShareService;

	@Autowired
    private SaveUserPaperService saveUserPaperService;
    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent) {

        log.info(" 服务已启动，初始化路由配置 ###################");

        if (applicationReadyEvent.getApplicationContext().getDisplayName().contains("AnnotationConfigServletWebServerApplicationContext")) {
            sysGatewayRouteService.addRoute2Redis(CacheConstant.GATEWAY_ROUTES);
        }

        log.info(" 启动业务服务 ");

		saveUserPaperService.start();

        materialHandlerService.start();

        faciallHandlerService.start();

        pushPaperService.start();

        userPhaseHandleService.start();

        asyncDownloadService.start();

        pushShareService.start();

    }

    @Override
    public int getOrder() {
        return 1;
    }
}
