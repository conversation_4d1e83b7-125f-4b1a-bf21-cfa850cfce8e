package org.jeecg.config.init;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.training.async.download.AsyncDownloadService;
import org.jeecg.modules.training.async.facial.FacialHandlerService;
import org.jeecg.modules.training.async.materials.MaterialHandlerService;
import org.jeecg.modules.training.async.paper.PushPaperService;
import org.jeecg.modules.training.async.paper.SaveUserPaperService;
import org.jeecg.modules.training.async.phase.PushPhaseService;
import org.jeecg.modules.training.async.share.PushShareService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

/**
 * @desc: 启动程序，初始化路由配置
 * @author: flyme
 */
@Slf4j
@Component
public class SystemCloseListener implements ApplicationListener<ContextClosedEvent>, Ordered {

    @Autowired
    private PushPaperService pushPaperService;

    @Autowired
    private PushPhaseService userPhaseHandleService;

    @Autowired
    private MaterialHandlerService materialHandlerService;

    @Autowired
    private AsyncDownloadService asyncDownloadService;

    @Autowired
    private FacialHandlerService faciallHandlerService;

    @Autowired
    private PushShareService pushShareService;
	
	@Autowired
    private SaveUserPaperService saveUserPaperService;

    @Override
    public void onApplicationEvent(@NotNull ContextClosedEvent contextClosedEvent) {
        pushPaperService.close();
        materialHandlerService.close();
        userPhaseHandleService.close();
        asyncDownloadService.close();
        faciallHandlerService.close();
        pushShareService.close();
		saveUserPaperService.close();
    }

    @Override
    public int getOrder() {
        return 1;
    }
}
