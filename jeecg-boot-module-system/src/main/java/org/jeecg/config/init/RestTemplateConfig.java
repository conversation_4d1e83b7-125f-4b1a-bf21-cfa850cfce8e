package org.jeecg.config.init;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * RestTemplate配置类
 */
@Slf4j
//@Configuration
public class RestTemplateConfig {
 
    /**
     * 常用远程调用RestTemplate
     * @return restTemplate
     */
    @Bean("restTemplate")
    public RestTemplate restTemplate(){
        RestTemplate restTemplate = new RestTemplate();
        // 设置通用的请求头
        HeaderRequestInterceptor myHeader = new HeaderRequestInterceptor("orgId", "1");
        List<ClientHttpRequestInterceptor> interceptors = new ArrayList<>();
        interceptors.add(myHeader);
        restTemplate.setInterceptors(interceptors);
        return restTemplate;
    }
 
   /**
     * 拦截器类，为restTemplatep后续调用请求时携带请求头
     */
    public static class HeaderRequestInterceptor implements ClientHttpRequestInterceptor{
 
        private final String header;
 
        private final String value;
 
        public HeaderRequestInterceptor(String header, String value){
            this.header = header;
            this.value = value;
        }
 
        @Override
        public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
            request.getHeaders().set(header, value);
            return execution.execute(request, body);
        }
 
    }
 
 
 
}