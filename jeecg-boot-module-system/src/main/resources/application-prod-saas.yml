server:
  port: 49010
  tomcat:
    max-swallow-size: -1
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /training
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*
  max-http-header-size:
management:
  endpoints:
    web:
      exposure:
        include: metrics,httptrace

spring:
  servlet:
    multipart:
      max-file-size: 300MB
      max-request-size: 300MB
  mail:
    host: smtp.163.com
    username: jeec<PERSON>@163.com
    password: ??
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  ## quartz定时任务,采用数据库方式
  quartz:
    job-store-type: jdbc
    initialize-schema: embedded
    #定时任务启动开关，true-开  false-关
    auto-startup: false
    #启动时更新己存在的Job
    overwrite-existing-jobs: false
    properties:
      org:
        quartz:
          scheduler:
            instanceName: MyScheduler
            instanceId: AUTO
          jobStore:
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 60000
            clusterCheckinInterval: 10000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  #json 时间戳统一转换
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  jpa:
    open-in-view: false
  activiti:
    check-process-definitions: false
    #启用作业执行器
    async-executor-activate: false
    #启用异步执行器
    job-executor-activate: false
  aop:
    proxy-target-class: true
  #配置freemarker
  freemarker:
    # 设置模板后缀名
    suffix: .ftl
    # 设置文档类型
    content-type: text/html
    # 设置页面编码格式
    charset: UTF-8
    # 设置页面缓存
    cache: false
    prefer-file-system-access: false
    # 设置ftl文件路径
    template-loader-path:
      - classpath:/templates
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
    #Spring Boot 2.6+后映射匹配的默认策略已从AntPathMatcher更改为PathPatternParser,需要手动指定为ant-path-matcher
    pathmatch:
      matching-strategy: ant_path_matcher
  resource:
    static-locations: classpath:/static/,classpath:/public/
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    druid:
      stat-view-servlet:
        enabled: false
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
        wall:
          multi-statement-allow: true
          none-base-statement-allow: true
          condition-and-alway-true-allow: true
          block-allow: true
      datasource:
        master:
          url: ******************************************************************************************************************************************************************************************
          username: admin
          password: trnSvr2024
          driver-class-name: com.mysql.cj.jdbc.Driver
          # 多数据源配置
          #multi-datasource1:
          #url: ***************************************************************************************************************************************************************************************************************************
          #username: root
          #password: root
          #driver-class-name: com.mysql.cj.jdbc.Driver
  #redis 配置
  redis:
    database: 0
    host: 127.0.0.1
    lettuce:
      pool:
        max-active: 8   #最大连接数据库连接数,设 -1 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 100ms
    password: ''
    port: 6379
#mybatis plus 设置
mybatis-plus:
  mapper-locations: classpath*:org/jeecg/modules/**/xml/*Mapper.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
      id-type: ASSIGN_ID
      # 默认数据库表下划线命名
      table-underline: true
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
#jeecg专用配置
minidao :
  base-package: org.jeecg.modules.jmreport.*
jeecg :
  # 是否启用安全模式
  safeMode: false
  # 签名密钥串(前后端要一致，正式发布请自行修改)
  signatureSecret: dd05f1c54d63749eda95f9fa6d49v442a
  # 本地：local\Minio：minio\阿里云：alioss
  uploadType: local
  # 转换视频高度设置
  videoMaxHeight: 720
  path :
    # webapp文件路径
    webapp: /home/<USER>/training/server
    
    # 普通文件上传根目录 设置
    upload: /data/training/server/upload-images

    # 数据文件上传根目录
    dataRoot: /data/training/server/data
    
    # icons 下面分两个目录，一个放前景图标(front)，一个放背景图标(background)
    icons: /data/training/server/icons
    
    # 系统资源存放路径
    systemResource: /data/training/server/document

    # 小程序码生成路径
    miniProgram: /data/training/server/miniProgram/
    miniProgramQrCode: /data/training/server/miniProgram/miniProgramQrCode/
    minniProgramAvatar: /data/training/server/miniProgram/avatar/
    
    # HLS 文件位置
    videoHlsPath: /data/training/server/hls
  shiro:
    excludeUrls: /test/jeecgDemo/demo3,/test/jeecgDemo/redisDemo/**,/visual/**,/map/**,/jmreport/bigscreen2/**,/**/training.apk,/**/ver_*,/**/category/getRandomBackground
    # 是否启用超级管理员模式，开启后可跳过接口鉴权
    adminSuperMode: false
  # 阿里云oss存储和大鱼短信秘钥配置
  oss:
    accessKey: ??
    secretKey: ??
    endpoint: oss-cn-beijing.aliyuncs.com
    bucketName: jeecgdev
    # ElasticSearch 8设置
  elasticsearch:
    username: elastic
    password: G1pn7x=sIA3hw_NrnySZ
    cluster-name: jeecg-ES
    # 使用localhost会出现拒绝连接的错误
    cluster-nodes: https://************:9200
    check-enabled: false
    # 连接ElasticSearch的https证书指纹
    fingerprint: 8c984694d784f51ade5a5ed2e2aa132dd193cef52e43f4f0655231f8eeeab35d
    # ES索引库名称
    indexName: study
  # 表单设计器配置
  desform:
    # 主题颜色（仅支持 16进制颜色代码）
    theme-color: "#1890ff"
    # 文件、图片上传方式，可选项：qiniu（七牛云）、system（跟随系统配置）
    upload-type: system
    map:
      # 配置百度地图的AK，申请地址：https://lbs.baidu.com/apiconsole/key?application=key#/home
      baidu: ??
  # 在线预览文件服务器地址配置
  file-view-domain: 127.0.0.1:8012
  # minio文件上传
  minio:
    minio_url: http://minio.jeecg.com
    minio_name: ??
    minio_pass: ??
    bucketName: otatest
  #大屏报表参数设置
  jmreport:
    mode: prod
    #数据字典是否进行saas数据隔离，自己看自己的字典
    saas: false
    #是否需要校验token
    is_verify_token: true
    #必须校验方法
    verify_methods: remove,delete,save,add,update
    #自定义项目前缀
    customPrePath:
    pageSize:
      - 10
      - 20
      - 30
      - 40
  #Wps在线文档
  wps:
    domain: https://wwo.wps.cn/office/
    appid: ??
    appsecret: ??
  #xxl-job配置
  xxljob:
    enabled: false
    adminAddresses: http://127.0.0.1:9080/xxl-job-admin
    appname: ${spring.application.name}
    accessToken: ''
    address: 127.0.0.1:30007
    ip: 127.0.0.1
    port: 30007
    logPath: logs/jeecg/job/jobhandler/
    logRetentionDays: 30
  route:
    config:
      data-id: jeecg-gateway-router
      group: DEFAULT_GROUP
      #自定义路由配置 yml nacos database
      data-type: database
  #分布式锁配置
  redisson:
    address: 127.0.0.1:6379
    password:
    type: STANDALONE
    enabled: true
#cas单点登录
cas:
  prefixUrl: http://cas.example.org:8443/cas
#Mybatis输出sql日志
logging:
  level:
    org.jeecg.modules.system.mapper : info
  config: classpath:logback-spring-prod.xml
#swagger
knife4j:
  #开启增强配置
  enable: true
  #开启生产环境屏蔽
  production: true
  basic:
    enable: true
    username: jeecg
    password: jeecg1314
#第三方登录
justauth:
  enabled: true
  type:
    GITHUB:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/github/callback
    WECHAT_ENTERPRISE:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/wechat_enterprise/callback
      agent-id: ??
    DINGTALK:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/dingtalk/callback
    WECHAT_OPEN:
      client-id: wx1a2995a64b605a63
      client-secret: ddc9a3512576b24e24d00b77f38edf92
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/wechat_open/callback
  cache:
    type: default
    prefix: 'demo::'
    timeout: 1h
#第三方APP对接
third-app:
  enabled: false
  type:
    #企业微信
    WECHAT_ENTERPRISE:
      enabled: false
      #CORP_ID
      client-id: ??
      #SECRET
      client-secret: ??
      #自建应用id
      agent-id: ??
      #自建应用秘钥（新版企微需要配置）
      # agent-app-secret: ??
    #钉钉
    DINGTALK:
      enabled: false
      # appKey
      client-id: ??
      # appSecret
      client-secret: ??
      agent-id: ??

WECHAT:
  path: /data/training/server/watch/qr
  faceVideoPath: /data/training/server/photo/
  model: /data/training/server/model/sf3.0_models
  # 临界值 用于人脸识别判断
  criticalValue: 90

training:
  # app端是否开启会员功能
  memberEnable: true

# 支付宝支付相关配置
alipay:
  # 应用ID
  appId: "2021005116627514"
  # 应用私钥存储路径
  privateKey: "/data/training/server/document/alipay/appPrivateKey.txt"
  # 支付宝公钥存储路径
  alipayPublicKey: "/data/training/server/document/alipay/alipayPublicKey.txt"
  # 网关
  serverUrl: "https://openapi.alipay.com/gateway.do"
  # 支付成功回调地址
  notifyUrl: "https://px.wyf168.cn/training/app/aliPay/notify"
  # 支付成功返回地址
  returnUrl: "https://px.wyf168.cn/training/payment?isPay=true"

# 微信支付相关配置
wechatpay:
  # 商户号
  mchid: "1563865921"
  # appID
  appid: "wx843101bff7cb1457"
  # APIV3密钥
  apiV3Key: "7fG9pR2qK5wY3zX8vB1nL6cD4sH0jM2e"
  # 商户API证书私钥的存放路径
  privateKeyPath: "/data/training/server/document/wechatPay/privateKey/apiclient_key.pem"
  # 微信支付公钥的存放路径
  publicKeyPath: "/data/training/server/document/wechatPay/publicKey/pub_key.pem"
  # 微信支付公钥ID
  publicKeyId: "PUB_KEY_ID_0115638659212025040100326400003027"
  # 支付成功回调地址（供微信官方回调）
  notifyUrl: "https://px.wyf168.cn/training/app/wechatPay/wechatPayNotify"
  # 支付成功返回地址（供本地payment调用）
  redirectUrl: "https://px.wyf168.cn/training/payment/#/order"
  # 商户API证书序列号
  merchantSerialNumber: "65498B79DE8D3E92626E5BFB7EFB4D734D9792C8"