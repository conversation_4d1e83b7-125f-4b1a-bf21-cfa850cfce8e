package org.jeecg;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.*;
import com.itextpdf.text.pdf.parser.PdfImageObject;
import com.seeta.sdk.*;
import com.seeta.sdk.util.LoadNativeCore;
import com.seeta.sdk.util.SeetafaceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.jeecg.common.util.PasswordUtil;


import org.jeecg.modules.training.controller.admin.StudyController;
import org.jeecg.modules.training.entity.Study;
import org.jeecg.modules.training.async.facial.impl.FileConstant;
import org.jeecg.modules.training.vo.ExamVO;
import org.jeecg.modules.training.vo.StudyOfflineVO;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.beans.PropertyDescriptor;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.UUID;

//@RunWith(SpringRunner.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,classes = JeecgSystemApplication.class)
@SpringBootTest(classes = JeecgSystemApplication.class)
@Slf4j
public class SampleTest {

    @Value(value = "${jeecg.path.upload}")
    private String uploadpath;

    @Autowired
    private StudyController studyController;

    /*public void test102() throws Exception {
        LoadNativeCore.LOAD_NATIVE(com.seeta.sdk.SeetaDevice.SEETA_DEVICE_AUTO);
        com.seeta.sdk.FaceDetector faceDetector = new com.seeta.sdk.FaceDetector(new  com.seeta.sdk.SeetaModelSetting(new String[]{"F:\\BaiduNetdiskDownload\\sf3.0_models" + "/face_detector.csta"}, com.seeta.sdk.SeetaDevice.SEETA_DEVICE_AUTO));
        System.out.println("创建成功");
    }*/

    @Test
    public void test101() {
        /*LoadNativeCore.LOAD_NATIVE(SeetaDevice.SEETA_DEVICE_AUTO);
        log.info(Arrays.toString(FileConstant.face_detector));

        System.loadLibrary("SeetaFaceDetector600.dll");*/

        try {
            LoadNativeCore.LOAD_NATIVE(SeetaDevice.SEETA_DEVICE_AUTO);
            //人脸检测器
            FaceDetector detector = new FaceDetector(new SeetaModelSetting(FileConstant.face_detector, SeetaDevice.SEETA_DEVICE_AUTO));
            //关键点定位器 5点
            FaceLandmarker faceLandmarker = new FaceLandmarker(new SeetaModelSetting(FileConstant.face_landmarker_pts5, SeetaDevice.SEETA_DEVICE_AUTO));

            //人脸向量特征提取和对比器
            FaceRecognizer faceRecognizer = new FaceRecognizer(new SeetaModelSetting(FileConstant.face_recognizer, SeetaDevice.SEETA_DEVICE_AUTO));

            System.out.println(faceRecognizer.GetExtractFeatureSize());

            //两张图片
            String fileName = "C:\\Users\\<USER>\\Desktop\\fsdownload\\1f179238-5084-409b-a87b-e8509a896ed23.jpg";
            String fileName2 = "C:\\Users\\<USER>\\Desktop\\fsdownload\\767f0f53-7c61-4566-8e62-c78f00bb54c48.jpg";
            //第1张照片
            SeetaImageData image1 = SeetafaceUtil.toSeetaImageData(fileName);
            //第一张照片人脸识别
            SeetaRect[] detects1 = detector.Detect(image1);

            SeetaPointF[] pointFS1 = new SeetaPointF[faceRecognizer.GetExtractFeatureSize()];
            int[] masks1 = new int[faceRecognizer.GetExtractFeatureSize()];
            //第一张图片，第一个人脸关键点定位，有多个人脸的情况下，只取第一个人脸（这是测试，先这样写）
            faceLandmarker.mark(image1, detects1[0], pointFS1, masks1);
            //第一张图片，第一个人脸向量特征提取features1
            float[] features1 = new float[faceRecognizer.GetExtractFeatureSize()];
            faceRecognizer.Extract(image1, pointFS1, features1);

            //第2张照片
            SeetaImageData image2 = SeetafaceUtil.toSeetaImageData(fileName2);

            //第二张图片，人脸识别
            SeetaRect[] detects2 = detector.Detect(image2);

            SeetaPointF[] pointFS2 = new SeetaPointF[faceRecognizer.GetExtractFeatureSize()];
            int[] masks2 = new int[faceRecognizer.GetExtractFeatureSize()];
            //第二张图片，第一个人脸，关键点识别
            faceLandmarker.mark(image2, detects2[0], pointFS2, masks2);

            //第二张图片，第一个人脸，向量特征提取features2
            float[] features2 = new float[faceRecognizer.GetExtractFeatureSize()];
            faceRecognizer.Extract(image2, pointFS2, features2);

            System.out.println(Arrays.toString(features1));
            System.out.println(Arrays.toString(features2));
            //两个人脸向量做对比，得出分数
            if (features1 != null && features2 != null) {
                float calculateSimilarity = faceRecognizer.CalculateSimilarity(features1, features2);
                System.out.printf("相似度:%f\n", calculateSimilarity);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void test100() {
        String str = "StudyFile\\pdf\\202301\\fa85367b954eeb0ffdce6fc0866f454e.pdf";
        System.out.println(str.substring(0, str.indexOf(FileUtil.getName(str))) + "thum_" + FileUtil.getName(str));
        /*System.out.println("1263724".replace("abc", ""));
        System.out.println(FileUtil.getParent("CompressStudyFile\\\\pdf\\\\202301\\\\e6aedc362c68b134a7a7274aa313cb36.pdf", 1));
        System.out.println(FileUtil.getPrefix("CompressStudyFile\\\\pdf\\\\202301\\\\e6aedc362c68b134a7a7274aa313cb36.pdf"));
        String src = "http://localhost:49010/training/sys/common/static/jeditor/8080_1672896119999.png";
        System.out.println(FileUtil.getName("http://localhost:49010/training/sys/common/static/jeditor/8080_1672896119999.png"));
        System.out.println(src.substring(0, src.indexOf(FileUtil.getName(src))));*/
        /*Double factor = 0.5d;
        String str = "http://localhost:49010/training/sys/common/static/jeditor/QQ图片20210610202049_1672887668788.jpg";
        System.out.println(FileUtil.extName(str));
        System.out.println(FileUtil.getName(str));
        System.out.println(str.indexOf("jeditor"));
        System.out.println(str.substring(str.indexOf("jeditor")));
        File file = new File("/home/<USER>/training/system/upload-images" + File.separator + str.substring(str.indexOf("jeditor")));
        System.out.println(file.getAbsolutePath());
        System.out.println(file.isFile());
        try {
            BufferedImage image = ImageIO.read(file);
            System.out.println("Width: " + image.getWidth());
            System.out.println("Height: " + image.getHeight());
            BufferedImage newImage = resizeImage(image, new Double(image.getWidth() * factor).intValue(), new Double(image.getHeight() * factor).intValue());
            //保存图片到文件
            ImageIO.write(newImage, FileUtil.extName(str), new File("/home/<USER>/training/system/upload-images" + File.separator + "jeditor/" + " thum_" + FileUtil.getName(str)));

        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println(file.isFile());*/
    }

    public static BufferedImage resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight) throws IOException {
        Image resultingImage = originalImage.getScaledInstance(targetWidth, targetHeight, Image.SCALE_AREA_AVERAGING);
        BufferedImage outputImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
        outputImage.getGraphics().drawImage(resultingImage, 0, 0, null);
        return outputImage;
    }

    @Test
    public void testCompressHtml() {
        File source = new File("D:\\Data\\StudyFile\\html\\202301\\e0c256fec35b3eb2097cc9425b18e8db.html");
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        String ymName = format.format(new Date());
        String fileName = UUID.randomUUID().toString().replaceAll("-", "") + ".html";

        //用于存储html字符串
        StringBuilder bizPath = new StringBuilder();

        //文件路径
        bizPath.append("CompressStudyFile");
        bizPath.append(File.separator);
        bizPath.append("html");
        bizPath.append(File.separator);
        bizPath.append(ymName);
        bizPath.append(File.separator);
        bizPath.append(fileName);
        //File target = new File("D:\\Data\\" + bizPath);
        File target = new File("/" + bizPath);
        if (!target.getParentFile().exists()) {
            target.getParentFile().mkdirs();
        }
        //CompressUtil.CompressHtml(source, target, 36);
    }

    @Test
    public void testStudyController() {
        Study study = new Study();
        study.setCategoryId(13L);
        study.setContent("StudyFile\\pdf\\202301\\a8b8a012d6bc69422f69fbf334f0fa42.pdf");
        study.setDisabled(false);
        study.setName("测试20230105");
        study.setNeedDownload(true);
        study.setTimeFactor("上船后1个月");
        study.setTotalTime(2);
        study.setType(0);
        studyController.add(study);
    }

    @Test
    public void testJsonObject() {
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject1 = new JSONObject();
        JSONObject jsonObject = JSONObject.parseObject("{\"study\":[\"1559904697366724609,34,1672153917508849,1672153921768399\",\"1559904697366724609,0,1672207441745836,1672207463438362\"]}");
        Object study = jsonObject.get("study");
        //ArrayList list = new ArrayList();

        ArrayList<StudyOfflineVO> studyOfflineVOS = new ArrayList<>();
        ArrayList list = JSON.parseObject(study.toString(), ArrayList.class);
        for (Object o : list) {
            String[] split = o.toString().split(",");
            StudyOfflineVO studyOfflineVO = new StudyOfflineVO();
            studyOfflineVO.setUserId(split[0]);
            studyOfflineVO.setStudyId(split[1]);
            studyOfflineVO.setStartTime(new Date(Long.valueOf(split[2])));
            studyOfflineVO.setEndTime(new Date(Long.valueOf(split[3])));
            //StudyOfflineVO studyOfflineVO = JSON.parseObject(o.toString(), StudyOfflineVO.class);
            studyOfflineVOS.add(studyOfflineVO);
        }
        System.out.println(studyOfflineVOS);
        //System.out.println(list.get(0));


        //JSONObject jsonObject = JSONObject.parseObject("{\"study\":\"1234\"}");
        //System.out.println(jsonObject);
    }

    public static void main(String[] args) throws Exception{
        String SRC = "E:\\WeChat Files\\wxid_nkvuyi1oad2m22\\FileStorage\\File\\2022-12\\十二黄金法则.pdf";
        String DEST = "D:\\Data\\output.pdf";

        File file = new File(DEST);
        file.getParentFile().mkdirs();
        manipulatePdf(SRC, DEST);
    }


    @Test
    public void test10() throws Exception {
        Double FACTOR = Double.valueOf("38") / 72D;
        System.out.println(FACTOR);
        System.out.println(new Date(1672207441745836l));
    }

    public static void manipulatePdf(String src, String dest) throws DocumentException, IOException {
        Double FACTOR = Double.valueOf("38") / 72D;
        System.out.println(FACTOR);
        //float FACTOR = 0.8f;
        PdfReader reader = new PdfReader(src);
        int n = reader.getXrefSize();
        PdfObject object;
        PRStream stream;
        // Look for image and manipulate image stream
        for (int i = 0; i < n; i++) {
            object = reader.getPdfObject(i);
            if (object == null || !object.isStream())
                continue;
            stream = (PRStream) object;
            if (!PdfName.IMAGE.equals(stream.getAsName(PdfName.SUBTYPE)))
                continue;
            if (!PdfName.DCTDECODE.equals(stream.getAsName(PdfName.FILTER)))
                continue;
            PdfImageObject image = new PdfImageObject(stream);
            BufferedImage bi = image.getBufferedImage();
            if (bi == null)
                continue;
            int width = (int) (bi.getWidth() * FACTOR);
            int height = (int) (bi.getHeight() * FACTOR);
            if (width <= 0 || height <= 0)
                continue;
            BufferedImage img = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            AffineTransform at = AffineTransform.getScaleInstance(FACTOR, FACTOR);
            Graphics2D g = img.createGraphics();
            g.drawRenderedImage(bi, at);
            ByteArrayOutputStream imgBytes = new ByteArrayOutputStream();
            ImageIO.write(img, "JPG", imgBytes);
            stream.clear();
            stream.setData(imgBytes.toByteArray(), false, PRStream.NO_COMPRESSION);
            stream.put(PdfName.TYPE, PdfName.XOBJECT);
            stream.put(PdfName.SUBTYPE, PdfName.IMAGE);
            stream.put(PdfName.FILTER, PdfName.DCTDECODE);
            stream.put(PdfName.WIDTH, new PdfNumber(width));
            stream.put(PdfName.HEIGHT, new PdfNumber(height));
            stream.put(PdfName.BITSPERCOMPONENT, new PdfNumber(8));
            stream.put(PdfName.COLORSPACE, PdfName.DEVICERGB);
        }
        reader.removeUnusedObjects();
        // Save altered PDF
        PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(dest));
        stamper.setFullCompression();
        stamper.close();
        reader.close();

    }


/*	@Resource
	private JeecgDemoMapper jeecgDemoMapper;
	@Resource
	private IJeecgDemoService jeecgDemoService;
//	@Resource
//	private ISysDataLogService sysDataLogService;
	@Resource
	private MockController mock;*/

    @Test
    public void testQuery() {

        ExamVO examVO = new ExamVO();
        examVO.setDelFlag(1);
        examVO.setUsername("小明");
        PropertyDescriptor[] propertyDescriptors = PropertyUtils.getPropertyDescriptors(examVO);
        log.info("=================开始测试查询框架========================");
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            System.out.println(propertyDescriptor);
        }
        System.out.println();
        log.info("=================测试查询框架结束========================");
    }

    @Test
    public void testGetRuleMap() {
        System.out.println(PasswordUtil.encrypt("admin", "123456", "RCGTeGiH"));
		/*QueryGenerator queryGenerator = new QueryGenerator();
		Map<String, SysPermissionDataRuleModel> ruleMap = queryGenerator.getRuleMap();
		System.out.println(ruleMap);*/
    }

    @Test
    public void testUUID() {
        System.out.println("m_t_12x5_shipName".startsWith("s"));

        //System.out.println(UUID.randomUUID());
    }

/*
	@Test
	public void testSelect() {
		System.out.println(("----- selectAll method test ------"));
		List<JeecgDemo> userList = jeecgDemoMapper.selectList(null);
		Assert.assertEquals(5, userList.size());
		userList.forEach(System.out::println);
	}

	@Test
	public void testXmlSql() {
		System.out.println(("----- selectAll method test ------"));
		List<JeecgDemo> userList = jeecgDemoMapper.getDemoByName("Sandy12");
		userList.forEach(System.out::println);
	}

	*//**
     * 测试事务
     *//*
	@Test
	public void testTran() {
		jeecgDemoService.testTran();
	}*/

    //author:lvdandan-----date：20190315---for:添加数据日志测试----
    /**
     * 测试数据日志添加
     */
//	@Test
//	public void testDataLogSave() {
//		System.out.println(("----- datalog test ------"));
//		String tableName = "jeecg_demo";
//		String dataId = "4028ef81550c1a7901550c1cd6e70001";
//		String dataContent = mock.sysDataLogJson();
//		sysDataLogService.addDataLog(tableName, dataId, dataContent);
//	}
    //author:lvdandan-----date：20190315---for:添加数据日志测试----
}
