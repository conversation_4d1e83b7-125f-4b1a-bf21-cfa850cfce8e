package org.jeecg.modules.training.util;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import javax.imageio.ImageIO;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import org.junit.BeforeClass;
import org.junit.Test;

/**
 * SegmentedBarChartImageGenerator 工具类的单元测试类
 * 测试分段条形图生成的各种场景和边界条件
 */
public class SegmentedBarChartTest {
    
    /** 测试输出目录路径 */
    private static final String TEST_OUTPUT_DIR = "target/test-output/SegmentedBarChartTest";

    /**
     * 测试类初始化方法
     * 在所有测试方法执行前创建测试输出目录
     * 
     * @throws IOException 当目录创建失败时抛出
     */
    @BeforeClass
    public static void setUp() throws IOException {
        Path outputDir = Paths.get(TEST_OUTPUT_DIR);
        // 清理测试输出目录
        if (Files.exists(outputDir)) {
            Files.walk(outputDir)
                    .sorted((a, b) -> b.compareTo(a)) // 反向排序，确保先删除文件再删除目录
                    .map(Path::toFile)
                    .forEach(File::delete);
        }
        // 创建测试输出目录
        if (!Files.exists(outputDir)) {
            Files.createDirectories(outputDir);
        }
    }

    /**
     * 测试有效分数范围内的图表生成
     * 验证不同分数值的图表生成结果
     * 
     * @throws IOException 当图片保存失败时抛出
     */
    @Test
    public void testGenerateChartWithValidScores() throws IOException {
        double[] testScores = {1.0, 1.9, 2.0, 2.9, 3.0, 3.9, 4.0, 5.0};
        
        for (double score : testScores) {
            // 生成图表
            BufferedImage image = SegmentedBarChartImageGenerator.generateChart(score);
            
            // 验证图像不为空且尺寸正确
            assertNotNull("生成的图像不应为空", image);
            assertEquals("图像宽度应为500", 500, image.getWidth());
            assertEquals("图像高度应为200", 200, image.getHeight());
            
            // 保存图像用于手动检查
            String fileName = String.format("chart_score_%.1f.png", score);
            ImageIO.write(image, "png", new File(TEST_OUTPUT_DIR, fileName));
        }
    }

    /**
     * 测试小于最小分数时的异常情况
     * 期望抛出 IllegalArgumentException
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGenerateChartWithScoreBelowMinimum() {
        SegmentedBarChartImageGenerator.generateChart(0.9);
    }

    /**
     * 测试大于最大分数时的异常情况
     * 期望抛出 IllegalArgumentException
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGenerateChartWithScoreAboveMaximum() {
        SegmentedBarChartImageGenerator.generateChart(5.1);
    }

    /**
     * 测试生成图片的类型是否正确
     */
    @Test
    public void testGenerateChartImageType() {
        BufferedImage image = SegmentedBarChartImageGenerator.generateChart(3.0);
        assertEquals("图像类型应为 TYPE_INT_ARGB", 
            BufferedImage.TYPE_INT_ARGB, image.getType());
    }

    /**
     * 测试每个分数区间的图表生成
     * 
     * @throws IOException 当图片保存失败时抛出
     */
    @Test
    public void testGenerateChartForEachRange() throws IOException {
        // 测试每个分数区间的代表值
        double[] rangeTestScores = {1.5, 2.5, 3.5, 4.5};
        String[] rangeNames = {"normal", "mild", "moderate", "severe"};
        
        for (int i = 0; i < rangeTestScores.length; i++) {
            BufferedImage image = SegmentedBarChartImageGenerator.generateChart(rangeTestScores[i]);
            String fileName = String.format("chart_range_%s.png", rangeNames[i]);
            ImageIO.write(image, "png", new File(TEST_OUTPUT_DIR, fileName));
        }
    }

    /**
     * 测试特殊分数值的处理
     * 
     * @throws IOException 当图片保存失败时抛出
     */
    @Test
    public void testSpecialScores() throws IOException {
        // 测试带多位小数的分数
        double[] specialScores = {1.23, 2.45, 3.78, 4.99};
        
        for (double score : specialScores) {
            BufferedImage image = SegmentedBarChartImageGenerator.generateChart(score);
            assertNotNull("特殊分数图像生成失败", image);
            String fileName = String.format("chart_special_%.2f.png", score);
            ImageIO.write(image, "png", new File(TEST_OUTPUT_DIR, fileName));
        }
    }
} 