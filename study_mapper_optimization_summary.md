# StudyMapper.xml 优化总结

## 修改目标

参考 UserPaperMapper.xml 中 findPapers 的实现方式，改写 StudyMapper.xml 中的 findStudies 查询，使得当 `departId`、`memberType`、`memberLevel` 三个参数为 null 时，不执行不必要的增强版本逻辑。

## 具体修改内容

### 1. 增强版本的额外字段（条件化）

**修改前：**
```sql
s.feedback_status,
s.charge_status,
-- 添加所需会员等级字段
CASE
    WHEN s.charge_status = 0 THEN 0
    ELSE s.charge_type
END as charge_type
```

**修改后：**
```sql
s.feedback_status,
s.charge_status
<!-- 增强版本的额外字段 -->
<if test="departId != null and memberType != null and memberLevel != null">
    ,
    -- 添加所需会员等级字段
    CASE
        WHEN s.charge_status = 0 THEN 0
        ELSE s.charge_type
    END as charge_type,
    -- 添加显示顺序字段
    COALESCE(ido.sequence, 999) as display_order
</if>
```

### 2. 增强版本的额外表关联（条件化）

**修改前：**
```sql
left join study_user_state sus3 on sus3.study_id = s.id and sus3.user_id = #{userId} and sus3.state = 3
-- 关联iteams_display_order表获取显示顺序
left join iteams_display_order ido on ido.member_package_lev = #{memberLevel}
    and ido.iteam_package_lev = CASE
        WHEN s.charge_status = 0 THEN 0
        ELSE s.charge_type
    END
```

**修改后：**
```sql
left join study_user_state sus3 on sus3.study_id = s.id and sus3.user_id = #{userId} and sus3.state = 3
<!-- 增强版本的额外表关联 -->
<if test="departId != null and memberType != null and memberLevel != null">
    -- 关联iteams_display_order表获取显示顺序
    LEFT JOIN iteams_display_order ido ON ido.member_package_lev = #{memberLevel}
        AND ido.iteam_package_lev = CASE
            WHEN s.charge_status = 0 THEN 0
            ELSE s.charge_type
        END
</if>
```

### 3. 排序逻辑（智能选择）

**修改前：**
```sql
-- 按照iteams_display_order表中定义的顺序排序，然后按创建时间倒序
order by COALESCE(ido.sequence, 999) asc, s.create_time desc
```

**修改后：**
```sql
<!-- 排序逻辑：增强版本优先按display_order排序，基础版本按原有逻辑排序 -->
<choose>
    <when test="departId != null and memberType != null and memberLevel != null">
        -- 增强版本：按照iteams_display_order表中定义的顺序排序，然后按创建时间倒序
        ORDER BY COALESCE(ido.sequence, 999) ASC, s.create_time DESC
    </when>
    <otherwise>
        -- 基础版本：按原有逻辑排序
        ORDER BY s.create_time DESC
    </otherwise>
</choose>
```

### 4. 权限过滤逻辑（保持不变）

权限过滤逻辑已经使用了条件判断，无需修改：
```sql
-- 企业会员制权限过滤逻辑
<if test="memberType != null and memberType == 1">
    and (
        -- 免费资料，所有人都可以访问
        s.charge_status = 0
        -- 或者用户套餐等级满足资料所需等级
        or (s.charge_status = 1 and #{memberLevel} >= COALESCE(s.charge_type, 1))
        -- 或者已单独购买（企业会员模式检查depart_ordered_iteams表）
        or exists (...)
    )
</if>
-- 船员会员制权限过滤逻辑
<if test="memberType != null and memberType == 0">
    and (...)
</if>
```

## 优化效果

### ✅ 性能优化
1. **减少不必要的字段查询**：基础版本不再查询 `charge_type` 和 `display_order` 字段
2. **减少不必要的表关联**：基础版本不再关联 `iteams_display_order` 表
3. **简化排序逻辑**：基础版本使用简单的 `ORDER BY s.create_time DESC`

### ✅ 功能完整性
1. **增强版本功能不变**：当三个参数都有值时，执行完整的增强功能
2. **基础版本向后兼容**：当三个参数为 null 时，执行原有的基础查询逻辑
3. **智能切换**：根据参数是否为 null 自动选择执行逻辑

### ✅ 代码一致性
1. **与 UserPaperMapper.xml 保持一致**：使用相同的条件判断模式
2. **清晰的注释说明**：每个条件块都有明确的注释
3. **统一的代码风格**：保持与项目整体风格一致

## 使用场景

### 基础版本（参数为 null）
- 适用于不需要会员制功能的场景
- 向后兼容原有调用方式
- 性能更优，查询更简单

### 增强版本（参数有值）
- 适用于需要会员制功能的场景
- 包含权限过滤和自定义排序
- 功能完整，支持所有增强特性

## 技术特点

1. **条件化执行**：根据参数是否为 null 决定执行逻辑
2. **性能优化**：避免不必要的表关联和字段查询
3. **向后兼容**：保持原有功能不受影响
4. **代码统一**：与 UserPaperMapper.xml 保持一致的实现模式

这样的优化既保证了功能的完整性，又提升了基础版本的查询性能，是一个很好的平衡方案。
