package org.jeecg.modules.training.test;

import org.jeecg.modules.training.entity.Packages;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 套餐时间处理升级测试类
 * 验证从Date到LocalDate的迁移是否正确工作
 */
@SpringBootTest
public class PackageTimeUpgradeTest {

    @Test
    public void testPackageExpiredMethod() {
        // 测试过期判定方法
        Packages packages = new Packages();
        
        // 测试1：过期时间为null，应该返回false
        packages.setExpireTime(null);
        assertFalse(packages.isExpired(), "过期时间为null时应该返回false");
        
        // 测试2：过期时间为昨天，应该返回true
        packages.setExpireTime(LocalDate.now().minusDays(1));
        assertTrue(packages.isExpired(), "过期时间为昨天时应该返回true");
        
        // 测试3：过期时间为今天，应该返回false
        packages.setExpireTime(LocalDate.now());
        assertFalse(packages.isExpired(), "过期时间为今天时应该返回false");
        
        // 测试4：过期时间为明天，应该返回false
        packages.setExpireTime(LocalDate.now().plusDays(1));
        assertFalse(packages.isExpired(), "过期时间为明天时应该返回false");
    }

    @Test
    public void testPackageValidMethod() {
        // 测试有效性判定方法
        Packages packages = new Packages();
        LocalDate today = LocalDate.now();
        
        // 测试1：开始时间或过期时间为null，应该返回false
        packages.setStartTime(null);
        packages.setExpireTime(today);
        assertFalse(packages.isValid(), "开始时间为null时应该返回false");
        
        packages.setStartTime(today);
        packages.setExpireTime(null);
        assertFalse(packages.isValid(), "过期时间为null时应该返回false");
        
        // 测试2：当前日期在有效期内，应该返回true
        packages.setStartTime(today.minusDays(1)); // 昨天开始
        packages.setExpireTime(today.plusDays(1)); // 明天过期
        assertTrue(packages.isValid(), "当前日期在有效期内时应该返回true");
        
        // 测试3：当前日期等于开始时间，应该返回true
        packages.setStartTime(today);
        packages.setExpireTime(today.plusDays(1));
        assertTrue(packages.isValid(), "当前日期等于开始时间时应该返回true");
        
        // 测试4：当前日期等于过期时间，应该返回true
        packages.setStartTime(today.minusDays(1));
        packages.setExpireTime(today);
        assertTrue(packages.isValid(), "当前日期等于过期时间时应该返回true");
        
        // 测试5：当前日期在开始时间之前，应该返回false
        packages.setStartTime(today.plusDays(1));
        packages.setExpireTime(today.plusDays(2));
        assertFalse(packages.isValid(), "当前日期在开始时间之前时应该返回false");
        
        // 测试6：当前日期在过期时间之后，应该返回false
        packages.setStartTime(today.minusDays(2));
        packages.setExpireTime(today.minusDays(1));
        assertFalse(packages.isValid(), "当前日期在过期时间之后时应该返回false");
    }

    @Test
    public void testTimeCalculations() {
        // 测试时间计算逻辑
        LocalDate startDate = LocalDate.of(2024, 1, 1);
        
        // 测试1：一年套餐的过期时间计算
        LocalDate oneYearExpire = startDate.plusYears(1).minusDays(1);
        assertEquals(LocalDate.of(2024, 12, 31), oneYearExpire, 
                "一年套餐应该从2024-01-01到2024-12-31");
        
        // 测试2：三个月套餐的过期时间计算
        LocalDate threeMonthExpire = startDate.plusMonths(3).minusDays(1);
        assertEquals(LocalDate.of(2024, 3, 31), threeMonthExpire,
                "三个月套餐应该从2024-01-01到2024-03-31");
        
        // 测试3：续费逻辑：在原过期时间基础上增加时间
        LocalDate originalExpire = LocalDate.of(2024, 6, 30);
        LocalDate renewedExpire = originalExpire.plusMonths(6);
        assertEquals(LocalDate.of(2024, 12, 30), renewedExpire,
                "续费6个月应该从2024-06-30延长到2024-12-30");
        
        // 测试4：新套餐开始时间：上一个套餐过期时间+1天
        LocalDate nextStartDate = originalExpire.plusDays(1);
        assertEquals(LocalDate.of(2024, 7, 1), nextStartDate,
                "新套餐应该从上一个套餐过期后的第二天开始");
    }

    @Test
    public void testEdgeCases() {
        // 测试边界情况
        
        // 测试1：闰年的2月29日
        LocalDate leapYearStart = LocalDate.of(2024, 2, 29);
        LocalDate leapYearExpire = leapYearStart.plusYears(1).minusDays(1);
        assertEquals(LocalDate.of(2025, 2, 28), leapYearExpire,
                "闰年2月29日开始的一年套餐应该在次年2月28日过期");
        
        // 测试2：月末开始的套餐
        LocalDate monthEndStart = LocalDate.of(2024, 1, 31);
        LocalDate monthEndExpire = monthEndStart.plusMonths(1).minusDays(1);
        assertEquals(LocalDate.of(2024, 2, 29), monthEndExpire,
                "1月31日开始的一个月套餐应该在2月29日过期（2024年是闰年）");
        
        // 测试3：年末开始的套餐
        LocalDate yearEndStart = LocalDate.of(2024, 12, 31);
        LocalDate yearEndExpire = yearEndStart.plusYears(1).minusDays(1);
        assertEquals(LocalDate.of(2025, 12, 30), yearEndExpire,
                "12月31日开始的一年套餐应该在次年12月30日过期");
    }
}
