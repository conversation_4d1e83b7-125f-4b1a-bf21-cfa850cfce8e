CREATE TABLE study_feedback
(
    `id`          BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '反馈id',
    `trainee_id`  VARCHAR(32) COMMENT '反馈船员id',
    `type`        TINYINT(1) COMMENT '反馈类型：0，错漏纠正，1问题建议，2，其他',
    `content`     TEXT COMMENT '反馈内容',
    `create_by`   VARCHAR(40) COMMENT '创建人',
    `update_by`   VARCHAR(40) COMMENT '修改人',
    `create_time` DATETIME COMMENT '创建时间',
    `update_time` DATETIME COMMENT '修改时间',
    `dep_id`      VARCHAR(32) COMMENT '当前登录公司',
    `status`      TINYINT(1) DEFAULT 0 COMMENT '反馈处理状态 （0：未处理；1：已处理）',
    `result`      TEXT COMMENT '反馈结果',
    `del_flag`    TINYINT(1) DEFAULT 0 COMMENT '删除状态（0：正常；1：删除）'
) COMMENT ='学习资料反馈表';

-- run ok