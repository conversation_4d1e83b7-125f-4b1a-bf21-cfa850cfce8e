ALTER TABLE user_paper ADD COLUMN exam_results tinyint(1) COMMENT '考试状态（0 未完成，1 未通过，2 通过 ）';
ALTER TABLE user_paper CHANGE COLUMN exam_results exam_results tinyint(1) DEFAULT 0 COMMENT '考试状态（0 未完成，1 未通过，2 通过 ）';

# 未通过
UPDATE user_paper SET exam_results = 1 WHERE status = 2 AND passed_times < total_times AND del_flag = 0;

# 通过
UPDATE user_paper SET exam_results = 2 WHERE status = 2 AND passed_times >= total_times AND del_flag = 0;

# 未完成
UPDATE user_paper SET exam_results = 0 WHERE status <2 AND del_flag = 0;

-- run ok