# 船员表新增 member_first属性，标记什么时候第一次购买了会员，（设置后以后再购买会员此值不变）
ALTER TABLE `trainee`
    ADD COLUMN `member_first` datetime NULL COMMENT '会员首次购买时间';

# 船员表新增 member_start 属性，标记当前会员从什么时候开始（=购买时间）
ALTER TABLE `trainee`
    ADD COLUMN `member_start` datetime NULL COMMENT '会员开始时间';

# 船员表新增 member_expired 属性，标记什么时候过期
ALTER TABLE `trainee`
    ADD COLUMN `member_expired` datetime NULL COMMENT '会员过期时间';

# orders（订单）表
create table `orders`
(
    `id`             bigint primary key auto_increment comment '订单ID',
    `name`           varchar(100)   null comment '创建人姓名',
    `user_id`        varchar(32)    null comment '创建人ID',
    `create_time`    datetime       null comment '创建时间',
    `orders_num`     varchar(255)   null comment '订单号',
    `pay_type`       tinyint    null comment '支付方式 0 支付宝，1 微信',
    `pay_orders_num` varchar(255)   null comment '支付订单号',
    `pay_time`       datetime       null comment '支付时间',
    `pay_status`     tinyint     null comment '支付状态 0 未支付，1 已支付，2 已过期',
    `amount`         decimal(10, 2) null comment '订单金额',
    `status`         tinyint     null comment '订单状态 0 未支付，1 已支付，2 已过期',
    `dep_route`      text           null comment '过滤路径',
    `del_flag`       tinyint     default 0 comment '删除标记 0 未删除，1 已删除'
);

-- saas run