update study_log sl
set department = (select id from sys_depart sd where sd.org_code = sl.department);

ALTER TABLE paper ADD COLUMN keep_duration int DEFAULT 0 COMMENT '保留时长';
ALTER TABLE study ADD COLUMN phase_level VARCHAR(30) DEFAULT 0 COMMENT '所属阶段';

ALTER TABLE `jeecg-boot`.`category` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
ALTER TABLE `jeecg-boot`.`checklist` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
ALTER TABLE `jeecg-boot`.`checklist_item` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
ALTER TABLE `jeecg-boot`.`checklist_task` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
ALTER TABLE `jeecg-boot`.`checklist_task_item` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
ALTER TABLE `jeecg-boot`.`exam` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
ALTER TABLE `jeecg-boot`.`exam_question` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
ALTER TABLE `jeecg-boot`.`knowledge` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
ALTER TABLE `jeecg-boot`.`managers` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
ALTER TABLE `jeecg-boot`.`paper` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
ALTER TABLE `jeecg-boot`.`questions` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
ALTER TABLE `jeecg-boot`.`rule` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
ALTER TABLE `jeecg-boot`.`study` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
ALTER TABLE `jeecg-boot`.`study_log` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
ALTER TABLE `jeecg-boot`.`study_log_num` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
ALTER TABLE `jeecg-boot`.`sys_announcement` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
ALTER TABLE `jeecg-boot`.`sys_depart` CHANGE COLUMN `del_flag` `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记';
