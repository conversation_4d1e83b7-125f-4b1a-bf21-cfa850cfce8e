# packages_log表
create table `packages_log`
(
    `id`              bigint primary key auto_increment comment '日志id',
    `package_id`      bigint      null comment '被修改的套餐id',
    `operator_id`     varchar(32) null comment '操作人id',
    `action_type`     tinyint(1)  null comment '操作类型（0：新增、1：修改、2：删除）',
    `old_surplus_num` int         null comment '修改前的剩余数量',
    `new_surplus_num` int         null comment '修改后的剩余数量',
    `old_expire_time` datetime    null comment '修改前的到期时间',
    `new_expire_time` datetime    null comment '修改后的到期时间',
    `create_time`     datetime    null comment '创建时间',
    `remark`          text        NULL COMMENT '备注'
) comment '套餐修改日志表';


-- saas run