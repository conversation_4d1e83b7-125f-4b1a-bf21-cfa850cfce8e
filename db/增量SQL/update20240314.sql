ALTER TABLE category
    ADD dep_route VARCHAR(1000) COMMENT '多层次部门路径';
CREATE INDEX IDX_CATEGORY_DEP_ROUTE ON category (dep_route);
UPDATE category
SET dep_route = '0'
WHERE dep_route IS NULL;

ALTER TABLE exam
    ADD dep_route VARCHAR(1000) COMMENT '多层次部门路径';
CREATE INDEX IDX_EXAM_DEP_ROUTE ON exam (dep_route);
UPDATE exam e,sys_user su
SET e.dep_route = su.dep_route
WHERE e.user_id = su.id;

ALTER TABLE knowledge
    ADD dep_route VARCHAR(1000) COMMENT '多层次部门路径';
CREATE INDEX IDX_KNOWLEDGE_DEP_ROUTE ON knowledge (dep_route);
UPDATE knowledge
SET dep_route = '0'
WHERE dep_route IS NULL;

ALTER TABLE paper
    ADD dep_route VARCHAR(1000) COMMENT '多层次部门路径';
CREATE INDEX IDX_PAPER_DEP_ROUTE ON paper (dep_route);
UPDATE paper
SET dep_route = '0'
WHERE dep_route IS NULL;


ALTER TABLE questions
    ADD dep_route VARCHAR(1000) COMMENT '多层次部门路径';
CREATE INDEX IDX_QUESTIONS_DEP_ROUTE ON questions (dep_route);
UPDATE questions
SET dep_route = '0'
WHERE dep_route IS NULL;


ALTER TABLE rule
    ADD dep_route VARCHAR(1000) COMMENT '多层次部门路径';
CREATE INDEX IDX_RULE_DEP_ROUTE ON rule (dep_route);
UPDATE rule
SET dep_route = '0'
WHERE dep_route IS NULL;

ALTER TABLE study
    ADD dep_route VARCHAR(1000) COMMENT '多层次部门路径';
CREATE INDEX IDX_STUDY_DEP_ROUTE ON study (dep_route);
UPDATE study
SET dep_route = '0'
WHERE dep_route IS NULL;


ALTER TABLE study_log
    ADD dep_route VARCHAR(1000) COMMENT '多层次部门路径';
CREATE INDEX IDX_STUDY_LOG_DEP_ROUTE ON study_log (dep_route);
UPDATE study_log sl,sys_user su
SET sl.dep_route = su.dep_route
WHERE sl.user_id = su.id;


ALTER TABLE sys_role
    ADD dep_route VARCHAR(1000) COMMENT '多层次部门路径';
CREATE INDEX IDX_SYS_ROLE_DEP_ROUTE ON sys_role (dep_route);
UPDATE sys_role
SET dep_route = '0'
WHERE dep_route IS NULL;


ALTER TABLE sys_user
    ADD dep_route VARCHAR(1000) COMMENT '多层次部门路径';
CREATE INDEX IDX_SYS_USER_DEP_ROUTE ON sys_user (dep_route);
UPDATE sys_user
SET dep_route = '0'
WHERE dep_route IS NULL;


ALTER TABLE sys_position
    ADD dep_route VARCHAR(1000) COMMENT '多层次部门路径';
CREATE INDEX IDX_SYS_POSITION_DEP_ROUTE ON sys_position (dep_route);
UPDATE sys_position
SET dep_route = '0'
WHERE dep_route IS NULL;

UPDATE sys_user su
SET dep_route = (SELECT sd.route
                 FROM sys_user_depart sud,
                      sys_depart sd
                 WHERE su.id = sud.user_id
                   AND sd.id = sud.dep_id
                 LIMIT 1);

UPDATE sys_user
SET dep_route = SUBSTRING_INDEX(dep_route, '/', 3)
WHERE dep_route IS NOT NULL;
