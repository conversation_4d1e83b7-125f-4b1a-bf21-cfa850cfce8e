ALTER TABLE sys_position
    ADD COLUMN share_id  BIGINT NULL;
CREATE INDEX sys_position_share_id_index
    ON sys_position (share_id);

ALTER TABLE `sys_position`
    ADD COLUMN `industry_ids` VARCHAR(255) COMMENT '行业关联id集合';

ALTER TABLE sys_position
    ADD COLUMN share_mode VARCHAR(10) DEFAULT '0' COMMENT '分享模式';


UPDATE sys_position
SET industry_ids = (SELECT CONCAT(',', id, ',')
                    FROM industry
                    WHERE (industry_name = '船公司' OR industry_name = '船管公司')
                      AND industry.del_flag = 0);


ALTER TABLE rule
    ADD COLUMN share_id BIGINT COMMENT '分享来源ID';

ALTER TABLE rule
    CHANGE COLUMN share_id share_id BIGINT NULL COMMENT '分享来源ID';

CREATE INDEX rule_share_id_index
    ON rule (share_id);


ALTER TABLE rule
    ADD COLUMN share_mode VARCHAR(10) DEFAULT '0' COMMENT '分享模式';

ALTER TABLE `rule`
    ADD COLUMN `industry_ids` VARCHAR(255) COMMENT '行业关联id集合';


UPDATE rule
SET industry_ids = (SELECT CONCAT(',', id, ',')
                    FROM industry
                    WHERE (industry_name = '船公司' OR industry_name = '船管公司')
                      AND industry.del_flag = 0);

-- run ok