
create table trainee_sync
(
    id     varchar(32) not null,
    states <PERSON><PERSON><PERSON>   null ,
    primary key (id)
);

insert into trainee_sync (id,states)
select id,'{}'
from trainee;


create table category_sync
(
    id     bigint not null,
    states <PERSON><PERSON><PERSON>   null ,
    primary key (id)
);

insert into category_sync (id,states)
select id,'{}'
from category;


create table knowledge_sync
(
    id     bigint not null,
    states <PERSON><PERSON><PERSON>   null ,
    primary key (id)
);

insert into knowledge_sync (id,states)
select id,'{}'
from knowledge;

create table rule_sync
(
    id     bigint not null,
    states <PERSON><PERSON><PERSON>   null ,
    primary key (id)
);

insert into rule_sync (id,states)
select id,'{}'
from rule;


create table questions_sync
(
    id     bigint not null,
    states <PERSON><PERSON><PERSON>  null,
    primary key (id)
);

insert into questions_sync (id,states)
select id,'{}'
from questions;

create table paper_sync
(
    id     bigint not null,
    states JSO<PERSON>   null ,
    primary key (id)
);

insert into paper_sync (id,states)
select id,'{}'
from paper;

create table paper_question_sync
(
    id     bigint not null,
    states <PERSON><PERSON><PERSON>   null ,
    primary key (id)
);

insert into paper_question_sync (id,states)
select id,'{}'
from paper_question;

create table user_paper_sync
(
    id     bigint not null,
    states <PERSON><PERSON><PERSON>   null ,
    primary key (id)
);

insert into user_paper_sync (id,states)
select id,'{}'
from user_paper;


create table exam_sync
(
    id     bigint not null,
    states JSON   null ,
    primary key (id)
);

insert into exam_sync (id,states)
select id,'{}'
from exam;

create table exam_question_sync
(
    id   bigint not null,
    states JSON   null ,
    primary key (id)
);

insert into exam_question_sync (id,states)
select id,'{}'
from exam_question;

create table mmpi_sync
(
    id     varchar(50) not null,
    states JSON   null ,
    primary key (id)
);

insert into mmpi_sync (id,states)
select id,'{}'
from mmpi;


create table study_sync
(
    id     bigint not null,
    states JSON   null ,
    primary key (id)
);

insert into study_sync (id,states)
select id,'{}'
from study;


create table study_log_sync
(
    id     bigint not null,
    states JSON   null ,
    primary key (id)
);

insert into study_log_sync (id,states)
select id,'{}'
from study_log;



create table phase_sync
(
    id     bigint not null,
    states JSON   null ,
    primary key (id)
);

insert into phase_sync (id,states)
select id,'{}'
from phase;

create table phase_item_sync
(
    id     bigint not null,
    states JSON   null ,
    primary key (id)
);

insert into phase_item_sync (id,states)
select id,'{}'
from phase_item;


create table user_phase_sync
(
    id     bigint not null,
    states JSON   null ,
    primary key (id)
);

insert into user_phase_sync (id,states)
select id,'{}'
from user_phase;

create table user_phase_item_sync
(
    id     bigint not null,
    states JSON   null ,
    primary key (id)
);

insert into user_phase_item_sync (id,states)
select id,'{}'
from user_phase_item;

create table sys_user_sync
(
    id     varchar(32) not null,
    states JSON   null ,
    primary key (id)
);

insert into sys_user_sync (id,states)
select id,'{}'
from sys_user;

create table sys_role_sync
(
    id     varchar(32) not null,
    states JSON   null ,
    primary key (id)
);

insert into sys_role_sync (id,states)
select id,'{}'
from sys_role;

create table sys_user_role_sync
(
    id     varchar(32) not null,
    states JSON   null ,
    primary key (id)
);

insert into sys_user_role_sync (id,states)
select id,'{}'
from sys_user_role;

create table sys_user_depart_sync
(
    id     varchar(32) not null,
    states JSON   null ,
    primary key (id)
);

insert into sys_user_depart_sync (id,states)
select id,'{}'
from sys_user_depart;

create table sys_trainee_depart_sync
(
    id     varchar(32) not null,
    states JSON   null ,
    primary key (id)
);

insert into sys_trainee_depart_sync (id,states)
select id,'{}'
from sys_trainee_depart;

create table sys_depart_sync
(
    id     varchar(32) not null,
    states JSON   null ,
    primary key (id)
);

insert into sys_depart_sync (id,states)
select id,'{}'
from sys_depart;


create table team_sync
(
    id     varchar(32) not null,
    states JSON   null ,
    primary key (id)
);

insert into team_sync (id,states)
select id,'{}'
from team;


create table sys_position_sync
(
    id     varchar(32) not null,
    states JSON   null ,
    primary key (id)
);

insert into sys_position_sync (id,states)
select id,'{}'
from sys_position;

alter table user_paper
    modify post varchar(50) null comment '推送时用户的职务';

#
# insert paper_sync (id, states) values (1000000001, JSON_OBJECT("11111",1));
#
# update paper_sync set states = JSON_SET(states,'$."22222"',2) where id=1000000001;
# update paper_sync set states = JSON_SET(states,'$."11111"',1) where id=1000000001;
#
# select * from paper_sync where id = 1000000001;
#
#
# select id from paper_sync where id = 1000000001 and states -> '$."11111"' =1;

# insert category_sync (id, states) values (10000, JSON_OBJECT("00cf5b5b3f174aada8e0e2b4149f62c6",1));

-- run ok