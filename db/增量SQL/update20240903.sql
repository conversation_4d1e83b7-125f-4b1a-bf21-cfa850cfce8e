alter table phase_item add column share_id BIGINT comment '分享id';

UPDATE phase_item pi LEFT JOIN (SELECT pi1.id,
                                       pi1.name,
                                       pi1.data_type,
                                       pi1.data_id,
                                       pi2.id        AS id2,
                                       pi2.name      AS name2,
                                       pi2.data_type AS data_type2,
                                       pi2.data_id   AS data_id2
                                FROM (SELECT pi.*, ph.share_id AS ph_id
                                      FROM phase_item pi
                                           LEFT JOIN phase ph ON pi.phase_id = ph.id
                                      WHERE ph.del_flag = 0
                                        AND pi.del_flag = 0
                                        AND ph.share_id IS NOT NULL) pi1
                                     LEFT JOIN
                                     (SELECT pi.*, ph.id AS ph_id
                                      FROM phase_item pi
                                           LEFT JOIN phase ph ON pi.phase_id = ph.id
                                      WHERE ph.share_id IS NULL) pi2
                                     ON pi1.ph_id = pi2.ph_id AND pi1.data_type = pi2.data_type AND
                                        pi1.name = pi2.name) t ON pi.id = t.id
SET pi.share_id = t.id2
WHERE t.id2 IS NOT NULL;

-- run ok