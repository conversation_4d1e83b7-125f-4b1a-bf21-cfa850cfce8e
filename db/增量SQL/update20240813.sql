alter table paper add column share_mode  varchar(10) default '0' comment '分享模式';
alter table paper add column share_id BIGINT comment '分享id';
alter table category add column share_mode varchar(10) default '0' comment '分享模式';
alter table category add column share_id BIGINT comment '分享id';
alter table phase add column share_mode varchar(10) default '0' comment '分享模式';
alter table phase add column share_id BIGINT comment '分享id';
alter table study add column share_mode varchar(10) default '0' comment '分享模式';
alter table study add column share_id BIGINT comment '分享id';
alter table questions add column share_mode varchar(10) default '0' comment '分享模式';
alter table questions add column share_id BIGINT comment '分享id';
alter table knowledge add column share_mode varchar(10) default '0' comment '分享模式';
alter table knowledge add column share_id BIGINT comment '分享id';
alter table phase add column dep_route VARCHAR(760) null comment '多层次部门路径';
-- run ok