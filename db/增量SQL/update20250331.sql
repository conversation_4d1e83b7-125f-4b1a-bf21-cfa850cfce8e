# 不规则权限标识修改（均为多处共用的按钮权限无法精确定位）、前端代码已同步修改
UPDATE `sys_permission` SET `perms` = 'home:results' WHERE `perms` = 'showSourceStatics';
UPDATE `sys_permission` SET `perms` = 'global:share' WHERE `perms` = 'showShareMode';
UPDATE `sys_permission` SET `perms` = 'managers:import' WHERE `perms` = 'saasManagersImport';
UPDATE `sys_permission` SET `perms` = 'managers:import' WHERE `perms` = 'coscoManagersImport';
UPDATE `sys_permission` SET `perms` = 'video:recheck' WHERE `perms` = 'rechcekVideo';

# 不规则访问url修改
UPDATE `sys_permission` SET `url` = '/team' WHERE `url` = '/training/team';
UPDATE `sys_permission` SET `url` = '/trainee' WHERE `url` = '/training/trainee';
UPDATE `sys_permission` SET `url` = '/feedback' WHERE `url` = '/training/feedback';
UPDATE `sys_permission` SET `url` = '/industry' WHERE `url` = '/training/industry';

ALTER TABLE `sys_permission` MODIFY COLUMN `perms` text;
UPDATE `sys_permission` SET `perms` = '/sys/permission/list,/sys/permission/edit,/sys/permission/add,/sys/duplicate/check,/sys/duplicate/check/buttonField' WHERE `name` = '菜单管理';

# sys_permission 表统一导入
# 注意，执行前需要先在后台管理界面统一菜单`url`及按钮`perms`书写规范，详细参照前端部署说明
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/camunda'; -- name: 流程管理
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/categoryManager'; -- name: 分类管理
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/checklist'; -- name: 检查和许可证管理
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/message'; -- name: 消息中心
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/monitor'; -- name: 系统监控
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/phasemg'; -- name: 培训阶段管理
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/report2'; -- name: 统计报表
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/studyMg'; -- name: 学习管理
UPDATE `sys_permission` SET `perms` = '/adm/paper/list' WHERE `url` = '/exam'; -- name: 考试管理
UPDATE `sys_permission` SET `perms` = '/adm/studyLog/oneMonthRecord' WHERE `url` = '/summary'; -- name: 统计汇总
UPDATE `sys_permission` SET `perms` = '/adm/studyTimeDepartment/list,/adm/studyTimeUser/list,/adm/studyTimePerDepartment/list,/adm/studyTimePerDepartment/completeRate,/sys/sysDepart/noExamNo,/sys/sysDepart/getPsychicWarnNo,/adm/examQuestion/getFactorRate3,/adm/studyStatistics/list,/adm/studyLogDailyStatistics/getList,/adm/studyTimeFleet/getAvgTime,/sys/annountCement/listByUser,/adm/trainee/getWillAndExpireMemberCount,/sys/sysDepart/list,/adm/examQuestion/getFactorRate,/adm/studyTimeFleet/list,/adm/studyTimePerFleet/list,/sys/user/updatePassword,/sys/user/getCurrentUserDeparts,/sys/dict/refleshCache,/sys/tenant/getCurrentUserTenant,/sys/dict/queryAllDictItems,/sys/user/setSecurityQuestion,/sys/selectDepart' WHERE `url` = '/dashboard'; -- name: 首页
UPDATE `sys_permission` SET `perms` = '/adm/systemResource/getFile' WHERE `url` = '/isystem'; -- name: 系统管理
UPDATE `sys_permission` SET `perms` = '/adm/systemResource/getFile' WHERE `url` = '/resources'; -- name: 资料下载
UPDATE `sys_permission` SET `perms` = '/adm/team/list' WHERE `url` = '/team'; -- name: 船舶管理
UPDATE `sys_permission` SET `perms` = '/sys/position/getNameAndCode,/adm/team/list,/sys/user/editDept,/adm/trainee/changePassword,/sys/user/queryUserRole,/sys/user/userDepartList,/adm/trainee/deleteBatch,/adm/trainee/changeMemberStatus,/adm/trainee/delete,/sys/user/frozenBatch,/adm/trainee/examExportXls,/sys/user/importExcel,/adm/trainee/listForExam,/miniProgram/sysUser/physicalDelete,/act/process/extActProcess/doSyncUser,/adm/trainee/unLinkTraineeToOpenId,/adm/userPaper/reset,/adm/userPaper/batchReset,/adm/userPaper/batchUpdateStatus,/adm/userPaper/add,/adm/category/queryTreeByKeyWord' WHERE `url` = '/trainee'; -- name: 受试人管理
UPDATE `sys_permission` SET `perms` = '/sys/transfer/resetSyncState,/sys/transfer/setCommand,/sys/transfer/queryById,/sys/transfer/getLogs,/sys/transfer/getLogContent,/sys/transfer/delLog,/sys/transfer/setOrgId,/sys/transfer/getHeartLogList,/sys/transfer/list,/adm/team/listAllTeam' WHERE `url` = '/transferList'; -- name: 中转服务管理
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/address'; -- name: 通讯录
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/bpmnDiagram'; -- name: 流程图
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/bpmnModeler'; -- name: 流程设计图
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/checklist/index'; -- name: 检查和许可证配置
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/checklistTask/index'; -- name: 申请管理
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/definition'; -- name: 审批流程配置
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/exam/examDetailMMPI/:id'; -- name: 考卷详情MMPI
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/exam/history/:id'; -- name: 人员的历史考卷
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/message/manage'; -- name: 消息管理
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/message/template'; -- name: 消息模板管理
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/monitor/datasource'; -- name: 数据源管理
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/monitor/druid'; -- name: SQL监控
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/monitor/quartz'; -- name: 定时任务
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/monitor/redis'; -- name: Redis监控
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/monitor/route'; -- name: 网关路由
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/monitor/server'; -- name: 性能监控
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/monitor/trace'; -- name: 请求追踪
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/paperQuestion'; -- name: 试卷题目
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/report/chartDemo'; -- name: EChartDemo
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/statisticst'; -- name: 布局统计报表
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/summary/monthStudy'; -- name: 近一个月学习时长
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/system/category'; -- name: 分类字典
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/system/depart-user'; -- name: 我的部门
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/system/ossfile'; -- name: 对象存储
UPDATE `sys_permission` SET `perms` = '' WHERE `url` = '/system/tenant'; -- name: 租户管理
UPDATE `sys_permission` SET `perms` = '/adm/category/edit,/adm/category/add,/adm/category/setShareCategory,/adm/category/getShareList,/adm/category/queryTreeByKeyWord,/adm/industry/listByIds,/adm/category/pagelist,/adm/category/delete,/adm/category/deleteBatch,/adm/category/importExcel,/adm/category/exportXls,/adm/category/autoSort,/adm/category/queryById,/sys/common/upload' WHERE `url` = '/category'; -- name: 分类
UPDATE `sys_permission` SET `perms` = '/adm/exam/examDetail,/adm/examQuestion/getAll' WHERE `url` = '/exam/examDetail/:id'; -- name: 考卷详情
UPDATE `sys_permission` SET `perms` = '/adm/exam/list,/adm/exam/cancel,/adm/exam/delete,/adm/exam/deleteBatch,/adm/exam/importExcel,/adm/exam/exportXls,/adm/exam/personSumbit,/adm/exam/getToBeScoreExam,/sys/position/getNameAndCode,/adm/team/list,/adm/category/pagelist,/adm/paper/list,/adm/category/queryTreeByKeyWord,/adm/exam/examDetail,/adm/examQuestion/getAll' WHERE `url` = '/getToBeScoreExam'; -- name: 待打分考卷列表
UPDATE `sys_permission` SET `perms` = '/adm/exam/list,/adm/exam/reset,/adm/exam/deleteBatch,/adm/exam/importExcel,/adm/exam/exportXls,/adm/exam/exportScoreReport,/sys/position/getNameAndCode,/adm/team/list,/adm/category/pagelist,/adm/paper/list,/adm/category/queryTreeByKeyWord' WHERE `url` = '/examList'; -- name: 已提交考卷
UPDATE `sys_permission` SET `perms` = '/adm/industry/add,/adm/industry/edit,/adm/industry/deleteBatch,/adm/industry/delete,/adm/industry/list' WHERE `url` = '/industry'; -- name: 行业管理
UPDATE `sys_permission` SET `perms` = '/adm/industry/listByIds,/adm/questions/list,/adm/questions/delete,/adm/questions/deleteBatch,/adm/knowledge/queryall,/adm/category/queryTreeByKeyWord,/adm/questions/queryById,/adm/questions/getShareList,/adm/questions/setShareQuestions' WHERE `url` = '/questions/list'; -- name: 题库管理
UPDATE `sys_permission` SET `perms` = '/adm/knowledge/add,/adm/knowledge/edit,/adm/knowledge/setShareKnowledge,/adm/knowledge/getShareList,/adm/category/queryTreeByKeyWord,/adm/industry/listByIds,/adm/knowledge/list,/adm/knowledge/delete,/adm/knowledge/deleteBatch,/adm/knowledge/importExcel,/adm/knowledge/exportXls' WHERE `url` = '/knowledge'; -- name: 知识点
UPDATE `sys_permission` SET `perms` = '/adm/managers/edit,/adm/managers/add,/adm/managers/list,/adm/managers/delete,/adm/managers/deleteBatch,/adm/managers/exportXls,/adm/team/list,/sys/user/queryUserComponentData,/sys/user/getCurrentUserDeparts,/sys/sysDepart/queryDepartTreeSync' WHERE `url` = '/training/managerList'; -- name: 岸基管理人员-船只关系
UPDATE `sys_permission` SET `perms` = '/adm/packages/renew,/adm/packages/delete,/adm/packages/deleteBatch,/adm/packages/pageList' WHERE `url` = '/system/packages/index'; -- name: 企业套餐管理
UPDATE `sys_permission` SET `perms` = '/adm/packagesLog/pageList' WHERE `url` = '/system/packagesLog/index'; -- name: 套餐日志管理
UPDATE `sys_permission` SET `perms` = '/adm/phase/add,/adm/phase/edit,/adm/phaseItem/add,/adm/phaseItem/edit,/adm/phase/setSharePhase,/adm/phase/getShareList,/adm/phase/list,/adm/phase/delete,/adm/phaseItem/list,/adm/phaseItem/delete,/adm/userPhase/pushAllPhase,/adm/category/queryTreeByKeyWord,/sys/position/getNameAndCode,/sys/dict/getDictItems/time_factor,/sys/common/upload' WHERE `url` = '/phase'; -- name: 配置培训阶段
UPDATE `sys_permission` SET `perms` = '/adm/questions/list,/adm/questions/add,/adm/questions/edit,/adm/questions/delete,/adm/questions/deleteBatch,/adm/paper/frozenBatch,/adm/phase/list,/adm/paper/allList,/adm/paper/queryById,/adm/paper/getQRCode,/adm/paper/updateQRCodeStatus,/adm/paper/deleteQRCode,/adm/paper/getShareList,/adm/paper/setSharePaper,/sys/position/getNameAndCode,/adm/paper/edit,/adm/rule/queryall,/adm/paper_question/paperQuestion/list,/adm/paper_question/paperQuestion/addQuestToPaper,/adm/paper_question/paperQuestion/setQuestionGroup,/adm/paper_question/paperQuestion/setQuestionSort,/adm/paper_question/paperQuestion/deleteBatch,/adm/category/queryTreeByKeyWord,/sys/dict/getDictItems/time_factor,/adm/paper/add,/adm/paper/copyPaper,/adm/paper/delete,/sys/dict/getDictItems,/adm/paper/deleteBatch,/adm/paper/list' WHERE `url` = '/paper'; -- name: 试卷库管理
UPDATE `sys_permission` SET `perms` = '/adm/rule/add,/adm/rule/edit,/adm/rule/setShareRule,/adm/rule/getShareList,/adm/rule/list,/adm/rule/delete,/adm/rule/deleteBatch,/adm/rule/importExcel,/adm/industry/listByIds,/adm/rule/exportXls' WHERE `url` = '/rule'; -- name: 评价规则
UPDATE `sys_permission` SET `perms` = '/adm/study/getUnfinishedStudyUser' WHERE `url` = '/unFinishStudy'; -- name: 未完成学习列表
UPDATE `sys_permission` SET `perms` = '/adm/study/list,/adm/study/getToc,/adm/study/editPdf,/adm/study/add,/adm/study/edit,/adm/study/delete,/adm/study/deleteBatch,/adm/study/importExcel,/adm/study/exportXls,/adm/study/updateBatch,/adm/study/queryById,/adm/industry/listByIds,/adm/study/recheckVideo,/adm/study/getShareList,/adm/study/setShareStudy,/sys/dict/getDictItems/time_factor,/sys/position/getNameAndCode,/adm/phase/list,/adm/category/queryTreeByKeyWord,/adm/paper/allList,/adm/study/upload' WHERE `url` = '/study'; -- name: 学习资料
UPDATE `sys_permission` SET `perms` = '/adm/studyFeedback/list,/adm/studyFeedback/delete,/adm/studyFeedback/deleteBatch,/adm/studyFeedback/process' WHERE `url` = '/feedback'; -- name: 反馈记录
UPDATE `sys_permission` SET `perms` = '/adm/studyLog/list,/adm/managers/list,/adm/category/pagelist,/adm/team/list,/adm/studyLog/exportXls' WHERE `url` = '/studyLog'; -- name: 学习记录
UPDATE `sys_permission` SET `perms` = '/adm/studyLog/oneMonthRecord,/adm/studyLog/oneMonthRecordExportXls,/adm/team/list' WHERE `url` = '/monthStudySummary/index'; -- name: 近一个月学习时长汇总
UPDATE `sys_permission` SET `perms` = '/adm/studyLog/querySummaryList,/adm/studyLog/summaryExportXls,/sys/position/getNameAndCode,/adm/managers/list,/adm/category/pagelist,/adm/team/list' WHERE `url` = '/studySummary'; -- name: 学习统计
UPDATE `sys_permission` SET `perms` = '/adm/team/list,/sys/position/getNameAndCode,/adm/category/pagelist,/adm/exam/summaryList' WHERE `url` = '/testSummaryList'; -- name: 练习情况统计
UPDATE `sys_permission` SET `perms` = '/adm/userPaper/listExamWarnList,/adm/userPaper/exportWarnXls,/sys/position/getNameAndCode,/adm/team/list,/adm/category/pagelist,/adm/paper/list' WHERE `url` = '/examTodoList'; -- name: 未考试人员列表
UPDATE `sys_permission` SET `perms` = '/adm/userPhaseItem/add,/adm/userPhaseItem/edit,/adm/phase/list,,/adm/phaseItem/list,/adm/phaseItem/delete,/adm/userPhaseItem/batchAdd,/adm/trainee/listForPhaseItem,/adm/paper/list,/adm/study/list,/adm/category/queryTreeByKeyWord,/adm/userPhaseItem/delete,/adm/userPhase/switchUsersPhase,/adm/userPhaseItem/getUserPhaseItems,/adm/userPhase/getUserPhases,/adm/userPhase/setUserPhase,/adm/userPhase/enable,/adm/trainee/listForPhase,/sys/position/list,/adm/team/list,/sys/position/getNameAndCode,/adm/trainee/listForExam' WHERE `url` = '/userPhase'; -- name: 管理受试人培训阶段
UPDATE `sys_permission` SET `perms` = '/sys/annountCement/add,/sys/annountCement/list,/sys/annountCement/importExcel,/sys/annountCement/exportXls' WHERE `url` = '/system/notice'; -- name: 通知公告
UPDATE `sys_permission` SET `perms` = '/sys/dataLog/list,/sys/dataLog/queryDataVerList,/sys/dataLog/queryCompareList' WHERE `url` = '/monitor/datalog'; -- name: 数据日志
UPDATE `sys_permission` SET `perms` = '/sys/dictItem/list,/sys/dictItem/delete,/sys/dictItem/add,/sys/dictItem/edit,/sys/dict/add,/sys/dict/edit,/sys/dict/deleteList,/sys/dict/back/*,/sys/dict/deletePhysic/*,/sys/dictItem/dictItemCheck,/sys/dict/list,/sys/dict/delete,/sys/dict/deleteBatch,/sys/dict/exportXls,/sys/dict/importExcel,/sys/dict/refleshCache,/sys/dict/queryAllDictItems' WHERE `url` = '/system/dict'; -- name: 数据字典
UPDATE `sys_permission` SET `perms` = '/sys/log/list' WHERE `url` = '/monitor/log'; -- name: 日志管理
UPDATE `sys_permission` SET `perms` = '/sys/permission/list,/sys/permission/edit,/sys/permission/add,/sys/duplicate/check,/sys/duplicate/check/buttonField,/sys/permission/queryPermissionRule,/sys/permission/deletePermissionRule,/sys/permission/addPermissionRule,/sys/permission/editPermissionRule,/sys/permission/delete,/sys/permission/deleteBatch,/sys/dict/getDictItems' WHERE `url` = '/system/menu'; -- name: 菜单管理
UPDATE `sys_permission` SET `perms` = '/sys/position/list,/sys/position/delete,/sys/position/deleteBatch,/sys/position/importExcel,/sys/position/exportXls,/adm/industry/listByIds,/sys/position/add,/sys/position/edit,/sys/position/queryById,/sys/position/setShareSysPosition,/sys/position/getShareList' WHERE `url` = '/system/position'; -- name: 职务管理
UPDATE `sys_permission` SET `perms` = '/sys/role/datarule,/act/process/extActDesignFlowData/getDesFormFlows,/joa/designform/designFormCommuse/getRoleDegisnList,/joa/designform/designFormCommuse/sysRoleDesignAdd,/sys/role/add,/sys/role/edit,/sys/sysRoleIndex/add,/sys/sysRoleIndex/edit,/sys/sysRoleIndex/queryByCode,/sys/role/queryTreeList,/sys/permission/queryRolePermission,/sys/permission/saveRolePermission,/sys/user/userRoleList,/sys/user/deleteUserRole,/sys/user/deleteUserRoleBatch,/sys/user/addSysUserRole,/sys/user/queryUserRole,/sys/role/list,/sys/role/delete,/sys/role/deleteBatch,/sys/role/exportXls,/sys/role/importExcel,/sys/role/checkRoleCode,/sys/position/getNameAndCode,/sys/role/queryall,/sys/user/add,/sys/user/userDepartList,/sys/user/list,/sys/role/datarule/*/*' WHERE `url` = '/system/role'; -- name: 角色管理
UPDATE `sys_permission` SET `perms` = '/sys/sysAnnouncementSend/getMyAnnouncementSend,/sys/sysAnnouncementSend/editByAnntIdAndUserId,/sys/annountCement/syncNotic,/sys/sysAnnouncementSend/readAll' WHERE `url` = '/monitor/mynews'; -- name: 我的消息
UPDATE `sys_permission` SET `perms` = '/sys/sysDepartPermission/datarule,/sys/sysDepart/add,/sys/sysDepart/edit,/sys/sysDepart/deleteBatch,/sys/sysDepart/queryDepartTreeSync,/sys/api/getMemberMode,/sys/sysDepart/getOnLine,/sys/sysDepart/searchBy,/adm/packages/add,/sys/role/queryTreeList,/sys/permission/queryDepartPermission,/sys/permission/saveDepartPermission,/sys/sysDepart/setMemberAmount,/adm/industry/listByIds,/sys/sysDepart/getChildrenNames,/sys/sysDepart/exportXls,/sys/sysDepart/importExcel' WHERE `url` = '/system/depart'; -- name: 部门管理
UPDATE `sys_permission` SET `perms` = '/sys/user/list,/sys/user/delete,/sys/user/deleteBatch,/sys/user/importExcel,/sys/user/exportXls,/sys/user/frozenBatch,/act/process/extActProcess/doSyncUser,/sys/user/changePassword,/sys/sysUserAgent/queryByUserName,/sys/sysUserAgent/edit,/sys/user/userDepartList,/sys/user/queryUserRole,/sys/user/recycleBin,/sys/user/putRecycleBin,/sys/user/deleteRecycleBin,/sys/role/queryall,/sys/tenant/queryList,/sys/position/getNameAndCode' WHERE `url` = '/system/user'; -- name: 用户管理
UPDATE `sys_permission` SET `perms` = 'admin:download' WHERE `perms` = 'admin:download'; -- name: 文件上传后下载
UPDATE `sys_permission` SET `perms` = 'btn:add' WHERE `perms` = 'btn:add'; -- name: btn:add
UPDATE `sys_permission` SET `perms` = 'depart:add,/sys/sysDepart/add' WHERE `perms` = 'depart:add'; -- name: 新增
UPDATE `sys_permission` SET `perms` = 'depart:addChild,/sys/sysDepart/add' WHERE `perms` = 'depart:addChild'; -- name: 添加下级
UPDATE `sys_permission` SET `perms` = 'depart:import,/sys/sysDepart/importExcel' WHERE `perms` = 'depart:import'; -- name: 导入
UPDATE `sys_permission` SET `perms` = 'depart:member,/sys/sysDepart/setMemberAmount' WHERE `perms` = 'depart:member'; -- name: 设置会员费用
UPDATE `sys_permission` SET `perms` = 'depart:packages,/adm/packages/add' WHERE `perms` = 'depart:packages'; -- name: 企业会员模式
UPDATE `sys_permission` SET `perms` = 'dict:add,/sys/dict/add' WHERE `perms` = 'dict:add'; -- name: 新增
UPDATE `sys_permission` SET `perms` = 'dict:config,/sys/dictItem/add' WHERE `perms` = 'dict:config'; -- name: 字典配置
UPDATE `sys_permission` SET `perms` = 'dict:delete,/sys/dict/delete' WHERE `perms` = 'dict:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'dict:deleteBatch,/sys/dict/deleteBatch' WHERE `perms` = 'dict:deleteBatch'; -- name: 批量删除
UPDATE `sys_permission` SET `perms` = 'dict:edit,/sys/dict/edit' WHERE `perms` = 'dict:edit'; -- name: 编辑
UPDATE `sys_permission` SET `perms` = 'dict:import,/sys/dict/importExcel' WHERE `perms` = 'dict:import'; -- name: 导入
UPDATE `sys_permission` SET `perms` = 'dict:rollback,/sys/dict/deleteList' WHERE `perms` = 'dict:rollback'; -- name: 回收站
UPDATE `sys_permission` SET `perms` = 'exam:delete,/adm/exam/delete' WHERE `perms` = 'exam:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'exam:deleteBatch,/adm/exam/deleteBatch' WHERE `perms` = 'exam:deleteBatch'; -- name: 批量删除
UPDATE `sys_permission` SET `perms` = 'exam:deleteBatch-unset-score,/adm/exam/deleteBatch' WHERE `perms` = 'exam:deleteBatch-unset-score'; -- name: 批量删除
UPDATE `sys_permission` SET `perms` = 'exam:export' WHERE `perms` = 'exam:export'; -- name: 导出
UPDATE `sys_permission` SET `perms` = 'exam:invalid,/adm/exam/cancel' WHERE `perms` = 'exam:invalid'; -- name: 作废
UPDATE `sys_permission` SET `perms` = 'exam:refresh,/adm/exam/personSubmit' WHERE `perms` = 'exam:refresh'; -- name: 刷新分数
UPDATE `sys_permission` SET `perms` = 'exam:reset,/adm/exam/reset' WHERE `perms` = 'exam:reset'; -- name: 重置考试
UPDATE `sys_permission` SET `perms` = 'exam:result,/adm/exam/examDetail,/adm/examQuestion/getAll' WHERE `perms` = 'exam:result'; -- name: 考试结果
UPDATE `sys_permission` SET `perms` = 'exam:score,/adm/exam/setScore' WHERE `perms` = 'exam:score'; -- name: 管理人员打分
UPDATE `sys_permission` SET `perms` = 'exam:sunmary-export,/adm/exam/summaryExportXls' WHERE `perms` = 'exam:sunmary-export'; -- name: 导出
UPDATE `sys_permission` SET `perms` = 'examCategory:add,/adm/category/add' WHERE `perms` = 'examCategory:add'; -- name: 新增
UPDATE `sys_permission` SET `perms` = 'examCategory:delete,/adm/category/delete' WHERE `perms` = 'examCategory:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'examCategory:deleteBatch,/adm/category/deleteBatch' WHERE `perms` = 'examCategory:deleteBatch'; -- name: 批量删除
UPDATE `sys_permission` SET `perms` = 'examCategory:edit,/adm/category/edit' WHERE `perms` = 'examCategory:edit'; -- name: 编辑
UPDATE `sys_permission` SET `perms` = 'examUser:onboardStatus' WHERE `perms` = 'examUser:onboardStatus'; -- name: 上船状态
UPDATE `sys_permission` SET `perms` = 'feedback:delete,/adm/studyFeedback/delete' WHERE `perms` = 'feedback:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'feedback:deleteBatch,/adm/studyFeedback/deleteBatch' WHERE `perms` = 'feedback:deleteBatch'; -- name: 批量删除
UPDATE `sys_permission` SET `perms` = 'feedback:handle,/adm/studyFeedback/process' WHERE `perms` = 'feedback:handle'; -- name: 处理反馈
UPDATE `sys_permission` SET `perms` = 'global:share' WHERE `perms` = 'global:share'; -- name: 共享模式
UPDATE `sys_permission` SET `perms` = 'global:share-query,/adm/questions/getShareList,/adm/paper/getShareList,/adm/study/getShareList,/adm/phase/getShareList,/adm/category/getShareList,/adm/knowledge/getShareList,/sys/position/getShareList' WHERE `perms` = 'global:share-query'; -- name: 查询公共数据
UPDATE `sys_permission` SET `perms` = 'home:results' WHERE `perms` = 'home:results'; -- name: 显示结果来源
UPDATE `sys_permission` SET `perms` = 'industry:add,/adm/industry/add' WHERE `perms` = 'industry:add'; -- name: 新增
UPDATE `sys_permission` SET `perms` = 'industry:delete,/adm/industry/delete' WHERE `perms` = 'industry:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'industry:deleteBatch,/adm/industry/deleteBatch' WHERE `perms` = 'industry:deleteBatch'; -- name: 批量删除
UPDATE `sys_permission` SET `perms` = 'industry:edit,/adm/industry/edit' WHERE `perms` = 'industry:edit'; -- name: 编辑
UPDATE `sys_permission` SET `perms` = 'knowledge: mport,/adm/knowledge/importExcel' WHERE `perms` = 'knowledge: mport'; -- name: 导入
UPDATE `sys_permission` SET `perms` = 'knowledge:add,/adm/knowledge/add' WHERE `perms` = 'knowledge:add'; -- name: 新增
UPDATE `sys_permission` SET `perms` = 'knowledge:delete,/adm/knowledge/delete' WHERE `perms` = 'knowledge:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'knowledge:edit,/adm/knowledge/edit' WHERE `perms` = 'knowledge:edit'; -- name: 编辑
UPDATE `sys_permission` SET `perms` = 'managers:add,/adm/managers/add' WHERE `perms` = 'managers:add'; -- name: 新增
UPDATE `sys_permission` SET `perms` = 'managers:delete,/adm/managers/delete' WHERE `perms` = 'managers:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'managers:deleteBatch,/adm/managers/deleteBatch' WHERE `perms` = 'managers:deleteBatch'; -- name: 批量删除
UPDATE `sys_permission` SET `perms` = 'managers:edit,/adm/managers/edit' WHERE `perms` = 'managers:edit'; -- name: 编辑
UPDATE `sys_permission` SET `perms` = 'managers:import,/adm/managers/importExcelForTraining' WHERE `perms` = 'managers:import'; -- name: 导入
UPDATE `sys_permission` SET `perms` = 'menu:add,/sys/permission/add' WHERE `perms` = 'menu:add'; -- name: 新增菜单
UPDATE `sys_permission` SET `perms` = 'menu:addChild,/sys/permission/add' WHERE `perms` = 'menu:addChild'; -- name: 添加下级
UPDATE `sys_permission` SET `perms` = 'menu:dataRule,/sys/permission/queryPermissionRule' WHERE `perms` = 'menu:dataRule'; -- name: 数据规则
UPDATE `sys_permission` SET `perms` = 'menu:delete,/sys/permission/delete' WHERE `perms` = 'menu:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'menu:deleteBatch,/sys/permission/deleteBatch' WHERE `perms` = 'menu:deleteBatch'; -- name: 批量删除
UPDATE `sys_permission` SET `perms` = 'menu:edit,/sys/permission/edit' WHERE `perms` = 'menu:edit'; -- name: 编辑
UPDATE `sys_permission` SET `perms` = 'news:read,/sys/sysAnnouncementSend/readAll' WHERE `perms` = 'news:read'; -- name: 全部标注已读
UPDATE `sys_permission` SET `perms` = 'packages:delete,/adm/packages/delete' WHERE `perms` = 'packages:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'packages:deleteBatch,/adm/packages/deleteBatch' WHERE `perms` = 'packages:deleteBatch'; -- name: 批量删除
UPDATE `sys_permission` SET `perms` = 'packages:renew,/adm/packages/renew' WHERE `perms` = 'packages:renew'; -- name: 续费
UPDATE `sys_permission` SET `perms` = 'paper:add,/adm/paper/add' WHERE `perms` = 'paper:add'; -- name: 新增
UPDATE `sys_permission` SET `perms` = 'paper:config,/adm/paper_question/paperQuestion/list' WHERE `perms` = 'paper:config'; -- name: 题目配置
UPDATE `sys_permission` SET `perms` = 'paper:copy,/adm/paper/copyPaper' WHERE `perms` = 'paper:copy'; -- name: 复制
UPDATE `sys_permission` SET `perms` = 'paper:create-code,/adm/paper/getQRCode' WHERE `perms` = 'paper:create-code'; -- name: 生成小程序码
UPDATE `sys_permission` SET `perms` = 'paper:delete,/adm/paper/delete' WHERE `perms` = 'paper:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'paper:deleteBatch,/adm/paper/deleteBatch' WHERE `perms` = 'paper:deleteBatch'; -- name: 批量删除
UPDATE `sys_permission` SET `perms` = 'paper:disable,/adm/paper/frozenBatch' WHERE `perms` = 'paper:disable'; -- name: 禁用
UPDATE `sys_permission` SET `perms` = 'paper:edit,/adm/paper/edit' WHERE `perms` = 'paper:edit'; -- name: 编辑
UPDATE `sys_permission` SET `perms` = 'paper:enable,/adm/paper/frozenBatch' WHERE `perms` = 'paper:enable'; -- name: 启用
UPDATE `sys_permission` SET `perms` = 'phase:add,/adm/phase/add' WHERE `perms` = 'phase:add'; -- name: 添加数据
UPDATE `sys_permission` SET `perms` = 'phase:delete,/adm/phase/delete' WHERE `perms` = 'phase:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'phase:edit,/adm/userPhase/pushAllPhase,/adm/industry/listByIds' WHERE `perms` = 'phase:edit'; -- name: 编辑阶段
UPDATE `sys_permission` SET `perms` = 'position:add,/sys/position/add' WHERE `perms` = 'position:add'; -- name: 新增
UPDATE `sys_permission` SET `perms` = 'position:delete,/sys/position/delete' WHERE `perms` = 'position:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'position:deleteBatch,/sys/position/deleteBatch' WHERE `perms` = 'position:deleteBatch'; -- name: 批量删除
UPDATE `sys_permission` SET `perms` = 'position:edit,/sys/position/edit' WHERE `perms` = 'position:edit'; -- name: 编辑
UPDATE `sys_permission` SET `perms` = 'position:import,/sys/position/importExcel' WHERE `perms` = 'position:import'; -- name: 导入
UPDATE `sys_permission` SET `perms` = 'question:add,/adm/questions/add,/sys/common/upload' WHERE `perms` = 'question:add'; -- name: 题目添加页面
UPDATE `sys_permission` SET `perms` = 'question:delete,/adm/questions/delete,/adm/questions/list' WHERE `perms` = 'question:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'question:deleteBatch,/adm/questions/queryById,/adm/questions/deleteBatch,/adm/questions/list' WHERE `perms` = 'question:deleteBatch'; -- name: 批量删除
UPDATE `sys_permission` SET `perms` = 'question:edit,/adm/questions/edit,/sys/common/upload' WHERE `perms` = 'question:edit'; -- name: 题目编辑页面
UPDATE `sys_permission` SET `perms` = 'resources:update,/adm/systemResource/upload' WHERE `perms` = 'resources:update'; -- name: 更新
UPDATE `sys_permission` SET `perms` = 'role:add,/sys/role/add' WHERE `perms` = 'role:add'; -- name: 新增
UPDATE `sys_permission` SET `perms` = 'role:auth,/sys/role/queryTreeList' WHERE `perms` = 'role:auth'; -- name: 授权
UPDATE `sys_permission` SET `perms` = 'role:delete,/sys/role/delete' WHERE `perms` = 'role:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'role:edit,/sys/role/edit' WHERE `perms` = 'role:edit'; -- name: 编辑
UPDATE `sys_permission` SET `perms` = 'role:import,/adm/managers/importExcelForTraining' WHERE `perms` = 'role:import'; -- name: 导入
UPDATE `sys_permission` SET `perms` = 'role:user-config,/sys/user/userRoleList' WHERE `perms` = 'role:user-config'; -- name: 用户
UPDATE `sys_permission` SET `perms` = 'rule:add,/adm/rule/add' WHERE `perms` = 'rule:add'; -- name: 新增
UPDATE `sys_permission` SET `perms` = 'rule:delete,/adm/rule/delete' WHERE `perms` = 'rule:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'rule:deleteBatch,/adm/rule/deleteBatch' WHERE `perms` = 'rule:deleteBatch'; -- name: 批量删除
UPDATE `sys_permission` SET `perms` = 'rule:edit,/adm/rule/edit' WHERE `perms` = 'rule:edit'; -- name: 编辑
UPDATE `sys_permission` SET `perms` = 'study:add,/adm/study/add' WHERE `perms` = 'study:add'; -- name: 新增
UPDATE `sys_permission` SET `perms` = 'study:category-config,/adm/study/getToc' WHERE `perms` = 'study:category-config'; -- name: 配置目录
UPDATE `sys_permission` SET `perms` = 'study:delete,/adm/study/delete' WHERE `perms` = 'study:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'study:deleteBatch,/adm/study/deleteBatch' WHERE `perms` = 'study:deleteBatch'; -- name: 批量删除
UPDATE `sys_permission` SET `perms` = 'study:edit,/adm/study/edit' WHERE `perms` = 'study:edit'; -- name: 编辑
UPDATE `sys_permission` SET `perms` = 'study:editBatch,/adm/study/updateBatch' WHERE `perms` = 'study:editBatch'; -- name: 批量修改
UPDATE `sys_permission` SET `perms` = 'study:test-result' WHERE `perms` = 'study:test-result'; -- name: 检测结果
UPDATE `sys_permission` SET `perms` = 'test:test' WHERE `perms` = 'test:test'; -- name: 测试
UPDATE `sys_permission` SET `perms` = 'transfer:manager,/sys/transfer/deleteBatch,/sys/transfer/resetSyncState' WHERE `perms` = 'transfer:manager'; -- name: 中转服务配置修改
UPDATE `sys_permission` SET `perms` = 'user:add,/sys/user/add,/sys/user/edit' WHERE `perms` = 'user:add'; -- name: 新增用户
UPDATE `sys_permission` SET `perms` = 'user:addExamUser,/adm/trainee/subjectEdit' WHERE `perms` = 'user:addExamUser'; -- name: 新增
UPDATE `sys_permission` SET `perms` = 'user:delete,/sys/user/delete' WHERE `perms` = 'user:delete'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'user:deleteBatch,/sys/user/deleteBatch' WHERE `perms` = 'user:deleteBatch'; -- name: 批量删除
UPDATE `sys_permission` SET `perms` = 'user:deleteExamUser,/adm/trainee/delete' WHERE `perms` = 'user:deleteExamUser'; -- name: 删除
UPDATE `sys_permission` SET `perms` = 'user:deleteExamUserBatch,/adm/trainee/deleteBatch' WHERE `perms` = 'user:deleteExamUserBatch'; -- name: 批量删除
UPDATE `sys_permission` SET `perms` = 'user:edit' WHERE `perms` = 'user:edit'; -- name: 用户编辑
UPDATE `sys_permission` SET `perms` = 'user:editExamUser,/adm/trainee/subjectEdit' WHERE `perms` = 'user:editExamUser'; -- name: 编辑
UPDATE `sys_permission` SET `perms` = 'user:export,/adm/trainee/examExportXls' WHERE `perms` = 'user:export'; -- name: 导出
UPDATE `sys_permission` SET `perms` = 'user:form:phone' WHERE `perms` = 'user:form:phone'; -- name: 手机号禁用
UPDATE `sys_permission` SET `perms` = 'user:freeze,/sys/user/frozenBatch' WHERE `perms` = 'user:freeze'; -- name: 冻结
UPDATE `sys_permission` SET `perms` = 'user:import,/adm/trainee/importExcelForAll' WHERE `perms` = 'user:import'; -- name: 导入受试人
UPDATE `sys_permission` SET `perms` = 'user:import-one,/adm/trainee/importExcelForDepart' WHERE `perms` = 'user:import-one'; -- name: 单船导入
UPDATE `sys_permission` SET `perms` = 'user:rollback,/sys/user/recycleBin,/sys/user/putRecycleBin,/sys/user/deleteRecycleBin' WHERE `perms` = 'user:rollback'; -- name: 回收站
UPDATE `sys_permission` SET `perms` = 'user:send-paper,/adm/userPaper/list,/adm/userPaper/updateStatus' WHERE `perms` = 'user:send-paper'; -- name: 管理考试列表
UPDATE `sys_permission` SET `perms` = 'user:updatePass,/sys/user/changePassword' WHERE `perms` = 'user:updatePass'; -- name: 密码
UPDATE `sys_permission` SET `perms` = 'user:updatePwd,/adm/trainee/changePassword' WHERE `perms` = 'user:updatePwd'; -- name: 修改密码
UPDATE `sys_permission` SET `perms` = 'userPhase:level,/adm/userPhase/switchUsersPhase,/adm/trainee/listForPhase' WHERE `perms` = 'userPhase:level'; -- name: 批量切换阶段
UPDATE `sys_permission` SET `perms` = 'userPhase:manager,/adm/userPhase/getUserPhases,/adm/userPhaseItem/getUserPhaseItems' WHERE `perms` = 'userPhase:manager'; -- name: 管理培训阶段
UPDATE `sys_permission` SET `perms` = 'userPhase:push,/adm/trainee/listForPhaseItem' WHERE `perms` = 'userPhase:push'; -- name: 批量推送数据
UPDATE `sys_permission` SET `perms` = 'video:recheck,/adm/study/recheckVideo' WHERE `perms` = 'video:recheck'; -- name: 重新处理视频

