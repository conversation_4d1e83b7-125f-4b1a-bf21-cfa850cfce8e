#查询全部船员列表
SELECT su.realname,
       sd.depart_name,
       su.post,
       su.onboard_date,
       su.status,
       su.phone,
       su.identity_card,
       su.work_no
FROM sys_user su
         LEFT JOIN
     sys_depart sd ON su.org_code = sd.org_code
WHERE su.del_flag = 0
  AND EXISTS(SELECT 1
             FROM sys_user_role sur
                      LEFT JOIN
                  sys_role sr ON sur.role_id = sr.id
             WHERE sur.user_id = su.id
               AND sr.role_code = 'exam');


SELECT up.id,
       realname,
       onboard_date,

       up.paper_name AS paper_name,
       up.warn_time,
       p.id          AS paper_id,
       c.name        AS category_name,
       up.board_time AS paper_time
FROM paper p
         INNER JOIN user_paper up ON p.id = up.paper_id
         INNER JOIN sys_user su ON up.user_id = su.id
         INNER JOIN sys_depart sd ON up.org_code = sd.org_code
         INNER JOIN category c ON p.category_id = c.id
WHERE up.del_flag = 0
  AND su.del_flag = 0
  AND su.status = 1
  AND p.del_flag = 0
  AND p.disabled = FALSE
  AND up.status = 1;

AND datediff(su.onboard_date,up.warn_time);



SELECT s.id,
       #{userId} AS user_id,
       p.id   AS phase_id,
       s.category_id,
       s.name,
       0      AS mode,
       2      AS type,
       0      AS action,
       s.id   AS data_id,
       s.type AS data_type,
       s.create_time,
       sln.num
FROM study s
         LEFT JOIN category c ON s.category_id = c.id
         LEFT JOIN phase p ON p.level = c.phase_level
         LEFT JOIN study_log_num sln ON sln.study_id = s.id
WHERE s.del_flag = 0
  AND s.disabled = FALSE
  AND p.id = 2;

-- 2023年热门学习资料TOP15
SELECT s.name, c.name, ss.c AS `count`
FROM study s
         LEFT JOIN category c ON c.id = s.category_id
         LEFT JOIN
     (SELECT study_id, COUNT(create_by) AS c
      FROM (SELECT DISTINCT study_id, create_by
            FROM study_log
            WHERE create_time BETWEEN '2023-01-01 00:00:00' AND '2024-01-01 00:00:00') AS sss
      GROUP BY study_id) ss ON s.id = ss.study_id
ORDER BY ss.c DESC
LIMIT 15;


-- 2024年1月学员学习时长 学员姓名	身份证号	船舶名称	当月已学学时
SELECT su.id, su.realname, su.identity_card, sd.depart_name, IF(ss.c IS NULL, 0, ss.c) AS `count`
FROM sys_user su
         LEFT JOIN sys_depart sd ON su.org_code = sd.org_code
         LEFT JOIN
     (SELECT user_id, SUM(use_time) / 60 AS c
      FROM study_log
      WHERE create_time BETWEEN '2024-01-01 00:00:00' AND '2024-02-01 00:00:00'
      GROUP BY user_id) ss ON su.id = ss.user_id
WHERE identity_card IS NOT NULL
ORDER BY ss.c DESC;


DELETE
FROM paper
WHERE category_id IN (SELECT id FROM category WHERE LENGTH(category.dep_route) > 33 AND category.share_id IS NULL);


DELETE
FROM questions
WHERE category_id IN (SELECT id FROM category WHERE LENGTH(category.dep_route) > 33 AND category.share_id IS NULL);


DELETE
FROM study
WHERE category_id IN (SELECT id FROM category WHERE LENGTH(category.dep_route) > 33 AND category.share_id IS NULL);

DELETE
FROM category
WHERE LENGTH(category.dep_route) > 33
  AND category.share_id IS NULL;


DELETE
FROM paper
WHERE LENGTH(paper.dep_route) > 33
  AND share_id IS NOT NULL;



DELETE
FROM questions
WHERE LENGTH(questions.dep_route) > 33
  AND share_id IS NOT NULL;

DELETE
FROM paper_question
WHERE NOT EXISTS(SELECT 1 FROM paper WHERE id = paper_question.paper_id);


SELECT s.id,
       s.category_id,
       s.name,
       s.type,
       s.description,
       s.paper_id,
       s.time_factor,
       s.post_factor,
       s.size,
       s.need_download,
       s.group_factor,
       s.total_time,
       s.disabled,
       s.update_time,
       s.stream_url,
       IF(sus1.id IS NULL, FALSE, TRUE)                                                      AS is_read,
       IF(sus2.id IS NULL, IF(sus3.id IS NULL, 0, sus3.state), sus2.state)                   AS is_attention,
       sln.num                                                                               AS done_times,
       s.del_flag,
       s.create_by,
       s.create_time,
       c.name                                                                                   category_name,
       p.name                                                                                   paper_name,
       IF(? REGEXP CONCAT(',', REPLACE(s.need_read, ',', ',|,'), ','), 'true', 'false')      AS need_read,
       IF(? REGEXP CONCAT(',', REPLACE(s.need_attention, ',', ',|,'), ','), 'true', 'false') AS need_attention,
       c.app_menu,
       s.dep_route
FROM study s
         LEFT JOIN category c ON s.category_id = c.id
         LEFT JOIN paper p ON p.id = s.paper_id
         LEFT JOIN study_log_num sln ON sln.study_id = s.id AND sln.user_id = ?
         LEFT JOIN study_user_state sus1 ON sus1.study_id = s.id AND sus1.user_id = ? AND sus1.state = 1
         LEFT JOIN study_user_state sus2 ON sus2.study_id = s.id AND sus2.user_id = ? AND sus2.state = 2
         LEFT JOIN study_user_state sus3 ON sus3.study_id = s.id AND sus3.user_id = ? AND sus3.state = 3
WHERE s.del_flag = 0

  AND s.category_id = ?


  AND s.disabled = FALSE
  AND (
    (
        (s.time_factor IS NULL OR s.time_factor = '' OR s.time_factor = '0' OR s.time_factor = '任意' OR
         FIND_IN_SET(?, s.time_factor))
            AND
        (s.post_factor IS NULL OR s.post_factor = '' OR s.post_factor = '0' OR s.post_factor = '任意' OR
         ? REGEXP CONCAT(',', REPLACE(s.post_factor, ',', ',|,'), ',')
            )
        )
    )
  AND (s.dep_route = ?)
ORDER BY s.create_time DESC;

TRUNCATE user_phase;
TRUNCATE user_phase_item;

SELECT s.id,
       s.category_id,
       s.name,
       s.type,
       s.description,
       s.paper_id,
       s.time_factor,
       s.post_factor,
       s.size,
       s.need_download,
       s.group_factor,
       s.total_time,
       s.disabled,
       s.update_time,
       s.stream_url,
       IF(sus1.id IS NULL, FALSE, TRUE)                                                               AS is_read,
       IF(sus2.id IS NULL, IF(sus3.id IS NULL, 0, sus3.state), sus2.state)                            AS is_attention,
       sln.num                                                                                        AS done_times,
       s.del_flag,
       s.create_by,
       s.create_time,
       c.name                                                                                            category_name,
       p.name                                                                                            paper_name,
       IF(',大管轮,' REGEXP CONCAT(',', REPLACE(s.need_read, ',', ',|,'), ','), 'true', 'false')      AS need_read,
       IF(',大管轮,' REGEXP CONCAT(',', REPLACE(s.need_attention, ',', ',|,'), ','), 'true', 'false') AS need_attention,
       c.app_menu,
       s.dep_route
FROM study s
         LEFT JOIN category c ON s.category_id = c.id
         LEFT JOIN paper p ON p.id = s.paper_id
         LEFT JOIN study_log_num sln ON sln.study_id = s.id AND sln.user_id = '1801272201790398465'
         LEFT JOIN study_user_state sus1
                   ON sus1.study_id = s.id AND sus1.user_id = '1801272201790398465' AND sus1.state = 1
         LEFT JOIN study_user_state sus2
                   ON sus2.study_id = s.id AND sus2.user_id = '1801272201790398465' AND sus2.state = 2
         LEFT JOIN study_user_state sus3
                   ON sus3.study_id = s.id AND sus3.user_id = '1801272201790398465' AND sus3.state = 3
WHERE s.del_flag = 0
  AND s.category_id = 1894
  AND s.disabled = FALSE
  AND (
    (
        (s.time_factor IS NULL OR s.time_factor = '' OR s.time_factor = '0' OR s.time_factor = '任意' OR
         FIND_IN_SET('上船后2个月', s.time_factor))
            AND
        (s.post_factor IS NULL OR s.post_factor = '' OR s.post_factor = '0' OR s.post_factor = '任意' OR
         ',大管轮,' REGEXP CONCAT(',', REPLACE(s.post_factor, ',', ',|,'), ',')
            )
        )
    )
  AND s.dep_route = '/79b24dc8981b4c159d6ab1bf166741c0/e8b43e9beafe49c59a6854ab8e0ac057'
ORDER BY s.create_time DESC;

UPDATE phase
SET share_mode=1
WHERE LENGTH(dep_route) <= 33;


UPDATE phase_item pi,phase ph , paper p
SET pi.category_id =p.category_id
WHERE pi.phase_id = ph.id
  AND pi.type = 1
  AND ph.dep_route = p.dep_route
  AND pi.data_id = p.id
  AND p.category_id = c.id;


SELECT upi.id,
       upi.user_id,
       upi.phase_item_id,
       upi.status,
       upi.create_time,
       pi.phase_id,
       pi.category_id,
       pi.for_user_id,
       upi.done,
       upi.total,
       pi.name,
       pi.mode,
       pi.type,
       pi.seq,
       pi.action,
       pi.data_id,
       pi.data_type,
       pi.parent_id,
       pi.create_time,
       pi.create_by,
       pi.update_time,
       pi.time_factor,
       pi.post_factor,
       pi.group_factor,
       pi.disabled,
       pi.update_by
FROM user_phase_item upi
         LEFT JOIN phase_item pi ON pi.id = upi.phase_item_id
WHERE upi.user_id = '1802508370633228289'
  AND pi.parent_id = '0'
  AND upi.phase_id = 40
  AND (${showAll} OR pi.for_user_id IS NULL OR pi.for_user_id = '')
  AND pi.del_flag = 0
ORDER BY pi.seq;

-- DISTINCT

TRUNCATE user_phase;
TRUNCATE user_phase_item;
TRUNCATE sync_state;

SELECT id, name, category_id, LENGTH(dep_route)
FROM study
WHERE share_mode = 0
  AND NOT EXISTS(SELECT 1 FROM category c WHERE c.id = study.category_id);

SELECT id, name, category_id, LENGTH(dep_route)
FROM paper
WHERE share_mode = 0
  AND NOT EXISTS(SELECT 1 FROM category c WHERE c.id = paper.category_id);


SELECT id, name, category_id, LENGTH(dep_route)
FROM study
WHERE share_mode = 0
  AND del_flag = 0
  AND NOT EXISTS(SELECT 1 FROM category c WHERE c.id = study.category_id AND c.del_flag = 0);

SELECT id, name, category_id, LENGTH(dep_route)
FROM paper
WHERE share_mode = 0
  AND del_flag = 0
  AND NOT EXISTS(SELECT 1 FROM category c WHERE c.id = paper.category_id AND c.del_flag = 0);

UPDATE study
SET study.del_flag=1
WHERE share_mode = 0
  AND NOT EXISTS(SELECT 1 FROM category c WHERE c.id = study.category_id AND c.del_flag = 0);


UPDATE study
SET study.share_mode=0
WHERE share_mode = 1
  AND NOT EXISTS(SELECT 1 FROM category c WHERE c.id = study.category_id AND c.del_flag = 0);

-- 查询删除重复数据

SELECT *
FROM (SELECT phase_id, category_id, parent_id, data_type, data_id, COUNT(id) AS c, MIN(id) AS id
      FROM phase_item
      GROUP BY phase_id, category_id, parent_id, data_type, data_id) a;


SELECT *
FROM (SELECT ph.dep_route,
             ph.share_id,
             phase_id,
             category_id,
             parent_id,
             data_type,
             data_id,
             COUNT(pi.id) AS c,
             MIN(pi.id)   AS id
      FROM phase_item pi
               LEFT JOIN phase ph ON pi.phase_id = ph.id
      WHERE ph.del_flag = 0
        AND pi.del_flag = 0
      GROUP BY ph.dep_route, ph.share_id, phase_id, category_id, parent_id, data_type, data_id) a
WHERE c > 1;


DELETE
FROM phase_item
WHERE id NOT IN (SELECT id
                 FROM (SELECT MIN(pi.id) AS id
                       FROM phase_item pi
                                LEFT JOIN phase ph ON pi.phase_id = ph.id
                       WHERE ph.del_flag = 0
                         AND pi.del_flag = 0
                       GROUP BY ph.dep_route, ph.share_id, phase_id, category_id, parent_id, data_type, data_id) a);


SELECT *
FROM (SELECT user_id, phase_id, phase_item_id, COUNT(id) AS c, MIN(id) AS id
      FROM user_phase_item
      GROUP BY user_id, phase_id, phase_item_id) a
WHERE c > 1;

DELETE
FROM user_phase_item
WHERE id NOT IN (SELECT id
                 FROM (SELECT MIN(id) AS id
                       FROM user_phase_item
                       GROUP BY user_id, phase_id, phase_item_id) a);

SELECT *
FROM (SELECT sd.route, user_id, phase_id, COUNT(up.id) AS c, MIN(up.id) AS id
      FROM user_phase up
               LEFT JOIN sys_trainee_depart std ON up.user_id = std.trainee_id
               LEFT JOIN sys_depart sd ON std.dep_id = sd.id
      GROUP BY sd.route, user_id, phase_id) a
WHERE c > 1;

SELECT *
FROM (SELECT user_id, phase_id, COUNT(up.id) AS c, MIN(up.id) AS id
      FROM user_phase up
      GROUP BY user_id, phase_id) a
WHERE c > 1;

SELECT *
FROM trainee te
         LEFT OUTER JOIN sys_trainee_depart sd ON te.id = sd.trainee_id
         LEFT JOIN sys_depart sd2 ON sd.dep_id = sd2.id
WHERE sd2.id = 'e5e941a5c0084ceca41677acda7aed76';


DELETE
FROM sys_trainee_depart
WHERE dep_id = 'e5e941a5c0084ceca41677acda7aed76';


SELECT *
FROM (SELECT up.user_id, up.id upid, std.id stdid, up.dep_route, sd.route
      FROM user_phase up
               LEFT JOIN sys_trainee_depart std ON up.user_id = std.trainee_id
               LEFT JOIN sys_depart sd ON std.dep_id = sd.id) a
WHERE stdid IS NULL;

DELETE
FROM user_phase
WHERE user_id = '1821071340925489153'
  AND dep_route <> '/e5e941a5c0084ceca41677acda7aed76/01b45ca4e49c4534b3c5ab35d3ca9d12';

DELETE
FROM user_phase
WHERE user_id = '1826182879118491650'
  AND dep_route <> '/e5e941a5c0084ceca41677acda7aed76/01b45ca4e49c4534b3c5ab35d3ca9d12';


DELETE
FROM user_phase
WHERE user_id = '1826182879118491650';

DELETE
FROM user_phase_item
WHERE user_id = '1826182879118491650';

-- ====================================清理数据==================================================================

DELETE
FROM category
WHERE LENGTH(dep_route) > 33
   OR del_flag = 1;

DELETE
FROM knowledge
WHERE LENGTH(dep_route) > 33
   OR del_flag = 1;

DELETE
FROM rule
WHERE LENGTH(dep_route) > 33
   OR del_flag = 1;

DELETE
FROM sys_position
WHERE LENGTH(dep_route) > 33;

DELETE
FROM paper
WHERE LENGTH(dep_route) > 33
   OR del_flag = 1;

DELETE
FROM study
WHERE LENGTH(dep_route) > 33
   OR del_flag = 1;

DELETE
FROM phase
WHERE LENGTH(dep_route) > 33
   OR del_flag = 1;

DELETE
FROM questions
WHERE LENGTH(dep_route) > 33
   OR del_flag = 1;

DELETE
FROM paper_question
WHERE paper_id NOT IN (SELECT id FROM paper);

DELETE
FROM phase_item
WHERE phase_id NOT IN (SELECT id FROM phase);

-- 反复执行，直到删除数量为0
DELETE
FROM phase_item
WHERE parent_id IS NOT NULL
  AND parent_id <> 0
  AND parent_id NOT IN (SELECT id FROM (SELECT id FROM phase_item) a);

-- 反复执行，直到删除数量为0
DELETE
FROM category
WHERE parent_id IS NOT NULL
  AND parent_id <> 0
  AND parent_id NOT IN (SELECT id FROM (SELECT id FROM category) a);

UPDATE category
SET share_mode=1
WHERE LENGTH(dep_route) = 33;

UPDATE knowledge
SET share_mode=1
WHERE LENGTH(dep_route) = 33;

UPDATE rule
SET share_mode=1
WHERE LENGTH(dep_route) = 33;

UPDATE sys_position
SET share_mode=1
WHERE LENGTH(dep_route) = 33;

UPDATE paper
SET share_mode=1
WHERE LENGTH(dep_route) = 33;

UPDATE study
SET share_mode=1
WHERE LENGTH(dep_route) = 33;

UPDATE phase
SET share_mode=1
WHERE LENGTH(dep_route) = 33;

UPDATE questions
SET share_mode=1
WHERE LENGTH(dep_route) = 33;

TRUNCATE exam;
TRUNCATE exam_question;
TRUNCATE user_paper;

TRUNCATE study_log;
TRUNCATE study_log_num;
TRUNCATE study_statistics;
TRUNCATE study_user_state;
TRUNCATE study_time_user;
TRUNCATE study_time_department;
TRUNCATE study_time_fleet;
TRUNCATE study_time_per_department;
TRUNCATE study_time_per_fleet;

TRUNCATE user_phase;
TRUNCATE user_phase_item;

TRUNCATE sync_state;


SELECT pi1.id,
       pi1.name,
       pi1.data_type,
       pi1.data_id,
       pi2.id        AS id2,
       pi2.name      AS name2,
       pi2.data_type AS data_type2,
       pi2.data_id   AS data_id2
FROM (SELECT pi.*, ph.share_id AS ph_id
      FROM phase_item pi
               LEFT JOIN phase ph ON pi.phase_id = ph.id
      WHERE ph.del_flag = 0
        AND pi.del_flag = 0
        AND ph.share_id IS NOT NULL) pi1
         LEFT JOIN
     (SELECT pi.*, ph.id AS ph_id
      FROM phase_item pi
               LEFT JOIN phase ph ON pi.phase_id = ph.id
      WHERE ph.share_id IS NULL) pi2
     ON pi1.ph_id = pi2.ph_id AND pi1.data_type = pi2.data_type AND pi1.name = pi2.name

LIMIT 10;

UPDATE phase_item pi LEFT JOIN (SELECT pi1.id,
                                       pi1.name,
                                       pi1.data_type,
                                       pi1.data_id,
                                       pi2.id        AS id2,
                                       pi2.name      AS name2,
                                       pi2.data_type AS data_type2,
                                       pi2.data_id   AS data_id2
                                FROM (SELECT pi.*, ph.share_id AS ph_id
                                      FROM phase_item pi
                                               LEFT JOIN phase ph ON pi.phase_id = ph.id
                                      WHERE ph.del_flag = 0
                                        AND pi.del_flag = 0
                                        AND ph.share_id IS NOT NULL) pi1
                                         LEFT JOIN
                                     (SELECT pi.*, ph.id AS ph_id
                                      FROM phase_item pi
                                               LEFT JOIN phase ph ON pi.phase_id = ph.id
                                      WHERE ph.share_id IS NULL) pi2
                                     ON pi1.ph_id = pi2.ph_id AND pi1.data_type = pi2.data_type AND
                                        pi1.name = pi2.name) t ON pi.id = t.id
SET pi.share_id = t.id2
WHERE t.id2 IS NOT NULL;


DELETE
FROM user_phase
WHERE id NOT IN (SELECT id
                 FROM (SELECT MIN(id) AS id, dep_route, user_id, phase_id
                       FROM user_phase
                       GROUP BY dep_route, user_id, phase_id) a);


DELETE
FROM user_phase
WHERE id NOT IN (SELECT id FROM (SELECT MIN(id) AS id FROM user_phase GROUP BY dep_route, user_id, phase_id) a);


-- 修复就数据的 user_paper_id
UPDATE exam e
set e.user_paper_id = (SELECT up.id
                       FROM user_paper up
                       WHERE up.user_id = e.user_id
                         AND up.paper_id = e.paper_id
                         and up.del_flag = 0
                       order by up.create_time desc
                       limit 1)
where user_paper_id IS NULL
  and e.del_flag = 0;

-- 无法修复的则删除。(不能执行)
# UPDATE exam e
# set e.del_flag=1
# where user_paper_id IS NULL and e.del_flag=0;
select count(1)
from exam e
where user_paper_id IS NULL
  and e.del_flag = 0;

TRUNCATE category_sync;
TRUNCATE exam_sync;
TRUNCATE mmpi_sync;
TRUNCATE paper_sync;
TRUNCATE paper_question_sync;
TRUNCATE phase_sync;
TRUNCATE phase_item_sync;
TRUNCATE questions_sync;
TRUNCATE rule_sync;
TRUNCATE study_log_sync;
TRUNCATE study_sync;
TRUNCATE sys_depart_sync;
TRUNCATE sys_trainee_depart_sync;
TRUNCATE sys_user_depart_sync;
TRUNCATE sys_position_sync;
TRUNCATE sys_user_sync;
TRUNCATE sys_user_role_sync;
TRUNCATE trainee_sync;
TRUNCATE user_paper_sync;
TRUNCATE user_phase_item_sync;
TRUNCATE user_phase_sync;
TRUNCATE packages_sync;

-- 找出尚未扣除数量的船员和套餐
select t.id as trainee_id, sd.id depart_id, p.id as package_id
from trainee t
         inner join sys_trainee_depart td on t.id = td.trainee_id
         inner join sys_depart sd on td.dep_id = sd.id
         inner join packages p on sd.id = p.sys_depart_id
where td.packages_id is null
  and sd.member_type = 1;

-- 关联船员套餐ID
update trainee t inner join sys_trainee_depart td on t.id = td.trainee_id inner join sys_depart sd on td.dep_id = sd.id inner join packages p on sd.id = p.sys_depart_id
set td.packages_id = p.id
where td.packages_id is null
  and sd.member_type = 1;

-- 仅 SAAS
update packages set surplus_num = member_num - 42 where id = 21;
