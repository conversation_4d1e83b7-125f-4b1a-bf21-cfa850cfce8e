server:
  #微服务端口
  port: 7001
spring:
  application:
    name: jeecg-system
#  cloud:
#    #Sentinel持久化配置
#    sentinel:
#      transport:
#        dashboard: jeecg-boot-sentinel:9000
#      datasource:
#        #流控规则
#        flow: # 指定数据源名称
#          # 指定nacos数据源
#          nacos:
#            server-addr: @config.server-addr@
#            # 指定配置文件
#            dataId: ${spring.application.name}-flow-rules
#            # 指定分组
#            groupId: SENTINEL_GROUP
#            # 指定配置文件规则类型
#            rule-type: flow
#            # 指定配置文件数据格式
#            data-type: json
#        #降级规则
#        degrade:
#          nacos:
#            server-addr: @config.server-addr@
#            dataId: ${spring.application.name}-degrade-rules
#            groupId: SENTINEL_GROUP
#            rule-type: degrade
#            data-type: json
#        #系统规则
#        system:
#          nacos:
#            server-addr: @config.server-addr@
#            dataId: ${spring.application.name}-system-rules
#            groupId: SENTINEL_GROUP
#            rule-type: system
#            data-type: json
#        #授权规则
#        authority:
#          nacos:
#            server-addr: @config.server-addr@
#            dataId: ${spring.application.name}-authority-rules
#            groupId: SENTINEL_GROUP
#            rule-type: authority
#            data-type: json
#        #热点参数
#        param-flow:
#          nacos:
#            server-addr: @config.server-addr@
#            dataId: ${spring.application.name}-param-rules
#            groupId: SENTINEL_GROUP
#            rule-type: param-flow
#            data-type: json